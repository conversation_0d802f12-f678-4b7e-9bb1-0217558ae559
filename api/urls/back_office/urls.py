from django.urls import path

from api.views.external_api.jobs.details import jobDetailsDetails
from api.views.external_api.jobs.list import get_jobs_data
from api.views.external_api.jobs.new import newJobAPI
from api.views.external_api.jobs.update import updateJobAPI

urlpatterns = [
    path("", get_jobs_data, name="get_jobs_data"),
    path("new/", newJobAP<PERSON>, name="newJobAPI"),
    path("<str:job_id>/", jobDetailsDetails, name="updateJobAPI"),
    path("<str:job_id>/update/", updateJobAPI, name="update<PERSON>ob<PERSON><PERSON>"),
]
