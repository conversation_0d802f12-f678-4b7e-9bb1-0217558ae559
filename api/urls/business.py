from django.urls import path
from api.views.applications import (
    get_business_application_details,
    update_business_application,
)
from api.views.interviews import get_business_interviews
from api.views.jobs import delete_job_api
from businesses.views.billing.invoices import (
    business_invoice_details_api_view,
    business_invoices_api_view,
    get_invoice_pdf,
)
from businesses.views.jobs import *

from businesses.views.registration import *
from businesses.views.subscribpions import business_subscription, business_subscriptions
from candidates.views.applications import *
from candidates.views.candidates import *
from candidates.views.data.dashboard import *
from candidates.views.data.profile import *
from api.views.business import *
from businesses.views.data.dashboard import *

urlpatterns = [
    path("", get_all_businesses, name="get_all_businesses"),
    path(
        "new/",
        new_business,
        name="new_business",
    ),
    path(
        "<str:business_slug>/update/",
        update_business,
        name="update_business",
    ),
    path(
        "<str:business_slug>/",
        details_business,
        name="details_business",
    ),
    path(
        "<str:business_slug>/status/",
        get_profile_status,
        name="business_status",
    ),
    path(
        "<str:business_slug>/delete/",
        delete_business,
        name="delete_business",
    ),
    path(
        "<str:business_slug>/dashboard/",
        business_dashboard_data,
        name="business dashboard data",
    ),
    path(
        "<str:business_slug>/industries/",
        business_industries,
        name="business_industries",
    ),
    path(
        "<str:business_slug>/jobs/",
        business_jobs,
        name="business_jobs",
    ),
    path(
        "<str:business_slug>/jobs/<str:job_slug>/delete/",
        delete_job_api,
        name="delete_job_api",
    ),
    path(
        "<business_slug>/jobs/<str:job_id>/applications/",
        get_business_applications,
        name="job_applications",
    ),
    path(
        "<business_slug>/applications/",
        get_all_business_applications,
        name="get_all_business_applications",
    ),
    path(
        "<str:business_slug>/applications/<str:application_id>/",
        get_business_application_details,
        name="job_applications",
    ),
    path(
        "<str:business_slug>/applications/<str:application_id>/update/",
        update_business_application,
        name="update_business_application",
    ),
    path(
        "<str:business_id>/jobs/<str:job_slug>/",
        business_single_job,
        name="business_single_job",
    ),
    path(
        "<str:business_slug>/services/",
        business_services_list,
        name="business_services_list",
    ),
    path(
        "update/<str:slug>/",
        business_services,
        name="new_business",
    ),
    path(
        "<str:business_slug>/applications/<str:application_id>/update/",
        update_application,
        name="update_application",
    ),
    path(
        "<str:business_slug>/profile/",
        business_profile,
        name="business_profile",
    ),
    path(
        "<str:business_slug>/employees/",
        company_profile_employees,
        name="company_profile_employees",
    ),
    # interviews
    path(
        "<str:business_slug>/interviews/",
        get_business_interviews,
        name="business_interviews",
    ),
    # candidate data
    path(
        "<str:business_slug>/candidates/<str:candidate_id>/",
        get_business_candidate,
        name="get_business_candidate",
    ),
    # candidates
    path(
        "candidates/complete-profile/",
        complete_profile,
        name="complete_profile",
    ),
    path(
        "<str:business_slug>/candidates/",
        get_business_candidates,
        name="get_business_candidates",
    ),
    # subscriptions
    path(
        "<str:business_slug>/subscriptions/",
        business_subscriptions,
        name="business_subscriptions",
    ),
    path(
        "<str:business_slug>/subscriptions/<str:subscription_id>/",
        business_subscription,
        name="new_subscription",
    ),
    # billing
    path(
        "<str:business_slug>/billing/invoices/",
        business_invoices_api_view,
        name="business_invoices",
    ),
    path(
        "<str:business_slug>/billing/invoices/<str:invoice_id>/",
        business_invoice_details_api_view,
        name="business_invoice_details",
    ),
    path(
        "<str:business_slug>/billing/invoices/<str:invoice_id>/download/",
        get_invoice_pdf,
        name="get_invoice_pdf",
    ),
]


# candidates features
# registration, registration, resume, jobs, interviews, job applications, job skills, work types, work benefits,
