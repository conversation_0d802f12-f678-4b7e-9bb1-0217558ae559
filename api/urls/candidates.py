from django.urls import path
from accounts.views.auth import *

# businesses
from api.views.interviews import get_candidate_interviews
from businesses.views.jobs import *
from businesses.views.registration import *
from candidates.views.applications import *
from candidates.views.candidates import *
from candidates.views.data.dashboard import *
from candidates.views.data.profile import *
from candidates.views.generate_cv import generate_resume_view
from candidates.views.interview import new_interview
from api.views.candidates import *

urlpatterns = [
    path("", get_all_candidates, name="get_all_candidates"),
    path("profile/", candidate_data, name="candidate_data"),
    path("profile/update/", update_profile, name="update_profile"),
    path("profile/status/", get_profile_status, name="get_profile_status"),
    path("profile/delete/", delete_candidate, name="delete_candidate"),
    path("profile/<str:candidate_id>/", candidate_profile_data, name="candidate_data"),
    path(
        "profile/<str:candidate_id>/resume/",
        generate_resume_view,
        name="generate_resume",
    ),
    path("dashboard/", dashboard_data, name="dashboard_data"),
    path("applications/", get_applications, name="get_applications"),
    path(
        "applications/<str:application_id>/update/",
        update_application,
        name="update_application",
    ),
    path(
        "interviews/",
        get_candidate_interviews,
        name="candidate_interviews",
    ),
    # path(
    #     "interviews/<str:interview_id>/confirm/",
    #     candidate_interviews_confirm, name="candidate_interviews_confirm",
    # ),
]
