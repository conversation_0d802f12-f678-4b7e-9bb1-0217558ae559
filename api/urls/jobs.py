from django.urls import path

# account
from accounts.views.auth import *

# businesses
from api.views import jobs
from api.views.jobs import *
from businesses.views.jobs import *
from businesses.views.registration import *
from candidates.views.applications import *
from candidates.views.candidates import *
from candidates.views.data.dashboard import *
from candidates.views.data.profile import *
from candidates.views.interview import new_interview

urlpatterns = [
    path(
        "<str:business_slug>/new/",
        jobs.new_job,
        name="new_job",
    ),
    path(
        "",
        jobs.list_jobs,
        name="list_job_types",
    ),
    path("job-types/", get_job_types_api, name="get_job_types_api",),
    path("work-types/", get_work_types_api, name="get_work_types_api",),
    path("experience-levels/", get_experience_levels_api, name="get_experience_levels_api",),
    path("locations/", get_locations_api, name="get_locations_api",),
    path(
        "<str:job_slug>/",
        jobs.get_job_api,
        name="get_job_api",
    ),
    path(
        "<str:job_slug>/update/",
        update_job_api,
        name="update_job_api",
    ),
    path("<str:job_slug>/apply/", save_application, name="save_application"),
]
