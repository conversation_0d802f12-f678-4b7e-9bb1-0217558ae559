from django.urls import path
from django.urls import path
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView

# account
from accounts.views.auth import *

# businesses
from api.views import jobs, accounts
from businesses.views.jobs import *
from businesses.views.registration import *
from candidates.views.applications import *
from candidates.views.candidates import *
from candidates.views.data.dashboard import *
from candidates.views.data.profile import *
from candidates.views.interview import new_interview

urlpatterns = [
    path("token/", TokenObtainPairView.as_view(), name="get_token"),
    path("token/refresh/", TokenRefreshView.as_view(), name="refresh"),
    path("login/", accounts.login_api, name="login_view"),
    path("user/", user_info, name="user"),
    path("register/", sign_up_api, name="sign_up_api"),
    path("verify-email/", verify_email, name="verify-email"),
    path("verify-code/", verify_code, name="verify-code"),
    path("reset-password/", reset_password, name="reset-password"),
    path("subscribe/", accounts.subscribe, name="subscribe"),
]
# authentication
