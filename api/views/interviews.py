from accounts.services.auth import PermissionDecorators
from accounts.views.services import verify_user
from rest_framework.response import Response
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from base import serializers
from base.services.interviews import InterviewService
from base.services.logging import LoggingService
from businesses.models import Company
from candidates.views.serializers import GetInterviewSerializer

interview_service = InterviewService()

"""
Candidate interviews endpoints
"""

"""
Company interviews endpoints
"""


# get interviews
@api_view(["GET"])
@permission_classes([IsAuthenticated])
@PermissionDecorators.is_creator(Company, lookup_field="slug", url_param_names=["business_slug"])
def get_business_interviews(request, business_slug):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_403_FORBIDDEN,
        )

    response = interview_service.get_interviews_by_company(business_slug)

    if not response.success:
        return Response({"error": response.message}, status=response.status)

    serializer = GetInterviewSerializer(response.data, many=True)
    return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(["GET"])

@permission_classes([IsAuthenticated])
def get_candidate_interviews(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_403_FORBIDDEN,
        )
    response = interview_service.get_interviews_user(user.id)
    if not response.success:
        return Response({"error": response.message}, status=response.status)
    serializer = GetInterviewSerializer(response.data, many=True)
    return Response(serializer.data, status=status.HTTP_200_OK)
