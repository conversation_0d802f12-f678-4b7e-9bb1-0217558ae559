from accounts.views.services import verify_user
from rest_framework.response import Response
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from businesses.services.applications import BusinessApplicationService


"""
Endpoints for interviews on business
"""


# get business interviews
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_business_interviews(request, business_id):
    pass


# get business interview details
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_business_interview_details(request, business_id, interview_id):
    pass


# update business interview
@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def update_business_interview(request, business_id, interview_id):
    pass


"""
Endpoints for interviews on candidates
"""


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_candidate_interviews(request, candidate_id):
    pass


# get candidate interview details
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_candidate_interview_details(request, candidate_id, interview_id):
    pass


# update candidate interview
@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def update_candidate_interview(request, candidate_id, interview_id):
    pass
