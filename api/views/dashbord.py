from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from accounts.views.services import verify_user
from jobs.models import Job
from base.models import BaseModel
from candidates.models import Candidate, Application
from datetime import datetime, timedelta
from django.utils.timezone import now
from rest_framework import status


def get_month_start_end_dates(year, month):
    month_start = datetime(year, month, 1)
    if month == 12:
        month_end = datetime(year + 1, 1, 1) - timedelta(seconds=1)
    else:
        month_end = datetime(year, month + 1, 1) - timedelta(seconds=1)
    return month_start, month_end


def get_monthly_counts(model, month_start, month_end):
    return model.objects.filter(date_created__range=[month_start, month_end]).count()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def dashboard_details(request):
    if request.method == "GET":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_403_FORBIDDEN,
            )
        try:
            total_jobs = Job.objects.count()
            current_date = now()
            month_start = current_date.replace(day=1)
            month_end = (month_start + timedelta(days=31)).replace(day=1) - timedelta(
                days=1
            )
            month_increase = Job.objects.filter(
                date_created__range=[month_start, month_end]
            ).count()
            job_increase_percentage = (
                ((month_increase / total_jobs) * 100) if total_jobs > 0 else 0
            )
            total_applicants = Candidate.objects.count()

            # For applicants
            total_applications = Application.objects.count()
            month_increase_applications = Application.objects.filter(
                date_created__range=[month_start, month_end]
            ).count()

            # Job matches
            total_job_matches = (
                Job.objects.filter(application__isnull=False).distinct().count()
            )
            jobs_with_applications_this_month = (
                Job.objects.filter(
                    application__date_created__range=[month_start, month_end]
                )
                .distinct()
                .count()
            )
            increase_percentage_jobs_matches = (
                (jobs_with_applications_this_month / total_job_matches) * 100
                if total_job_matches > 0
                else 0
            )

            # User performances
            employees_count = []
            applicants_counts = []
            months = [
                "Jan",
                "Feb",
                "Mar",
                "Apr",
                "May",
                "Jun",
                "Jul",
                "Aug",
                "Sep",
                "Oct",
                "Nov",
                "Dec",
            ]
            current_year = current_date.year

            for month in range(1, 13):
                month_start, month_end = get_month_start_end_dates(current_year, month)
                employees_count.append(
                    get_monthly_counts(BaseModel, month_start, month_end)
                )
                applicants_counts.append(
                    get_monthly_counts(Application, month_start, month_end)
                )

            # Applicants data
            applications = Application.objects.all()
            application_list = []
            print(f"Found {applications.count()} applications.")

            for application in applications:
                if application.applicant and application.applicant.user:
                    applicant_name = application.applicant.user.get_full_name()
                    applicant_email = application.applicant.user.email
                    job_posting = (
                        application.job_applied.name
                        if application.job_applied
                        else "N/A"
                    )
                    company_name = (
                        application.job_applied.company_name.name
                        if application.job_applied
                        and application.job_applied.company_name
                        else "N/A"
                    )
                    application_time = application.date_created.strftime()
                    application_status = application.status

                    application_list.append(
                        {
                            "applicant": applicant_name,
                            "email": applicant_email,
                            "job_posting": job_posting,
                            "company": company_name,
                            "application_time": application_time,
                            "status": application_status,
                        }
                    )
                    print(f"Added application for {applicant_name}.")
                else:
                    print(
                        f"Skipping application with id {application.id} due to missing applicant or user."
                    )

            recent_sign_ups = (
                user.objects.filter(date_joined__range=[month_start, month_end])
                .order_by("-date_joined")[:4]
                .values(
                    "first_name", "last_name", "email", "is_candidate", "date_joined"
                )
            )

            formatted_sign_ups = [
                {
                    "name": f"{user['first_name']} {user['last_name']}",
                    "email": user["email"],
                    "type": "Candidate" if user["is_candidate"] else "Employer",
                    "date": user["date_joined"].strftime("%d %B %Y"),
                }
                for user in recent_sign_ups
            ]
            data = {
                "numbers": {
                    "job_posted": {
                        "number": total_jobs,
                        "increase_percentage": job_increase_percentage,
                    },
                    "total_applicants": {
                        "number": total_applicants,
                        "increase_percentage": month_increase_applications,
                    },
                    "active_job_matches": {
                        "number": total_job_matches,
                        "increase_percentage": increase_percentage_jobs_matches,
                    },
                    "user_performances": {
                        "yearly": {
                            "employees": employees_count,
                            "applicants": applicants_counts,
                            "months": months[:9],
                        }
                    },
                    "applicants": application_list,
                    "recent_sign_up": formatted_sign_ups,
                }
            }
            return Response(data, status=status.HTTP_200_OK)

        except Exception as e:
            print(f"Error: {e}")
            return Response(
                {"status": "failed", "message": "Failed to retrieve dashboard details"},
                status=status.HTTP_400_BAD_REQUEST,
            )
