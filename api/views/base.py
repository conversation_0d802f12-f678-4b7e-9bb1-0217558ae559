from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny

from base.services.packagess import PackagesService
from base.services.logging import LoggingService
from django_ratelimit.decorators import ratelimit


logging_service = LoggingService()
packages_services = PackagesService()


@api_view(["GET", "POST"])
@permission_classes([AllowAny])
@ratelimit(key="ip", rate="10/m", method="POST", block=False)
def packages_api_view(request):
    try:
        if request.method == "GET":
            response = packages_services.get_all_packages()
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.status,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )

        elif request.method == "POST":
            response = packages_services.create_package(request.user, request.data)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.status,
                )

            return Response(
                response.data,
                status=status.HTTP_201_CREATED,
            )

    except Exception as e:
        logging_service.log_error(e)
        return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)
