from rest_framework.permissions import AllowAny
from rest_framework import status
from rest_framework.response import Response
from django.contrib.auth.models import User
from rest_framework.decorators import api_view, permission_classes
from accounts.services.auth import AuthService
from accounts.views.email_templates.subscripion_and_account_saved import (
    send_subscription_and_account_saved_email,
)
from accounts.views.email_templates.subscripion_saved import (
    send_subscription_saved_email,
)
from candidates.views.candidates import register_subscriber
from django_ratelimit.decorators import ratelimit


auth_service = AuthService()


@api_view(["POST"])
@permission_classes([AllowAny])
@ratelimit(key="ip", rate="10/m", method="POST", block=False)
def login_api(request):
    # Skip rate limit for admin users
    if hasattr(request, "skip_rate_limit") and request.skip_rate_limit:
        pass
    elif getattr(request, "limited", False):
        return Response({"error": "Too many requests"}, status=429)

    username = request.data.get("username")
    password = request.data.get("password")

    if not username or not password:
        return Response(
            {"error": "Please provide both username and password"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    response = auth_service.get_token(username, password)
    if not response.success:
        return Response({"error": response.message}, status=response.status)

    return Response(response.data, status=response.status)


def save_subscription(email, full_name, subscription_type):
    pass


@api_view(["POST"])
@permission_classes([AllowAny])
@ratelimit(key="ip", rate="10/m", method="POST", block=False)
def subscribe(request):
    if request.method == "POST":
        email = request.data.get("email")
        full_name = request.data.get("full_name")
        _type = request.data.get("type")
        create_account = request.data.get("create_me_account")
        if email and _type:
            first_name = ""
            last_name = ""
            if full_name:
                name = str(full_name).split()

                if len(name) > 1:
                    first_name = name[0]
                    last_name = name[-1]
                else:
                    first_name = full_name
            is_created, message = save_subscription(
                email=email, full_name=full_name, subscription_type=_type
            )
            account_created = None
            if create_account:
                account_created, account_message, user = register_subscriber(
                    first_name=first_name,
                    last_name=last_name,
                    email=email,
                    password=123,
                )

            # send_subscription email notification

            if is_created and account_created:
                send_subscription_and_account_saved_email(
                    recipient_email=email,
                    first_name=first_name,
                    login_link="https://jobmatch.csrlimited.com/login/",
                )
                return Response(
                    {"message": f"{message} and {account_message}"},
                    status=status.HTTP_200_OK,
                )
            elif is_created:
                return Response(
                    {"message": f"{message} and {account_message}"},
                    status=status.HTTP_200_OK,
                )
            elif account_created:
                return Response(
                    {"message": f"{message} and {account_message}"},
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"message": f"{message}"}, status=status.HTTP_400_BAD_REQUEST
                )
        else:
            return Response(
                {"message": "Email and type are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
