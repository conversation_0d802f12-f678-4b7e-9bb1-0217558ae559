from accounts.services.auth import PermissionDecorators
from accounts.views.services import verify_user
from rest_framework.response import Response
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from base.services.logging import LoggingService
from businesses.models import Company
from businesses.services.applications import BusinessApplicationService

applications_service = BusinessApplicationService()
logging_service = LoggingService()
"""
Endpoints for applications on business
"""


# get business applications
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_business_applications(request, business_slug):
    pass


# get business application details
@api_view(["GET"])
@permission_classes([IsAuthenticated])
@PermissionDecorators.is_creator(Company, lookup_field="slug", url_param_names=["business_slug"])
def get_business_application_details(request, business_slug, application_id):
    pass


# update business application
@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@PermissionDecorators.is_creator(Company, lookup_field="slug", url_param_names=["business_slug"])
def update_business_application(request, business_slug, application_id):
    try:
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)

        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_403_FORBIDDEN,
            )

        application_data = request.data
        application_data["applicant_id"] = user.id
        application_response = applications_service.update_application(
            business_slug,
            application_id,
            application_data,
        )

        if not application_response.success:
            return Response(
                {"error": application_response.message},
                status=application_response.status,
            )

        return Response(application_response.data, status=application_response.status)
    except Exception as e:
        logging_service.log_error(e)
        return Response({"error": "Error updating application"})


"""
Endpoints for applications on candidates
"""


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_candidate_applications(request, candidate_id):
    pass


# get candidate application details
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_candidate_application_details(request, candidate_id, application_id):
    pass


# update candidate application
@api_view(["PUT"])
@permission_classes([IsAuthenticated])
def update_candidate_application(request, candidate_id, application_id):
    pass
