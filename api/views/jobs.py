from uuid import UUID
from django.http import Http404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from accounts.services.auth import PermissionDecorators
from accounts.views.services import SkillSerializer, verify_user
from base.services.logging import LoggingService
from businesses.models import *
from businesses.views.serializers import JobSerializer
from candidates.models import *
from jobs.models import *
from candidates.views.serializers import ApplicationSerializer
from rest_framework.pagination import PageNumberPagination

from rest_framework import status
from datetime import datetime

from base.models import Skill, Category
import time
from django.http import JsonResponse

from jobs.services.job_service import JobService
from django_ratelimit.decorators import ratelimit


job_service = JobService()
logging_service = LoggingService()


class JobPagination(PageNumberPagination):
    page_size = 10  # Adjust page size as needed


@api_view(["GET"])
def job_required_skills(request, category):
    if request.method == "GET":
        try:
            job_skill_category = Category.objects.get(name=category)
            skills_list = Skill.objects.filter(category_name=job_skill_category)
            skills = SkillSerializer(skills_list, many=True)
            return Response(skills.data, status=status.HTTP_200_OK)
        except Category.DoesNotExist:
            return Response({"message": "no category with that name found"})


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def new_job(request, business_slug):
    if request.method == "POST":
        # authorization
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            response = job_service.create_job(user, business_slug, request.data)
            if not response.success:
                return Response({"error": response.message}, status=response.status)
            serializer = JobSerializer(response.data)
            return Response(
                serializer.data,
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            logging_service.log_error(e)
            return Response(
                {"message": "Unknown error while creating a job"},
                status=status.HTTP_400_BAD_REQUEST,
            )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_job_api(request, job_slug):
    try:
        job = job_service.get_job(job_slug)
        if not job.success:
            return Response({"error": job.message}, status=job.status)
        serializer = JobSerializer(job.data)
        job_data = serializer.data
        job_data["has_applied"] = job_service.has_user_applied_to_job(
            job.data.id, request.user
        )
        return Response(job_data, status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Failed to fetch job"}, status=status.HTTP_404_NOT_FOUND
        )


@api_view(["GET"])
@permission_classes([AllowAny])
@ratelimit(key="ip", rate="10/m", method="GET", block=False)
def list_jobs(request):
    logged_in_user = request.user
    print("User is: ", logged_in_user)
    try:
        # Basic parameters
        page = int(request.GET.get("page", 1))
        page_size = int(request.GET.get("page_size", 10))
        search_query = request.GET.get("search", None)
        order_by = request.GET.get("order_by", "-date_created")

        # Define which fields are arrays vs single values
        array_fields = ["job_type", "work_type", "experience_level", "job_industry"]
        single_fields = ["location", "min_salary", "max_salary", "status"]

        # Build filters
        filters = {}

        # Handle array fields
        for field in array_fields:
            values = request.GET.getlist(field)
            if values:
                filters[field] = values

        # Handle single value fields
        for field in single_fields:
            value = request.GET.get(field)
            if value:
                filters[field] = value

        # Get jobs
        jobs = job_service.get_jobs_list(
            page=page,
            page_size=page_size,
            filters=filters,
            search_query=search_query,
            order_by=order_by,
            user=logged_in_user,
        )

        if not jobs.success:
            return Response({"error": jobs.message}, status=jobs.status)

        return Response(
            {
                "jobs": jobs.data["jobs"],
                "total_pages": jobs.data["total_pages"],
                "current_page": jobs.data["current_page"],
                "total_jobs": jobs.data["total_jobs"],
            },
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Failed to fetch job list"},
            status=status.HTTP_400_BAD_REQUEST,  # Changed from 404 as this is more appropriate
        )


@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@PermissionDecorators.is_creator(Job, lookup_field="slug", url_param_names=["job_slug"])
def update_job_api(request, job_slug):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_403_FORBIDDEN,
        )
    response = job_service.update_job(user, job_slug, request.data)
    if not response.success:
        return Response({"error": response.message}, status=response.status)
    serializer = JobSerializer(response.data)
    return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
@PermissionDecorators.is_creator(
    Company, lookup_field="slug", url_param_names=["business_slug"]
)
def delete_job_api(request, business_slug, job_slug):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_403_FORBIDDEN,
        )
    response = job_service.delete_job(user, job_slug)
    if not response.success:
        return Response({"error": response.message}, status=status.HTTP_400_BAD_REQUEST)
    return Response(
        {"message": "Job deleted successfully"}, status=status.HTTP_204_NO_CONTENT
    )


@api_view(["GET"])
def dashboard_data_view(request):
    data = {
        "numbers": {
            "job_posted": {"number": 12, "increase_percentage": 7.5},
            "applicants": {"number": 14, "increase_percentage": 8.2},
            "total_applicants": {"number": 14500, "increase_percentage": 6.3},
            "active_job_matches": {"number": 14500, "increase_percentage": 6.3},
        },
        "user_performances": {
            "yearly": {
                "employers": [320, 290, 310, 340, 330, 360, 310, 330, 320],
                "applicants": [240, 260, 270, 300, 310, 290, 300, 310, 300],
                "months": [
                    "Jan",
                    "Feb",
                    "Mar",
                    "Apr",
                    "May",
                    "Jun",
                    "Jul",
                    "Aug",
                    "Sep",
                ],
            }
        },
        "applications": [
            {
                "applicant": "John Doe",
                "email": "<EMAIL>",
                "job_posting": "Senior UI/UX Designer",
                "company": "Spotify",
                "application_time": "17 July 2024 10:00 AM",
                "status": "In Review",
            },
            {
                "applicant": "Jane Smith",
                "email": "<EMAIL>",
                "job_posting": "Frontend Developer",
                "company": "Google",
                "application_time": "18 July 2024 11:00 AM",
                "status": "In Review",
            },
            {
                "applicant": "Alice Johnson",
                "email": "<EMAIL>",
                "job_posting": "Backend Developer",
                "company": "Amazon",
                "application_time": "19 July 2024 12:00 PM",
                "status": "In Review",
            },
            {
                "applicant": "Bob Brown",
                "email": "<EMAIL>",
                "job_posting": "Product Manager",
                "company": "Microsoft",
                "application_time": "20 July 2024 01:00 PM",
                "status": "In Review",
            },
            {
                "applicant": "Charlie Davis",
                "email": "<EMAIL>",
                "job_posting": "Data Scientist",
                "company": "Facebook",
                "application_time": "21 July 2024 02:00 PM",
                "status": "In Review",
            },
        ],
        "recent_sign_ups": [
            {
                "name": "Ammar Hernandez",
                "email": "<EMAIL>",
                "type": "Applicant",
                "date": "17 June 2024",
            },
            {
                "name": "Filip Baldwin",
                "email": "<EMAIL>",
                "type": "Employer",
                "date": "17 June 2024",
            },
            {
                "name": "Abu Rocha",
                "email": "<EMAIL>",
                "type": "Applicant",
                "date": "17 June 2024",
            },
            {
                "name": "Mahir Bell",
                "email": "<EMAIL>",
                "type": "Employer",
                "date": "17 June 2024",
            },
        ],
    }

    return JsonResponse(data)


@api_view(["GET"])
def get_job_types_api(request):
    try:
        response = job_service.get_job_types()
        if not response.success:
            return Response({"error": response.message}, status=response.status)
        return Response(response.data, status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Failed to fetch job types"}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["GET"])
def get_work_types_api(request):
    try:
        response = job_service.get_work_types()
        if not response.success:
            return Response({"error": response.message}, status=response.status)
        return Response(response.data, status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Failed to fetch work types"}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["GET"])
def get_experience_levels_api(request):
    try:
        response = job_service.get_experience_levels()
        if not response.success:
            return Response({"error": response.message}, status=response.status)
        return Response(response.data, status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Failed to fetch experience levels"},
            status=status.HTTP_400_BAD_REQUEST,
        )


@api_view(["GET"])
def get_locations_api(request):
    try:
        response = job_service.get_locations()
        if not response.success:
            return Response({"error": response.message}, status=response.status)
        return Response(response.data, status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Failed to fetch locations"}, status=status.HTTP_400_BAD_REQUEST
        )
