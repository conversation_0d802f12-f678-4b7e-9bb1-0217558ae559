from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from jobs.models import (
    Job,
    JobType,
    WorkType,
    ExperienceLevel,
    Company,
    Benefit,
    JobSkill,
)


@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
def updateJobAPI(request, job_id):
    job = get_object_or_404(Job, id=job_id)
    data = request.data

    # Fields that can be updated
    updatable_fields = [
        "job_name",
        "location",
        "job_type",
        "work_type",
        "experience_level",
        "min_salary",
        "max_salary",
        "job_description",
        "responsibilities",
        "qualifications",
        "nice_to_have",
        "status",
    ]

    try:
        # Update simple fields
        for field in updatable_fields:
            if field in data:
                setattr(job, field, data[field])

        # Update related fields
        if "company_name" in data:
            company, _ = Company.objects.get_or_create(name=data["company_name"])
            job.company_name = company

        if "job_type" in data:
            job_type, _ = JobType.objects.get_or_create(name=data["job_type"])
            job.job_type = job_type

        if "work_type" in data:
            work_type, _ = WorkType.objects.get_or_create(name=data["work_type"])
            job.work_type = work_type

        if "experience_level" in data:
            experience_level, _ = ExperienceLevel.objects.get_or_create(
                name=data["experience_level"]
            )
            job.experience_level = experience_level

        # Update benefits
        if "benefits" in data:
            job.benefits.clear()
            for benefit_name in data["benefits"]:
                benefit, _ = Benefit.objects.get_or_create(name=benefit_name)
                job.benefits.add(benefit)

        # Update required skills
        if "required_skills" in data:
            job.required_skills.clear()
            for skill_name in data["required_skills"]:
                skill, _ = JobSkill.objects.get_or_create(name=skill_name)
                job.required_skills.add(skill)

        job.save()
        job_data = {
            "slug": job.slug,
            "name": job.name,
            "company_name": job.company_name.name,
            "location": job.location,
            "job_type": job.job_type.name,
            "work_type": job.work_type.name,
            "experience_level": job.experience_level.name,
        }
        return Response(job_data, status=status.HTTP_200_OK)

    except Exception as e:
        print(e)
        return Response(
            {"error": "Unknown error while updating the job"},
            status=status.HTTP_400_BAD_REQUEST,
        )
