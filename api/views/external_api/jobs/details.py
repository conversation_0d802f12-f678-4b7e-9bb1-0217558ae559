from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from jobs.models import Job


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def jobDetailsDetails(request, job_id):
    job = get_object_or_404(Job, id=job_id)

    try:
        job_data = {
            "id": job.id,
            "job_name": job.name,
            "company_name": job.company_name.name if job.company_name else None,
            "location": job.location,
            "job_type": job.job_type.name if job.job_type else None,
            "work_type": job.work_type.name if job.work_type else None,
            "experience_level": (
                job.experience_level.name if job.experience_level else None
            ),
            "min_salary": float(job.min_salary) if job.min_salary else None,
            "max_salary": float(job.max_salary) if job.max_salary else None,
            "job_description": job.job_description,
            "responsibilities": job.responsibilities,
            "qualifications": job.qualifications,
            "nice_to_have": job.nice_to_have,
            "status": job.status,
            "created_at": job.date_created.isoformat() if job.date_created else None,
            "updated_at": job.date_updated.isoformat() if job.date_updated else None,
            "benefits": [benefit.name for benefit in job.benefits.all()],
            "required_skills": [skill.name for skill in job.required_skills.all()],
            "applicants": job.applicants,
        }

        return Response(job_data, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
