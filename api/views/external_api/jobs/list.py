from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from jobs.models import Job
from rest_framework.pagination import PageNumberPagination


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_jobs_data(request):
    all_jobs = Job.objects.all()
    total_jobs = all_jobs.count()
    active_jobs = all_jobs.filter(status="active").count()
    closed_jobs = all_jobs.filter(status="closed").count()

    data = {
        "total_jobs": total_jobs,
        "active_jobs": active_jobs,
        "closed_jobs": closed_jobs,
    }

    # do pagination for job, each one should return 40 jobs
    paginator = PageNumberPagination()
    paginator.page_size = 40
    paginated_jobs = paginator.paginate_queryset(all_jobs, request)
    jobs_list = []
    for job in paginated_jobs:
        job_data = {
            "id": job.id,
            "name": job.name,
            "employer": job.company_name.name,
            "applicants": job.applicants,
            "date_posted": job.date_created,
            "status": job.status,
        }
        jobs_list.append(job_data)
        # make an object for each job and add it to the list of job_list
    return Response({"data": data, "jobs": jobs_list}, status=status.HTTP_200_OK)
