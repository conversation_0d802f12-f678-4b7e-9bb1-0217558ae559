from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from accounts.views.services import verify_user
from jobs.models import (
    Job,
    JobType,
    WorkType,
    ExperienceLevel,
    Company,
    Benefit,
    JobSkill,
)


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def newJobAPI(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_400_BAD_REQUEST,
        )
    data = request.data
    required_fields = [
        "job_name",
        "company_name",
        "experience_level",
        "responsibilities",
        "job_description",
    ]
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        return Response(
            {
                "status": "failed",
                "message": f"Missing required fields: {', '.join(missing_fields)}",
            },
            status=status.HTTP_400_BAD_REQUEST,
        )
    try:
        # Get or create related objects
        company, _ = Company.objects.get_or_create(name=data["company_name"])
        job_type, _ = JobType.objects.get_or_create(name=data["job_type"])
        work_type, _ = WorkType.objects.get_or_create(name=data["work_type"])
        experience_level, _ = ExperienceLevel.objects.get_or_create(
            name=data["experience_level"]
        )

        # Create the job
        job = Job.objects.create(
            name=data["job_name"],
            company_name=company,
            location=data["location"],
            job_type=job_type,
            work_type=work_type,
            experience_level=experience_level,
            min_salary=data["min_salary"],
            max_salary=data["max_salary"],
            job_description=data["job_description"],
            responsibilities=data.get("responsibilities"),
            qualifications=data.get("qualifications"),
            nice_to_have=data.get("nice_to_have"),
            status=data.get("status", "active"),
        )

        # Add benefits
        if "benefits" in data:
            for benefit_name in data["benefits"]:
                benefit, _ = Benefit.objects.get_or_create(name=benefit_name)
                job.benefits.add(benefit)

        # Add required skills
        if "required_skills" in data:
            for skill_name in data["required_skills"]:
                skill, _ = JobSkill.objects.get_or_create(name=skill_name)
                job.required_skills.add(skill)

        if job is not None:
            job_data = {
                "slug": job.slug,
                "name": job.name,
                "company_name": job.company_name.name,
                "location": job.location,
                "job_type": job.job_type.name,
                "work_type": job.work_type.name,
                "experience_level": job.experience_level.name,
            }

            return Response(job_data, status=status.HTTP_201_CREATED)
        else:
            return Response(
                {"error": "Failed to create new job"},
                status=status.HTTP_400_BAD_REQUEST,
            )
    except Exception as e:
        print(e)
        return Response(
            {"error": "Unknown error while creating job"},
            status=status.HTTP_400_BAD_REQUEST,
        )
