from ast import Is
from accounts.services.auth import PermissionDecorators, PermissionValidators
from accounts.services.profile_service import ProfileService
from accounts.views.services import verify_user
from base.services.logging import LoggingService
from rest_framework.response import Response
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from businesses.models import Company
from businesses.services.applications import BusinessApplicationService
from businesses.services.business import BusinessService
from candidates.repositories.candidate_repository import CandidateRepository
from candidates.services import candidate
from candidates.services.candidate import CandidateService
import jwt

from candidates.views.serializers import GetCandidateSerializer
from django_ratelimit.decorators import ratelimit


business_service = BusinessService()
application_service = BusinessApplicationService()
logging_service = LoggingService()
service = ProfileService()

candidate_service = CandidateService()
candidate_repo = CandidateRepository()


# service categories
@api_view(["POST"])
def new_business(request):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_400_BAD_REQUEST,
        )

    response = business_service.create_business(user, data=request.data)

    if not response.success:
        return Response({"error": response.message}, status=response.status)
    return Response(response.data, status=response.status)


@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
@PermissionDecorators.is_creator(
    Company, lookup_field="slug", url_param_names=["business_slug"]
)
def update_business(request, business_slug):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_400_BAD_REQUEST,
        )
    response = business_service.update_business(
        user=user, slug=business_slug, business_data=request.data
    )

    if not response.success:
        return Response({"error": response.message}, status=response.status)
    return Response(response.data, status=response.status)


@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
@PermissionDecorators.is_creator(
    Company, lookup_field="slug", url_param_names=["business_slug"]
)
@PermissionValidators.can_delete_profile(Company)
def delete_business(request, business_slug):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_403_FORBIDDEN,
        )
    response = business_service.delete_business(user=user, slug=business_slug)
    if not response.success:
        return Response({"error": response.message}, status=response.status)
    return Response(
        {"message": "Business deleted successfully"}, status=response.status
    )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def details_business(request, business_slug):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_400_BAD_REQUEST,
        )
    response = business_service.get_business_by_slug(business_slug)
    if not response.success:
        return Response({"error": response.message}, status=response.status)
    return Response(response.data, status=response.status)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@PermissionDecorators.is_creator(
    Company, lookup_field="slug", url_param_names=["business_slug"]
)
def get_profile_status(request, business_slug):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_400_BAD_REQUEST,
        )
    response = business_service.get_business_profile_status(business_slug)
    if not response.success:
        return Response({"error": response.message}, status=response.status)
    return Response(response.data, status=response.status)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@PermissionDecorators.is_creator(
    Company, lookup_field="slug", url_param_names=["business_slug"]
)
def get_business_applications(
    request,
    business_slug,
    job_id,
):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_400_BAD_REQUEST,
        )

    page = request.query_params.get("page", 1)
    search_query = request.query_params.get("search", None)
    page_size = request.query_params.get("page_size", 10)

    try:
        page = int(page)
        page_size = int(page_size)
    except ValueError:
        return Response(
            {"error": "Invalid page or page_size parameter"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    response = application_service.get_applications_by_job_id(
        user,
        business_slug,
        job_id=job_id,
        page=page,
        page_size=page_size,
        search_query=search_query,
    )

    if not response.success:
        return Response({"error": response.message}, status=response.status)

    return Response(response.data, status=response.status)


@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
def update_application(request, id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_403_FORBIDDEN,
        )
    response = application_service.update_application(id, request.data)
    if not response.success:
        return Response({"error": response.message}, status=status.HTTP_400_BAD_REQUEST)
    return Response(response.data, status=status.HTTP_200_OK)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@PermissionDecorators.is_creator(
    Company, lookup_field="slug", url_param_names=["business_slug"]
)
def get_all_business_applications(request, business_slug):
    try:
        search_query = request.query_params.get("search", None)
        page = request.query_params.get("page", 1)
        page_size = request.query_params.get("page_size", 10)
        response = application_service.get_all_business_applications(
            request.user,
            business_slug,
            page=page,
            page_size=page_size,
            search_query=search_query,
        )
        if not response.success:
            return Response({"error": response.message}, status=response.status)
        return Response(response.data, status=response.status)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An Error Occured while Fetching Business Application"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# get candidate details by candidate_id as a business
@api_view(["GET"])
@permission_classes([IsAuthenticated])
@PermissionDecorators.is_creator(
    Company, lookup_field="slug", url_param_names=["business_slug"]
)
def get_business_candidate(request, business_slug, candidate_id):
    """
    Retrieve candidate details by candidate_id.
    """
    try:
        response = candidate_repo.get_candidate(candidate_id)
        if not response.success:
            return Response(
                {"error": response.message},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serialize = GetCandidateSerializer(response.data).data
        return Response(serialize, status=status.HTTP_200_OK)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An Error Occured while Fetching Candidate Details"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([AllowAny])
@ratelimit(key="ip", rate="10/m", method="GET", block=False)
def get_all_businesses(request):
    try:
        page = request.query_params.get("page", 1)
        page_size = request.query_params.get("page_size", 10)
        response = business_service.get_all_businesses(page=page, page_size=page_size)
        if not response.success:
            return Response({"error": response.message}, status=response.status)
        return Response(response.data, status=response.status)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An Error Occured while Fetching All Businesses"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def complete_profile(request):
    try:
        candidate_ids = request.data.get("candidate_ids")
        response = service.complete_profile(candidate_ids)
        if not response.success:
            return Response({"error": response.message}, status=response.status)
        return Response({"message": response.message}, status=response.status)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"message": "Failed to request to complete profile"},
            status=status.HTTP_400_BAD_REQUEST,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_business_candidates(request, business_slug):
    try:
        page = request.query_params.get("page", 1)
        page_size = request.query_params.get("page_size", 10)
        search_query = request.query_params.get("search", None)
        response = business_service.get_business_candidates(
            business_slug, page=page, page_size=page_size, search_query=search_query
        )
        if not response.success:
            return Response({"error": response.message}, status=response.status)
        return Response(response.data, status=response.status)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An Error Occured while Fetching Business Candidates"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
