from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated

from api.urls import business
from base.services.logging import LoggingService
from businesses.services.admin import AdminBusinessService


logging_service = LoggingService()
business_service = AdminBusinessService()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def admin_businesses_api_view(request):
    """Manage businesses"""

    if request.method == "GET":
        # get parameters
        page = request.query_params.get("page", 1)
        page_size = request.query_params.get("page_size", 10)
        response = business_service.list_businesses(int(page), int(page_size))
        if not response.success:
            return Response({"error": response.message}, status=response.status)
        return Response(response.data, status=response.status)
    elif request.method == "POST":
        response = business_service.create_business(request.user, request.data)
        if not response.success:
            return Response(
                {"error": response.message},
                status=status.HTTP_400_BAD_REQUEST,
            )
        return Response(response.data, status=status.HTTP_201_CREATED)


@api_view(["GET", "PUT", "DELETE", "PATCH"])
@permission_classes([IsAuthenticated])
def admin_business_api_view(
    request,
    business_slug,
):
    """Manage business details"""
    try:
        # get business

        # update business
        if request.method == "PUT":
            response = business_service.update_business(
                user=request.user, business_slug=business_slug, data=request.data
            )
            if not response.success:
                return Response({"error": response.message}, status=response.status)
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        # delete business
        elif request.method == "DELETE":
            response = business_service.delete_business(request.user, business_slug)
            if not response.success:
                return Response(
                    {"error": response.message}, status=status.HTTP_400_BAD_REQUEST
                )
            return Response(status.HTTP_204_NO_CONTENT)

        elif request.method == "PATCH":
            action = request.data.get("action", None)
            if action and action == "activate":
                response = business_service.activate_business(business_slug)
                if not response.success:
                    return Response({"error": response.message}, status=response.status)
                return Response(response.data, status=status.HTTP_200_OK)
            elif action and action == "deactivate":
                response = business_service.deactivate_business(business_slug)
                if not response.success:
                    return Response({"error": response.message}, status=response.status)
                return Response(response.data, status=status.HTTP_200_OK)
            else:
                return Response(
                    {"error": "Invalid action"}, status=status.HTTP_400_BAD_REQUEST
                )

        elif request.method == "GET":
            response = business_service.get_business_details(business_slug)
            if not response.success:
                return Response({"error": response.message}, status=response.status)
            return Response(response.data, status=status.HTTP_200_OK)

        else:
            return Response(
                {"error": "Invalid request method"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def admin_businesses_overview_api_view(request, business_id):
    """Get business overview"""
