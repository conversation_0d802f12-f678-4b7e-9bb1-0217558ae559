from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def admin_subscriptions_api_view(request, business_id):
    """Manage subscriptions"""


@api_view(["GET", "PUT", "DELETE", "PATCH"])
@permission_classes([IsAuthenticated])
def admin_subscriptions_api_view(request, business_id, subscription_id):
    """Manage subscription details"""


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def admin_subscriptions_overview_api_view(request, business_id):
    """Get subscription overview"""
