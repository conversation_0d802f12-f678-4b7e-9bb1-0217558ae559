from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from django.http import FileResponse
from accounts.models import Subscription, Invoice
from businesses.services.invoices import InvoiceService
from base.services.logging import LoggingService


logging_service = LoggingService()
invoice_service = InvoiceService()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def admin_invoices_api_view(request):
    """Manage invoices"""
    try:
        if request.method == "GET":
            "Get list of invoices"
            page = request.query_params.get("page", 1)
            page_size = request.query_params.get("page_size", 10)
            search_query = request.query_params.get("search", None)
            filters = request.query_params.get("filters", {})
            order_by = request.query_params.get("order_by", "-date_created")
            response = invoice_service.get_invoices(
                user=request.user,
                page=page,
                page_size=page_size,
                search_query=search_query,
                filters=filters,
                order_by=order_by,
            )
            if not response.success:
                return Response({"error": response.message}, status=response.status)
            return Response(response.data, status=status.HTTP_200_OK)
        elif request.method == "POST":
            "Create invoice"
            try:
                data = request.data.copy()
                subscription_id = data.pop("subscription_id")
                subscription = Subscription.objects.get(id=subscription_id)
                response = invoice_service.create_invoice(request.user, subscription)
                if not response.success:
                    return Response({"error": response.message}, status=response.status)
                return Response(response.data, status=status.HTTP_201_CREATED)
            except Subscription.DoesNotExist:
                return Response(
                    {"error": "Subscription not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def admin_invoice_download_api_view(request, invoice_id):
    """Download a PDF of an invoice"""
    try:
        response = invoice_service.download_invoice_pdf(invoice_id)
        if not response.success:
            return Response(
                {"message": response.message, "success": False}, status=response.status
            )

        # Return  PDF file as a FileResponse
        buffer = response.data
        buffer.seek(0)
        return FileResponse(
            buffer,
            as_attachment=True,
            filename=f"invoice-{invoice_id}.pdf",
            content_type="application/pdf",
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {
                "message": "An error occurred while processing your request",
                "success": False,
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "PUT", "DELETE", "PATCH"])
@permission_classes([IsAuthenticated])
def admin_invoice_api_view(request, invoice_id):
    """Manage invoice details"""
    try:
        try:
            invoice = Invoice.objects.get(id=invoice_id)
        except Invoice.DoesNotExist:
            return Response(
                {"message": "Invoice not found", "success": False},
                status=status.HTTP_404_NOT_FOUND,
            )

        if request.method == "GET":
            response = invoice_service.get_invoice(invoice_id)
            if not response.success:
                return Response(
                    {"message": "Failed to retrieve invoice", "success": False},
                    status=response.status,
                )
            return Response(
                {
                    "message": "Invoice retrieved successfully",
                    "success": True,
                    "data": response.data,
                },
                status=response.status,
            )

        elif request.method == "PATCH":
            data = request.data
            response = invoice_service.update_invoice(invoice_id, data, request.user)
            if not response.success:
                return Response(
                    {"message": "Failed to update invoice status", "success": False},
                    status=response.status,
                )
            return Response(
                {
                    "message": "Invoice status updated successfully",
                    "success": True,
                    "data": response.data,
                },
                status=response.status,
            )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {
                "message": "An error occurred while processing your request",
                "success": False,
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def admin_invoices_overview_api_view(request, business_id):
    """Get invoice overview"""
