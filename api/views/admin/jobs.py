from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from base.services.logging import LoggingService
from jobs.services.admin import JobsAdminService


service = JobsAdminService()
logging_service = LoggingService()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def admin_jobs_api_view(request):
    """Manage jobs"""
    try:
        if request.method == "GET":
            "Get list of jobs"
            page = request.query_params.get("page", 1)
            page_size = request.query_params.get("page_size", 10)
            search_query = request.query_params.get("search", None)
            filters = request.query_params.get("filters", {})
            order_by = request.query_params.get("order_by", "-date_created")
            response = service.get_all_jobs(
                request.user,
                page=page,
                page_size=page_size,
                search_query=search_query,
                filters=filters,
                order_by=order_by,
            )
            if not response.success:
                return Response({"error": response.message}, status=response.status)
            return Response(response.data, status=status.HTTP_200_OK)

        elif request.method == "POST":
            "Create job"
            data = request.data.copy()
            business_slug = data.pop('business_slug')
            response = service.create_job(
                request.user, business_slug, data
            )
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.status,
                )
            return Response(response.data, status=status.HTTP_201_CREATED)
        else:
            return Response(
                {"error": "Invalid request method"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["GET", "PUT", "DELETE", "PATCH"])
@permission_classes([IsAuthenticated])
def admin_job_api_view(request, job_slug):
    """Manage job details"""
    try:
        if request.method == "GET":
            "Get job details"
            response = service.get_job(request.user, job_slug)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.status,
                )
            return Response(response.data, status=status.HTTP_200_OK)

        elif request.method == "PUT":
            "Update job details"
            response = service.update_job(
                request.user, job_slug, request.data
            )
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.status,
                )
            return Response(response.data, status=status.HTTP_200_OK)
        elif request.method == 'PATCH':
            "Update job details"
            response = service.update_job_status(
                request.user, job_slug, request.data
            )
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.status,
                )
            return Response(response.data, status=status.HTTP_200_OK)
        elif request.method == "DELETE":
            "Delete job"
            response = service.delete_job(request.user, job_slug)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.status,
                )
            return Response(status=status.HTTP_204_NO_CONTENT)
        else:
            return Response(
                {"error": "Invalid request method"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def admin_jobs_overview_api_view(request, business_slug):
    """Get job overview"""
