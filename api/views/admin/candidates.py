import logging
from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated

from base.services.logging import LoggingService
from candidates.services.admin import AdminCandidateService

service = AdminCandidateService()
logging_service = LoggingService()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def admin_candidates_api_view(request):
    """Manage candidates"""
    try:
        if request.method == "GET":
            "Get list of candidates"
            # get parameters
            page = request.query_params.get("page", 1)
            page_size = request.query_params.get("page_size", 10)
            response = service.list_candidates(request.user, page, page_size)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.status,
                )
            return Response(response.data, status=status.HTTP_200_OK)

        elif request.method == "POST":
            "Create candidate"
            response = service.create_candidate(request.user, request.data)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.status,
                )
            return Response(response.data, status=status.HTTP_201_CREATED)

        else:
            return Response(
                {"error": "Invalid request method"},
                status=status.HTTP_400_BAD_REQUEST,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Failed to retrieve candidates"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "PUT", "DELETE", "PATCH"])
@permission_classes([IsAuthenticated])
def admin_candidate_api_view(request, candidate_id):
    """Manage candidate details"""
    try:
        if request.method == "GET":
            "Get candidate details"
            response = service.get_candidate_details(request.user, candidate_id)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.status,
                )
            return Response(response.data, status=status.HTTP_200_OK)

        elif request.method == "PUT":
            "Update candidate details"
            response = service.update_candidate(
                request.user, candidate_id, request.data
            )
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.status,
                )
            return Response(response.data, status=status.HTTP_200_OK)
        elif request.method == "DELETE":
            "Delete candidate"
            response = service.delete_candidate(request.user, candidate_id)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.status,
                )
            return Response(status=status.HTTP_204_NO_CONTENT)

        elif request.method == "PATCH":
            """Get action from the request"""
            action = request.data.get("action", None)
            if not action:
                return Response(
                    {"error": "Action is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if action == "activate":
                response = service.activate_candidate(request.user, candidate_id)
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=response.status,
                    )
                return Response(response.data, status=status.HTTP_200_OK)

            elif action == "deactivate":
                response = service.deactivate_candidate(request.user, candidate_id)
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=response.status,
                    )
                return Response(response.data, status=status.HTTP_200_OK)

            else:
                return Response(
                    {"error": "Invalid action"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"error": "Invalid request method"},
                status=status.HTTP_400_BAD_REQUEST,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Failed to retrieve candidates"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def admin_candidates_overview_api_view(request, business_id):
    """Get candidate overview"""
