name: Deploy

on:
  pull_request:
    branches:
      - production

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v2.3.1 
      with:
        python-version: '3.x'

    - name: Deploying to the server
      env:
        GITHUB_TOKEN: ${{ secrets.REPO_TOKEN }}
      run: |
        sshpass -p "${{ secrets.TALENT_SERVER_PASSWORD }}" ssh -o StrictHostKeyChecking=no root@${{ secrets.TALENT_SERVER_IP }} << 'EOF'
          cd /root/talent_connect/talent-connect-backend
          source venv/bin/activate
          python /root/talent_connect/talent-connect-backend/deploy.py
        EOF
