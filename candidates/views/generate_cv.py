from ast import Is
import os
from django.http import FileResponse
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import api_view, permission_classes
from accounts.views.services import verify_user
from candidates.models import Candidate
from candidates.resume_generator import generate_pdf


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def generate_resume_view(request, candidate_id):
    """
    Retrieves the candidate associated with the current user,
    generates a resume PDF using dynamic data from the Candidate model,
    and returns the PDF as a downloadable file.
    """
    try:
        candidate = Candidate.objects.get(id=candidate_id)
    except Candidate.DoesNotExist:
        return Response(
            {"error": "Candidate profile not found."}, status=status.HTTP_404_NOT_FOUND
        )

    # Get PDF buffer from generate_pdf
    pdf_buffer = generate_pdf(candidate)

    # Return PDF as downloadable file
    filename = f"resume_{candidate.user.first_name}.pdf"
    response = FileResponse(pdf_buffer, content_type="application/pdf")
    response["Content-Disposition"] = f'attachment; filename="{filename}"'
    return response
