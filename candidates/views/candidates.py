from datetime import datetime
from unittest import skipIf
from django.contrib.auth.models import User

from django.contrib.auth.hashers import make_password
from django.contrib.auth import login, authenticate

from rest_framework.decorators import api_view, permission_classes, throttle_classes
from rest_framework.throttling import AnonRateThrottle
from django_ratelimit.decorators import ratelimit
from rest_framework.permissions import AllowAny, IsAuthenticated

from rest_framework.response import Response
from rest_framework import status
from base.models import Address
from candidates.models import (
    Skill,
    SkillsCategory,
    WorkExperience,
    AcademicEducation,
    City,
    Candidate,
)
from PIL import Image
from io import BytesIO

import jwt
from django.conf import settings
from core.settings import SECRET_KEY
import jwt
from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
import time


from rest_framework import serializers
from accounts.services.user_services import UserService
from accounts.serializers import UserSerializer
from django_ratelimit.decorators import ratelimit

user_service = UserService()


class SkillSerializer(serializers.ModelSerializer):
    class Meta:
        model = Skill
        fields = "__all__"


# profile picture
def compress_file(file):
    img = Image.open(file)
    max_size_kb = 100
    target_size_bytes = max_size_kb * 1024

    img_io = BytesIO()
    img.save(img_io, format="JPEG", optimize=True, quality=85)
    img_io.seek(0)
    img = Image.open(img_io)

    while img_io.getbuffer().nbytes > target_size_bytes:
        img.save(img_io, format="JPEG", optimize=True, quality=70)
        img_io.seek(0)
        img = Image.open(img_io)


def verify_user(access_token_with_bearer):

    if not isinstance(access_token_with_bearer, str):
        raise TypeError("Access token must be a string")
    try:
        access_token = access_token_with_bearer.split()[1]
        decoded_token = jwt.decode(access_token, SECRET_KEY, algorithms=["HS256"])
        user_id = decoded_token.get("user_id")
        user = User.objects.get(pk=user_id)
        message = "User is authenticated"
        return (user, message)
    except jwt.ExpiredSignatureError:
        user = None
        message = "Token has expired"
        return (user, message)
    except jwt.InvalidTokenError:
        user = None
        message = "Invalid token"
        return (user, message)
    except User.DoesNotExist:
        user = None
        message = "User not found"
        return (user, message)


def generate_username(first_name, last_name):

    username = str(first_name[3] + last_name)
    count = 1

    username = (first_name[0].lower() + last_name).lower()
    count = 1
    while User.objects.filter(username=username).exists():
        username = username + str(count)
        count += 1
    while User.objects.filter(username__iexact=username).exists():
        username = username + str(count)
        count += 1
    return username


def register_user(
    email, password, first_name, last_name, roles=None, logged_in_user=None
):
    user = None
    existing_user = User.objects.filter(email=email).first()
    if existing_user is None:
        try:
            user_data = {
                "first_name": first_name,
                "last_name": last_name,
                "username": email,
                "email": email,
                "password": password,
                "roles": roles or [],
            }
            response = user_service.create_user(user_data, logged_in_user)
            if response.success:
                return (True, "User is created successfully", response.data)
            else:
                return (False, response.message, user)
        except Exception as e:
            return (False, str(e), user)
    else:
        return (False, "Someone with the same email already exist in our system", user)


def register_subscriber(email, password, first_name, last_name):
    user = None
    existing_user = User.objects.filter(email=email).first()
    if existing_user is None:
        try:
            user = User.objects.create(
                first_name=first_name,
                last_name=last_name,
                username=email,
                email=email,
                password=password,
            )
            return (True, "User is created successfully", user)
        except Exception as e:
            return (False, str(e), user)
    else:
        return (False, "Someone with the same email already exist in our system", user)


def create_candidate(user, skills):
    try:
        candidate = Candidate.objects.get(user=user)
        candidate.skills.set(skills)
        return (True, candidate, "Skills are updated successfully")
    except Exception as e:
        return (False, None, str(e))


from accounts.views.services import send_welcome_email


@ratelimit(
    key="ip", rate="10/m", method="POST", block=False
)  # block=False to handle manually
@api_view(["POST"])
@permission_classes([AllowAny])
@ratelimit(key="ip", rate="10/m", method="POST", block=False)
def sign_up_api(request):

    if request.method == "POST":
        signup_data = {
            "email": request.data.get("email"),
            "password": request.data.get("password"),
            "first_name": request.data.get("first_name"),
            "last_name": request.data.get("last_name"),
            "phone_number": request.data.get("phone_number"),
            "gender": request.data.get("gender"),
            "date_of_birth": request.data.get("date_of_birth"),
            "account_type": request.data.get("account_type"),
            "business_name": request.data.get("business_name"),
        }

        response = user_service.sign_up(signup_data)
        serializers = UserSerializer(response.data).data

        if response.success:
            return Response(
                serializers,
                status=status.HTTP_201_CREATED,
            )
        else:
            return Response(
                {"status": response.success, "message": response.message},
                status=status.HTTP_400_BAD_REQUEST,
            )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def update_profile_info(request):
    if request.method != "POST":
        return Response(
            {"status": "failed", "message": "Invalid request method."},
            status=status.HTTP_405_METHOD_NOT_ALLOWED,
        )

    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message}, status=status.HTTP_400_BAD_REQUEST
        )

    profile_data = {
        "location": request.data.get("location"),
        "job_title": request.data.get("job_title"),
        "company_name": request.data.get("company_name"),
        "school_name": request.data.get("school_name"),
        "degree_attained": request.data.get("degree_attained"),
        "started_year": request.data.get("started_year"),
        "ended_year": request.data.get("ended_year"),
    }

    required_fields = [
        "location",
        "school_name",
        "degree_attained",
        "started_year",
        "ended_year",
    ]
    for field in required_fields:
        if not profile_data.get(field):
            return Response(
                {"status": "failed", "message": f"Missing required field: {field}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    try:
        response = user_service.update_profile(user, profile_data)
        if response.success:
            return Response(
                {"status": "success", "message": response.message},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"status": "failed", "message": response.message},
                status=status.HTTP_400_BAD_REQUEST,
            )
    except Exception as e:
        return Response(
            {"status": "failed", "message": f"An error occurred: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# profile location
@api_view(["POST"])
@permission_classes([IsAuthenticated])
def update_profile_location(request):
    if request.method == "POST":
        location = request.data.get("location")
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response({"status": "failed", "message": message}, status=400)
        if location is not None:
            try:
                candidate = Candidate.objects.get(user=user)
                try:
                    candidate.location = location
                    candidate.save()
                    return Response(
                        {
                            "status": "success",
                            "message": "Profile image is updates",
                        },
                        status=status.HTTP_200_OK,
                    )
                except Exception as e:
                    return Response(
                        {
                            "status": "failed",
                            "message": f"We could not update your location: {e}",
                        },
                        status=400,
                    )
            except Candidate.DoesNotExist:
                return Response(
                    {
                        "status": "failed",
                        "message": "We could not find a profile with your details",
                    },
                    status=400,
                )


@api_view(["GET"])
@permission_classes([AllowAny])
@ratelimit(key="ip", rate="10/m", method="GET", block=False)
def get_locations(request):
    if request.method == "GET":
        try:
            addresses = City.objects.all()
            addresses_data = list(addresses.values())
            return Response(
                {
                    "status": "success",
                    "message": "locations are found",
                    "locations": addresses_data,
                }
            )
        except Exception as e:
            return Response(
                {
                    "status": "failed",
                    "message": "Failed to retrieve locations",
                },
                status=status.HTTP_404_NOT_FOUND,
            )


# get skills
@api_view(["GET"])
@permission_classes([AllowAny])
@ratelimit(key="ip", rate="10/m", method="GET", block=False)
def get_skills(request):
    if request.method == "GET":
        try:
            categories = SkillsCategory.objects.all()
            response_data = {"categories": []}
            for category in categories:
                category_data = {"name": category.name, "topics": []}
                skills = Skill.objects.filter(category_name=category)
                for skill in skills:
                    topic_data = {"name": skill.name, "description": skill.description}
                    category_data["topics"].append(topic_data)
                response_data["categories"].append(category_data)
            return Response({"skills": response_data}, status=200)
        except Exception as e:
            print(e)
            return Response(
                {"status": "failed", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )
    else:
        return Response(
            {"status": "failed", "message": "Only gt requests are allowed"},
            status=status.HTTP_403_FORBIDDEN,
        )


# Work experience
@api_view(["POST"])
@permission_classes([IsAuthenticated])
def update_profile_work_experience(request):
    user = request.user
    if request.method == "POST":
        job_title = request.data.get("job_title")
        company_name = request.data.get("company_name")
        access_token = request.headers.get("Authorization")

        user, message = verify_user(access_token)

        if user is None:
            return Response({"status": "failed", "message": message}, status=400)
        if all([job_title, company_name]):
            try:
                candidate = Candidate.objects.get(user=user)
                try:
                    work_experience = WorkExperience.objects.create(
                        name=job_title,
                        company_name=company_name,
                        created_by=user,
                    )
                    try:
                        candidate.work_experience.set([work_experience])
                        candidate.save()
                        return Response(
                            {"status": "success", "message": "Work experience updated"},
                            status=status.HTTP_200_OK,
                        )
                    except Exception as e:
                        print(e)
                        return Response(
                            {
                                "status": "failed",
                                "message": f"We could not update your work experience: {e}",
                            },
                            status.HTTP_400_BAD_REQUEST,
                        )
                except Exception as e:
                    print(e)
                    return Response(
                        {
                            "status": "failed",
                            "message": f"We could not save your work: {e}",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            except Candidate.DoesNotExist:
                return Response(
                    {
                        "status": "failed",
                        "message": "We could not find a profile with your details",
                    },
                    status=400,
                )
        else:

            return Response(
                {"status": "failed", "message": "All fields are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )


# Academic education
@api_view(["POST"])
@permission_classes([IsAuthenticated])
def update_profile_academic_education(request):
    user = request.user
    if request.method == "POST":
        school_name = request.data.get("school_name")
        degree_attained = request.data.get("degree_attained")
        started_year = request.data.get("started_year")
        ended_year = request.data.get("ended_year")
        access_token = request.headers.get("Authorization")

        print(school_name, degree_attained, started_year, ended_year)
        user, message = verify_user(access_token)
        if user is None:
            return Response({"status": "failed", "message": message}, status=400)
        if all([school_name, degree_attained, started_year, ended_year]):
            try:
                candidate = Candidate.objects.get(user=user)
                try:
                    academic_education = AcademicEducation.objects.create(
                        name=school_name,
                        degree_attained=degree_attained,
                        started_year=started_year,
                        ended_year=ended_year,
                        created_by=user,
                    )

                    try:
                        candidate.academic_education.set([academic_education])
                        candidate.save()
                        from accounts.views.services import send_profile_complete_email

                        send_profile_complete_email(
                            recipient_email=user.email,
                            first_name=user.first_name,
                            profile_link=f"{settings.DOMAIN_NAME}/profile/",
                        )
                        return Response(
                            {"status": "success", "message": "Education info updated"},
                            status=status.HTTP_200_OK,
                        )
                    except Exception as e:
                        return Response(
                            {
                                "status": "failed",
                                "message": f"We could not update your work academic education: {e}",
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                except Exception as e:
                    return Response(
                        {
                            "status": "failed",
                            "message": f"We could not save your academic: {e}",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            except Candidate.DoesNotExist:
                return Response(
                    {
                        "status": "failed",
                        "message": "We could not find a profile with your details",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"status": "failed", "message": "All fields are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def update_profile_skills(request):

    if request.method == "POST":
        skills = request.data.get("skills")
        access_token = request.headers.get("Authorization")

        user, message = verify_user(access_token)
        if user is None:
            return Response({"status": "failed", "message": message}, status=400)
        if skills is not None:

            skills_ids = []
            for skill in skills:
                try:
                    skill_obj = Skill.objects.get(name=skill).pk
                    skills_ids.append(skill_obj)

                except Skill.DoesNotExist:
                    continue
            try:
                candidate = Candidate.objects.get(user=user)
                candidate.skills.set(skills_ids)
                return Response(
                    {"status": "success", "message": "Skills are update successfully"},
                    status=status.HTTP_200_OK,
                )
            except City.DoesNotExist:
                return Response(
                    {
                        "status": "failed",
                        "message": "The city name is wrong or we do not support that city",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        else:
            return Response(
                {"status": "failed", "message": "All fields are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    # if user.is_authenticated:
    #     else:
    #     return Response({"status": "failed", "message": "You must me logged in"})


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def save_skills_api(request):
    if request.method == "POST":
        data = request.data.get("categories", [])
        # print("data", data)
        for category_data in data:
            category_name = category_data.get("name")
            topics = category_data.get("topics", [])
            skill_category = SkillsCategory.objects.get(name=category_name)
            for topic in topics:
                skill_name = topic.get("name")
                skill_description = topic.get("description")
                try:
                    skill = Skill.objects.create(
                        name=skill_name,
                        description=skill_description,
                        category=skill_category,
                    )
                except Exception as e:
                    print(e)
            print("categories", category_name)
            print("topics", topics)
    return None


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def update_profile_files(request, format=None):
    if request.method == "POST":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response({"status": "failed", "message": message}, status=400)
        try:
            candidate = Candidate.objects.get(user=user)
            try:
                candidate.profile_picture = request.data["profile_image"]
                candidate.resume = request.data["cv_file"]
                candidate.save()
                return Response(
                    {"status": "success", "message": "profile updated"},
                    status=status.HTTP_200_OK,
                )
            except Exception as e:
                print(e)
                return Response(
                    {
                        "status": "failed",
                        "message": f"We could not save your image {e}",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except Exception as e:
            return Response(
                {"status": "failed", "message": f"We could not save your image {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
