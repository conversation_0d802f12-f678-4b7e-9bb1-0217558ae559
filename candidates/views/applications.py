from uuid import UUID
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from accounts.services.auth import PermissionDecorators
from accounts.views.services import verify_user
from candidates.services import candidate
from jobs.models import *
from candidates.models import Application, Candidate
from candidates.views.serializers import ApplicationSerializer, GetApplicationSerializer
from datetime import datetime
from candidates.services.application import ApplicationService
from jobs.services.job_service import JobService
from candidates.services.candidate import CandidateService


def validate_uuid(id):
    try:
        UUID(id, version=4)
        return True
    except ValueError:
        return False


service = ApplicationService()
jobService = JobService()
candidateService = CandidateService()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@PermissionDecorators.is_creator(Candidate, fallback_to_user=True)
def get_applications(request): 
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_403_FORBIDDEN,
        )
    applications = service.get_all_applications(user_id=user.id)
    if not applications.success:
        return Response(applications.message, status=status.HTTP_400_BAD_REQUEST)
    applications_data = GetApplicationSerializer(applications.data, many=True).data
    return Response({"applications": applications_data}, status=status.HTTP_200_OK)



@api_view(["DELETE"])
@permission_classes([IsAuthenticated])
@PermissionDecorators.is_creator(Application, lookup_field="id", url_param_names=["application_id"])
def delete_application(request, application_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_403_FORBIDDEN,
        )
    application = service.delete_application(application_id)
    if not application.success:
        return Response(
            {"error": application.message}, status=status.HTTP_400_BAD_REQUEST
        )
    return Response({"message": application.message}, status=status.HTTP_204_NO_CONTENT)


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def save_application(request, job_slug):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_403_FORBIDDEN,
        )
    response = service.create_application(job_slug=job_slug, user_id=user.id)
    if not response.success:
        return Response({"error": response.message}, status=status.HTTP_400_BAD_REQUEST)
    serialized_data = ApplicationSerializer(response.data)
    return Response(serialized_data.data, status=status.HTTP_201_CREATED)


@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
def update_application(request, application_id):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_403_FORBIDDEN,
        )
    response = service.update_application(application_id, request.data)
    serializer = GetApplicationSerializer(response.data)
    if not response.success:
        return Response({"error": response.message}, status=status.HTTP_400_BAD_REQUEST)
    return Response(serializer.data, status=status.HTTP_200_OK)
