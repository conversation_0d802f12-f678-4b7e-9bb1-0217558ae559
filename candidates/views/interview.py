from rest_framework.response import Response
from rest_framework import status
from rest_framework.decorators import permission_classes, api_view
from rest_framework.permissions import IsAuthenticated
import time
from accounts.views.services import verify_user
from jobs.models import *
from candidates.models import Application, Interview
from datetime import time as interview_time

from candidates.views.serializers import InterviewSerializer


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def new_interview(request):
    if request.method == "POST":
        # get user and verify a user
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_400_BAD_REQUEST,
            )

        application_id = request.data.get("application_id")
        date = request.data.get("date")
        from_time = request.data.get("from")
        to_time = request.data.get("to")
        interview_type = request.data.get("interview_type")
        interview_place = request.data.get("interview_place")
        message = request.data.get("message")

        required_fields = [application_id, date, from_time, interview_place]
        required_fields = [application_id, date, from_time, interview_place]
        missing_fields = [field for field in required_fields if not field]

        time.sleep(3)
        if not application_id:
            return Response(
                {"message": "Application id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        elif not date:
            return Response(
                {"message": "date is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        elif not from_time:
            return Response(
                {"message": "interview time is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        elif not interview_place:
            return Response(
                {"message": "interview place is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        parsed_to_time = interview_time.fromisoformat(str(to_time).replace(" am", ""))
        to_time = parsed_to_time.strftime("%H:%M")

        parsed_from_time = interview_time.fromisoformat(
            str(from_time).replace(" am", "")
        )
        from_time = parsed_from_time.strftime("%H:%M")

        try:
            application = Application.objects.get(id=application_id)
            job_object = application.job_applied
            business = job_object.company_name  # business interview

            if Interview.objects.filter(
                user=user, business=business, status="Upcoming"
            ).exists():
                return Response(
                    {"message": "There is an interview scheduled already"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            else:
                new_interview = Interview.objects.create(
                    user=user,
                    business=business,
                    interview_date=date,
                    from_time=from_time,
                    to_time=to_time,
                    location=interview_place,
                    message=message,
                )
                saved_interview = InterviewSerializer(new_interview)

                return Response(
                    {
                        "message": f"Interview is schedules at {date}, {from_time}",
                        "interview": saved_interview.data,
                    },
                    status=status.HTTP_201_CREATED,
                )
        except Application.DoesNotExist:
            return Response(
                {"message": "No Application with that id found"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            print(e)
            return Response(
                {"message": f"Something is not right: {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
