from django.contrib.auth.models import User
from rest_framework import serializers
from base.serializers import (
    GetLocationSerializer,
    HumanReadableDateField,
    HumanReadableTimeField,
)
from businesses.models import Company
from candidates.models import *
from jobs.models import *


# Base User Serializer
class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["first_name", "last_name", "email"]


# Skills Related Serializers
class SkillCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Skill
        fields = ["name"]


class SkillSerializer(serializers.ModelSerializer):
    category_name = SkillCategorySerializer()

    class Meta:
        model = Skill
        fields = ["name", "category_name"]


# Language Serializers
class LanguageSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserLanguage
        fields = "__all__"


# Education Serializers
class AcademicEducationSerializer(serializers.ModelSerializer):
    class Meta:
        model = AcademicEducation
        fields = "__all__"


class GetAcademicEducationSerializer(serializers.ModelSerializer):
    created_by = UserSerializer()

    class Meta:
        model = AcademicEducation
        fields = "__all__"


# Work Experience Serializers
class WorkExperienceSerializer(serializers.ModelSerializer):
    class Meta:
        model = WorkExperience
        fields = "__all__"


class GetWorkExperienceSerializer(serializers.ModelSerializer):
    created_by = UserSerializer()
    started_from = HumanReadableDateField()
    ended_at = HumanReadableDateField()

    class Meta:
        model = WorkExperience
        fields = "__all__"


# Social Media Serializers
class SocialMediaPlatformsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SocialMediaPlatforms
        fields = "__all__"


# Business Serializers
class BusinessSerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = "__all__"


# Job Related Serializers
class WorkTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = WorkType
        fields = ["name"]


class JobTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = JobType
        fields = ["name"]


class JobSerializer(serializers.ModelSerializer):
    company_name = BusinessSerializer()

    class Meta:
        model = Job
        fields = "__all__"


# Candidate Serializers
class CandidateSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    skills = SkillSerializer(many=True)
    languages = LanguageSerializer(many=True)

    class Meta:
        model = Candidate
        fields = "__all__"


class GetCandidateSerializer(serializers.ModelSerializer):
    date_of_birth = HumanReadableDateField()
    date_created = HumanReadableDateField()
    started_working = HumanReadableDateField()
    created_by = UserSerializer()
    user = UserSerializer()
    work_experience = GetWorkExperienceSerializer(many=True)
    academic_education = GetAcademicEducationSerializer(many=True)
    languages = LanguageSerializer(many=True)
    skills = SkillSerializer(many=True)
    location = GetLocationSerializer()

    class Meta:
        model = Candidate
        fields = "__all__"


class CandidateDataSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    academic_education = AcademicEducationSerializer(many=True)
    skills = SkillSerializer(many=True)
    work_experience = WorkExperienceSerializer(many=True)
    languages = LanguageSerializer(many=True)
    social_platforms = SocialMediaPlatformsSerializer(many=True)

    class Meta:
        model = Candidate
        fields = "__all__"


# Interview Serializers
class InterviewSerializer(serializers.ModelSerializer):
    class Meta:
        model = Interview
        fields = "__all__"


class GetInterviewSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    created_by = UserSerializer()
    from_time = HumanReadableTimeField()
    to_time = HumanReadableTimeField()
    interview_date = HumanReadableDateField()
    business = BusinessSerializer()
    job_applied = JobSerializer()

    class Meta:
        model = Interview
        fields = "__all__"


class InterviewDataSerializer(serializers.ModelSerializer):
    business = BusinessSerializer()

    class Meta:
        model = Interview
        fields = ["interview_date", "location", "status", "date_created", "business"]


# Application Serializers
class ApplicationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Application
        fields = "__all__"


class GetApplicationSerializer(serializers.ModelSerializer):
    created_by = UserSerializer()
    date_created = HumanReadableDateField()
    applicant = GetCandidateSerializer()
    job_applied = JobSerializer()

    class Meta:
        model = Application
        fields = "__all__"


# Chat Serializers
class ChatSerializer(serializers.ModelSerializer):
    class Meta:
        model = Chat
        fields = "__all__"


class GetChatSerializer(serializers.ModelSerializer):
    sender = UserSerializer()
    receiver = UserSerializer()

    class Meta:
        model = Chat
        fields = "__all__"
