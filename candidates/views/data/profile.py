from django.contrib.auth.models import User
from django.contrib.auth.models import User

from rest_framework.response import Response
from rest_framework import status
from accounts.services.auth import PermissionDecorators
from rest_framework.permissions import IsAuthenticated
from base.models import Address
from accounts.views.services import (
    get_profile_data,
    verify_user,
)
from candidates.models import (
    Skill,
    Candidate,
)

from rest_framework import serializers


class SkillSerializer(serializers.ModelSerializer):
    class Meta:
        model = Skill
        fields = "__all__"


from candidates.models import (
    Candidate,
    Skill,
)

from rest_framework.decorators import api_view, permission_classes


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def candidate_data(request):
    if request.method == "GET":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response({"status": "failed", "message": message}, status=403)
        try:
            candidate = get_profile_data(user)
            return Response(candidate, status=status.HTTP_200_OK)
        except Candidate.DoesNotExist:
            return Response(
                {"status": "fail", "message": "Candidate has no profile data"},
                status=status.HTTP_404_NOT_FOUND,
            )
