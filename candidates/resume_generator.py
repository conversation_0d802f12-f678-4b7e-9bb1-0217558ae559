from fpdf import FPDF
import io

GOLDEN_BG = (223, 201, 134)
DARK_BROWN = (65, 59, 26)
GRAY_TEXT = (83, 83, 83)
LIGHT_GRAY = (180, 180, 180)
LINE_THICKNESS = 0.5
PAGE_MARGIN = 15
LEFT_COL_WIDTH = 80
RIGHT_COL_WIDTH = 115
COLUMN_GAP = 10
VLINE_X = PAGE_MARGIN + LEFT_COL_WIDTH + COLUMN_GAP / 2
BULLET = "-"
FOOTER_HEIGHT = 10
SECTION_SPACING = 6
LINE_HEIGHT = 6
HEADER_SPACING = 4
LINE_TO_HEADER_SPACING = 4

class PDF(FPDF):
    def footer(self):
        footer_text = "Proudly generated by <PERSON><PERSON>atch"
        self.set_font("Helvetica", "", 8)
        self.set_text_color(*LIGHT_GRAY)
        text_width = self.get_string_width(footer_text)
        x = (self.w - text_width) / 2
        self.set_y(-FOOTER_HEIGHT)
        self.cell(text_width, FOOTER_HEIGHT, footer_text, align="C")

    def draw_section_header(self, text, x, y, width):
        self.set_xy(x, y)
        self.set_font("Helvetica", "B", 12)
        self.set_text_color(*DARK_BROWN)
        self.cell(width, 8, text.upper(), 0, 1)
        return self.get_y() + HEADER_SPACING

    def draw_horizontal_line(self, start_x, end_x, y, draw_circle=False):
        circle_radius = 1.5
        self.set_line_width(LINE_THICKNESS)
        self.set_draw_color(*LIGHT_GRAY)
        self.line(start_x, y, end_x, y)
        
        if draw_circle:
            self.set_fill_color(*GOLDEN_BG)
            self.ellipse(start_x - circle_radius, y - circle_radius, 
                        circle_radius * 2, circle_radius * 2, style="F")
            self.ellipse(end_x - circle_radius, y - circle_radius, 
                        circle_radius * 2, circle_radius * 2, style="F")

    def draw_vertical_line(self, x, start_y, end_y):
        self.set_line_width(LINE_THICKNESS)
        self.set_draw_color(*LIGHT_GRAY)
        self.line(x, start_y, x, end_y)

    def add_page_header(self, candidate):
        header_height = 40
        header_start_y = 0
        header_end_y = header_start_y + header_height
        center_x = self.w / 2

        self.set_fill_color(*GOLDEN_BG)
        self.rect(0, header_start_y, self.w, header_height, style="F")
        
        full_name = f"{candidate.user.first_name} {candidate.user.last_name}"
        self.set_font("Helvetica", "B", 18)
        self.set_text_color(*DARK_BROWN)
        name_width = self.get_string_width(full_name)
        name_y = header_start_y + (header_height / 2) - 4
        name_x = center_x - name_width / 2
        self.set_xy(name_x, name_y)
        self.cell(name_width, 10, full_name, align="C")
        
        line_y = name_y + 5
        line_margin = 15
        circle_radius = 1.5
        left_line_start_x = 0
        left_line_end_x = name_x - line_margin
        right_line_start_x = name_x + name_width + line_margin
        right_line_end_x = self.w

        self.set_line_width(LINE_THICKNESS)
        self.set_draw_color(*DARK_BROWN)
        self.line(left_line_start_x, line_y, left_line_end_x, line_y)
        self.line(right_line_start_x, line_y, right_line_end_x, line_y)
        
        self.set_fill_color(*DARK_BROWN)
        self.ellipse(left_line_end_x - circle_radius, line_y - circle_radius, 
                    circle_radius * 2, circle_radius * 2, style="F")
        self.ellipse(right_line_start_x - circle_radius, line_y - circle_radius, 
                    circle_radius * 2, circle_radius * 2, style="F")

        return header_end_y + 5

    def add_left_column(self, x, y, candidate):
        internal_pad = 5
        start_x = x + internal_pad
        current_y = y

        # CONTACT section
        current_y = self.draw_section_header("CONTACT", start_x, current_y, LEFT_COL_WIDTH - internal_pad)
        self.set_xy(start_x, current_y)
        address = (candidate.location.street_address + ", " +
                  candidate.location.city_name.name) if candidate.location else "N/A"
        contact_info = [
            ("Phone:", candidate.phone_number or "N/A"),
            ("Email:", candidate.user.email),
            ("Address:", address),
            ("Website:", candidate.portfolio_url or "N/A")
        ]
        
        self.set_text_color(*GRAY_TEXT)
        label_indent = 2
        for label, value in contact_info:
            self.set_x(start_x)
            self.set_font("Helvetica", "", 10)
            label_width = self.get_string_width(label)
            self.cell(label_width, LINE_HEIGHT, label, 0, 0)
            value_x = start_x + label_width + label_indent
            self.set_xy(value_x, self.get_y())
            self.multi_cell(LEFT_COL_WIDTH - internal_pad - label_width - label_indent, 
                          LINE_HEIGHT, str(value), 0, 'L')
        current_y = self.get_y() + 4
        self.draw_horizontal_line(x, VLINE_X - COLUMN_GAP / 2, current_y, draw_circle=True)
        current_y += LINE_TO_HEADER_SPACING

        # EDUCATION section
        current_y = self.draw_section_header("EDUCATION", start_x, current_y, LEFT_COL_WIDTH - internal_pad)
        self.set_xy(start_x, current_y)
        self.set_text_color(*GRAY_TEXT)
        for edu in candidate.academic_education.all():
            dates = f"{edu.started_year} - {edu.ended_year}"
            self.set_font("Helvetica", "", 10)
            self.set_x(start_x)
            self.cell(LEFT_COL_WIDTH - internal_pad, LINE_HEIGHT, dates, 0, 1, 'L')
            self.set_font("Helvetica", "B", 10)
            self.set_x(start_x)
            self.cell(LEFT_COL_WIDTH - internal_pad, LINE_HEIGHT, str(edu.school_name), 0, 1, 'L')
            if edu.degree_attained:
                self.set_x(start_x)
                self.multi_cell(LEFT_COL_WIDTH - internal_pad, LINE_HEIGHT, f"{BULLET} {edu.degree_attained}", 0, 'L')
            current_y = self.get_y() + 3

        current_y = self.get_y() + 4
        self.draw_horizontal_line(x, VLINE_X - COLUMN_GAP / 2, current_y, draw_circle=True)
        current_y += LINE_TO_HEADER_SPACING

        # SKILLS section
        current_y = self.draw_section_header("SKILLS", start_x, current_y, LEFT_COL_WIDTH - internal_pad)
        self.set_xy(start_x, current_y)
        self.set_font("Helvetica", "", 10)
        self.set_text_color(*GRAY_TEXT)
        skills_text = "\n".join([f"{BULLET} {skill.name}" for skill in candidate.skills.all()])
        self.multi_cell(LEFT_COL_WIDTH - internal_pad, LINE_HEIGHT, skills_text, 0, 'L')
        current_y = self.get_y() + 4
        self.draw_horizontal_line(x, VLINE_X - COLUMN_GAP / 2, current_y, draw_circle=True)
        current_y += LINE_TO_HEADER_SPACING
        
        # LANGUAGES section
        current_y = self.draw_section_header("LANGUAGES", start_x, current_y, LEFT_COL_WIDTH - internal_pad)
        self.set_xy(start_x, current_y)
        self.set_font("Helvetica", "", 10)
        self.set_text_color(*GRAY_TEXT)
        languages_text = "\n".join([f"{BULLET} {lang.name}" for lang in candidate.languages.all()])
        self.multi_cell(LEFT_COL_WIDTH - internal_pad, LINE_HEIGHT, languages_text, 0, 'L')

    def add_right_column(self, x, y, candidate):
        internal_pad = 5
        section_spacing = 8
        right_margin = 20
        start_x = x + internal_pad
        current_y = y
        col_width = RIGHT_COL_WIDTH - internal_pad - right_margin
        right_boundary = x + RIGHT_COL_WIDTH - right_margin

        # PROFILE SUMMARY section
        current_y = self.draw_section_header("PROFILE SUMMARY", start_x, current_y, col_width)
        self.set_xy(start_x, current_y)
        self.set_font("Helvetica", "", 10)
        self.set_text_color(*GRAY_TEXT)
        profile_summary = candidate.bio or "No profile summary available."
        self.multi_cell(col_width, LINE_HEIGHT, profile_summary, align="L")
        current_y = self.get_y() + 4
        self.draw_horizontal_line(VLINE_X + COLUMN_GAP / 2, right_boundary, current_y, draw_circle=True)
        current_y += SECTION_SPACING

        # WORK EXPERIENCE section
        current_y = self.draw_section_header("WORK EXPERIENCE", start_x, current_y, col_width)
        self.set_text_color(*GRAY_TEXT)
        for job in candidate.work_experience.all():
            job_start_y = current_y
            self.set_xy(start_x, current_y)
            self.set_font("Helvetica", "B", 10)
            
            started = job.started_from.strftime("%Y") if job.started_from else "N/A"
            ended = job.ended_at.strftime("%Y") if job.ended_at else "Present"
            dates = f"{started} - {ended}"
            company = job.company_name or "Company N/A"
            employment_title = job.employment_title or "Title N/A"
            
            date_width = self.get_string_width(dates) + 5
            company_width = col_width - date_width - 3
            self.cell(company_width, LINE_HEIGHT, company, align="L")
            
            date_x = right_boundary - date_width
            self.set_xy(date_x, job_start_y)
            self.cell(date_width, LINE_HEIGHT, dates, align="R")
            
            self.set_xy(start_x, self.get_y() + 5)
            self.set_font("Helvetica", "", 10)
            self.multi_cell(col_width, LINE_HEIGHT, employment_title, align="L")
            current_y = self.get_y() + 3

def generate_pdf(candidate):
    pdf = PDF(orientation="P", unit="mm", format="A4")
    pdf.set_auto_page_break(auto=True, margin=PAGE_MARGIN + FOOTER_HEIGHT)
    pdf.set_margins(PAGE_MARGIN, PAGE_MARGIN, PAGE_MARGIN)
    pdf.add_page()
    
    start_y = pdf.add_page_header(candidate)
    left_col_x = PAGE_MARGIN
    right_col_x = PAGE_MARGIN + LEFT_COL_WIDTH + COLUMN_GAP

    pdf.add_left_column(left_col_x, start_y, candidate)
    max_y_left = pdf.get_y()
    pdf.add_right_column(right_col_x, start_y, candidate)
    max_y_right = pdf.get_y()
    max_y = max(max_y_left, max_y_right)

    vertical_line_start_y = start_y - 2
    vertical_line_end_y = max_y + 5
    pdf.draw_vertical_line(VLINE_X, vertical_line_start_y, vertical_line_end_y)

    buffer = io.BytesIO()
    pdf.output(buffer)
    buffer.seek(0)
    return buffer
