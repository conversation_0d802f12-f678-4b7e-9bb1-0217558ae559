from candidates.models import WorkExperience
from django.db import transaction
from candidates.repositories.application_repository import RepositoryResponse


class WorkExperienceRepository:
    """Create a new work experience"""
    @transaction.atomic
    def create_work_experience(self, data) -> RepositoryResponse:
        """
        Create a new work experience for a candidate.
        """
        try:
            work_experience = WorkExperience.objects.create(**data)
            return RepositoryResponse(
                success=True,
                data=work_experience,
                message="Work experience created successfully",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to create work experience: {str(e)}"
            )
    
    """ get work experience by id"""
    def get_work_experience_by_id(self, id) -> RepositoryResponse:
        """
        Get a work experience by id.
        """
        try:
            work_experience = WorkExperience.objects.get(pk=id)
            return RepositoryResponse(
                success=True,
                data=work_experience,
                message="Work experience retrieved successfully",
            )
        except WorkExperience.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Work experience not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to retrieve work experience: {str(e)}"
            )
    
    """Update a work experience"""
    @transaction.atomic
    def update_work_experience(self, id, data) -> RepositoryResponse:
        """
        Update an existing work experience.
        """
        try:
            work_experience = WorkExperience.objects.get(pk=id)
            for key, value in data.items():
                setattr(work_experience, key, value)
            work_experience.save()
            return RepositoryResponse(
                success=True,
                data=work_experience,
                message="Work experience updated successfully",
            )
        except WorkExperience.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Work experience not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to update work experience: {str(e)}"
            )
    
    """Delete a work experience"""
    @transaction.atomic
    def delete_work_experience(self, id) -> RepositoryResponse:
        """
        Delete an existing work experience.
        """
        try:
            work_experience = WorkExperience.objects.get(pk=id)
            work_experience.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Work experience deleted successfully",
            )
        except WorkExperience.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Work experience not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to delete work experience: {str(e)}"
            )
