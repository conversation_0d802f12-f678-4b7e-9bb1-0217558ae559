from base.services.logging import LoggingService
from candidates.models import Candidate
from django.db import transaction
from candidates.repositories.application_repository import RepositoryResponse


logging_service = LoggingService()


class CandidateRepository:
    """Create a candidate"""

    @transaction.atomic
    # we need to implement updating the cache here
    def create_candidate(self, data):
        try:
            if Candidate.objects.filter(user=data["user"]).exists():
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="Candidate for this user already exists",
                )
            candidate = Candidate.objects.create(**data)
            return RepositoryResponse(
                success=True,
                data=candidate,
                message="Candidate created successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Error creating candidate",
            )

    """Update a candidate"""

    @transaction.atomic
    def update_candidate(self, candidate_id, data):
        try:
            candidate = Candidate.objects.get(pk=candidate_id)
            for key, value in data.items():
                setattr(candidate, key, value)
            candidate.save()
            return RepositoryResponse(
                success=True,
                data=candidate,
                message="Candidate updated successfully",
            )
        except Candidate.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Candidate not found",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="An error occurred",
            )

    """Delete a candidate"""

    @transaction.atomic
    def delete_candidate(self, candidate_id):
        try:
            candidate = Candidate.objects.get(id=candidate_id)
            candidate.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Candidate deleted successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="An error occurred while",
            )

    """Get a candidate by id"""

    def get_candidate(self, candidate_id):
        try:
            candidate = Candidate.objects.get(id=candidate_id)
            return RepositoryResponse(
                success=True,
                data=candidate,
                message="Candidate retrieved successfully",
            )
        except Candidate.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Candidate does not exist",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to retrieve candidate",
            )

    def get_candidate_by_user_id(self, user_id):
        try:
            candidate = Candidate.objects.get(user_id=user_id)
            return RepositoryResponse(
                success=True,
                data=candidate,
                message="Candidate retrieved successfully",
            )
        except Candidate.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Candidate does not exist",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to retrieve candidate",
            )

    def get_candidate_by_slug(self, slug):
        try:
            candidate = Candidate.objects.get(slug=slug)
            return RepositoryResponse(
                success=True,
                data=candidate,
                message="Candidate retrieved successfully",
            )
        except Candidate.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Candidate does not exist",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to retrieve candidate",
            )

    """Get all candidates"""

    def get_all_candidates(
        self,
    ):
        # we need to implement getting users from the cache first
        try:
            candidates = Candidate.objects.all()
            return RepositoryResponse(
                success=True,
                data=candidates,
                message="All candidates retrieved successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to retrieve candidates",
            )

    "Activate candidate"

    def activate_candidate(self, candidate_id):
        try:
            candidate = Candidate.objects.get(id=candidate_id)
            candidate.is_active = True
            candidate.save()
            return RepositoryResponse(
                success=True,
                data=candidate,
                message="Candidate activated successfully",
            )
        except Candidate.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Candidate does not exist",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to activate candidate",
            )

    "Deactivate candidate"

    def deactivate_candidate(self, candidate_id):
        try:
            candidate = Candidate.objects.get(id=candidate_id)
            candidate.is_active = False
            candidate.save()
            return RepositoryResponse(
                success=True,
                data=candidate,
                message="Candidate deactivated successfully",
            )
        except Candidate.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Candidate does not exist",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to deactivate candidate",
            )
