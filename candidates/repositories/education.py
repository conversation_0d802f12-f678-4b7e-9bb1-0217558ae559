from candidates.models import AcademicEducation
from django.db import transaction
from candidates.repositories.application_repository import RepositoryResponse


class AcademicEducationRepository:
    """Create a new academic education"""

    def create_academic_education(self, data) -> RepositoryResponse:
        """
        Create a new academic education for a candidate.
        """
        try:
            academic_education = AcademicEducation.objects.create(**data)
            return RepositoryResponse(
                success=True,
                data=academic_education,
                message="Academic education created successfully",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to create academic education: {str(e)}",
            )

    """ get academic education by id"""

    def get_academic_education_by_id(self, id) -> RepositoryResponse:
        """
        Get a academic education by id.
        """
        try:
            academic_education = AcademicEducation.objects.get(pk=id)
            return RepositoryResponse(
                success=True,
                data=academic_education,
                message="Academic education retrieved successfully",
            )
        except AcademicEducation.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Academic education not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to retrieve academic education: {str(e)}",
            )

    """Update an academic education"""

    @transaction.atomic
    def update_academic_education(self, id, data) -> RepositoryResponse:
        """
        Update an existing academic education.
        """
        try:
            academic_education = AcademicEducation.objects.get(pk=id)
            for key, value in data.items():
                setattr(academic_education, key, value)
            academic_education.save()
            return RepositoryResponse(
                success=True,
                data=academic_education,
                message="Academic education updated successfully",
            )
        except AcademicEducation.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Academic education not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to update academic education: {str(e)}",
            )

    """Delete an academic education"""

    @transaction.atomic
    def delete_academic_education(self, id) -> RepositoryResponse:
        """
        Delete an existing academic education.
        """
        try:
            academic_education = AcademicEducation.objects.get(pk=id)
            academic_education.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Academic education deleted successfully",
            )
        except AcademicEducation.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Academic education not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to delete academic education: {str(e)}",
            )
