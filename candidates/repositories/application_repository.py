from base.services.logging import LoggingService
from candidates.models import Application, Candidate
from dataclasses import dataclass
from typing import Union, Optional, Dict, List
from django.db.models import Model
from django.db import transaction


logging_service = LoggingService()


@dataclass
class RepositoryResponse:
    success: bool
    message: str
    data: Optional[Union[Application, Dict, List, Model]]


class ApplicationRepository:
    def get_all_applications(self, user_id: int) -> RepositoryResponse:
        """
        Get all applications for a candidate.
        """
        # We should get the applications from the cache first
        try:
            candidate = Candidate.objects.get(user=user_id)
            if not candidate:
                return RepositoryResponse(
                    success=False, message="Candidate not found", data=None
                )
            applications = Application.objects.filter(applicant=candidate)
            return RepositoryResponse(
                success=True,
                message="Applications retrieved successfully",
                data=applications,
            )
        except Candidate.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Candidate does not exist",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Failed to retrieve applications",
                data=None,
            )

    def get_application(self, application_id: int) -> RepositoryResponse:
        """
        Get a single application by its ID.
        """
        try:
            application = Application.objects.get(id=application_id)
            return RepositoryResponse(
                success=True,
                message="Application retrieved successfully",
                data=application,
            )
        except Application.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Application not found",
                data=None,
            )
    
    def get_applications_by_job(self, job_applied):
        """
        Get applications by job applied.
        """
        try:
            applications = Application.objects.filter(job_applied=job_applied)
            return RepositoryResponse(
                success=True,
                message="Applications retrieved successfully",
                data=applications,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Failed to retrieve applications by job",
                data=None,
            )

    # update the cache
    def create_application(self, data):
        """
        Create a new application for a candidate.
        """
        # update the cache
        with transaction.atomic():
            try:
                required_fields = ["applicant", "job_applied"]
                missing_fields = logging_service.check_required_fields(
                    data, required_fields
                )
                if missing_fields:
                    return RepositoryResponse(
                        success=False,
                        message=missing_fields,
                        data=None,
                    )
                application = Application.objects.create(**data)
                job = application.job_applied
                job.applicants += 1
                job.save()
                return RepositoryResponse(
                    success=True,
                    message="Application created successfully",
                    data=application,
                )
            except Exception as e:
                logging_service.log_error(e)
                return RepositoryResponse(
                    success=False,
                    message="Failed to create application",
                    data=None,
                )

    # update the cache
    def update_application(self, application_id, data):
        """
        Update an existing application.
        """
        # update the cache
        with transaction.atomic():
            try:
                application = Application.objects.get(id=application_id)
                valid_fields = [f.name for f in Application._meta.fields]

                for key, value in data.items():
                    if key not in valid_fields:
                        return RepositoryResponse(
                            success=False,
                            message=f"Invalid field: {key}",
                            data=None,
                        )
                    setattr(application, key, value)
                application.save()
                return RepositoryResponse(
                    success=True,
                    message="Application updated successfully",
                    data=application,
                )
            except Application.DoesNotExist:
                return RepositoryResponse(
                    success=False,
                    message="Application not found",
                    data=None,
                )
    
    def update_application_status(self, application_id, status):
        """ Updates the status of a given application"""
        with transaction.atomic():
            try:
                application = Application.objects.get(id=application_id)
                application.status = status
                application.save()
                return RepositoryResponse(
                    success=True,
                    message="Application status updated successfully",
                    data=application,
                )
            except Application.DoesNotExist:
                return RepositoryResponse(
                    success=False,
                    message="Application not found",
                    data=None,
                )
            except Exception as e:
                logging_service.log_error(e)
                return RepositoryResponse(
                    success=False,
                    message="Failed to update application status",
                    data=None,
                )


    # update the cache
    def delete_application(self, application_id):
        """
        Delete an existing application.
        """
        # update the cache
        with transaction.atomic():
            try:
                application = Application.objects.get(id=application_id)
                job = application.job_applied
                if job:
                    job.applicants -= 1
                    job.save()
                application.delete()
                return RepositoryResponse(
                    success=True,
                    message="Application deleted successfully",
                    data=None,
                )
            except Application.DoesNotExist:
                return RepositoryResponse(
                    success=False,
                    message="Application not found",
                    data=None,
                )
