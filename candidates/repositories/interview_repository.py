from base.services.logging import LoggingService
from candidates.models import Interview
from dataclasses import dataclass
from typing import Union, Optional, Dict, List
from django.db.models import Model
from django.db import transaction
from candidates.repositories.application_repository import RepositoryResponse


logging_service = LoggingService()


class InterviewRepository:
    """Create a new interview"""

    @transaction.atomic
    def create_interview(self, data):
        # we need to implement updating the cache here
        try:
            interview = Interview.objects.create(**data)
            return RepositoryResponse(
                success=True,
                data=interview,
                message="Interview created successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error creating interview",
                data=None,
            )

    """Update an existing interview"""

    @transaction.atomic
    def update_interview(self, interview_id, data):
        try:
            interview = Interview.objects.get(pk=interview_id)
            for key, value in data.items():
                setattr(interview, key, value)
            return RepositoryResponse(
                success=True,
                data=interview,
                message="Interview updated successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error updating interview",
                data=None,
            )

    """Delete an interview"""

    @transaction.atomic
    def delete_interview(self, interview_id):
        try:
            interview = Interview.objects.get(pk=interview_id)
            interview.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Interview deleted successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error deleting interview",
                data=None,
            )

    """Get an interview by id"""

    def get_interview(self, interview_id):
        try:
            interview = Interview.objects.get(pk=interview_id)
            return RepositoryResponse(
                success=True,
                data=interview,
                message="Interview retrieved successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error occured while retrieving interview",
                data=None,
            )

    """Get interviews by user id"""

    def get_interviews_by_user_id(self, user_id):
        try:
            interviews = Interview.objects.filter(user_id=user_id)
            return RepositoryResponse(
                success=True,
                data=interviews,
                message="Interviews retrieved successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Could not retrieve interviews",
                data=None,
            )

    """Get interviews by company id"""

    def get_interviews_by_company(self, company):
        try:
            interviews = Interview.objects.filter(business__id=company.id)
            return RepositoryResponse(
                success=True,
                data=interviews,
                message="Interviews retrieved successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Could not retrieve interviews",
                data=None,
            )
