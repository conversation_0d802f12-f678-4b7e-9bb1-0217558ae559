from base.services.logging import LoggingService
from candidates.models import Chat
from dataclasses import dataclass
from typing import Union, Optional, Dict, List
from django.db.models import Model
from django.db import transaction


logging_service = LoggingService()


@dataclass
class RepositoryResponse:
    success: bool
    message: str
    data: Optional[Union[Chat, Dict, List, Model]]


class ChatRepository:
    def create_chat(
        self, sender_id: int, receiver_id: int, message: str
    ) -> RepositoryResponse:
        """
        Create a new chat message.
        """
        try:
            with transaction.atomic():
                chat = Chat.objects.create(
                    sender_id=sender_id, receiver_id=receiver_id, message=message
                )
                return RepositoryResponse(
                    success=True, message="Chat created successfully", data=chat
                )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(success=False, message="Error occured while creating chat", data=None)

    def get_chat_by_id(self, chat_id: int) -> RepositoryResponse:
        """
        Get a chat message by its id.
        """
        try:
            chat = Chat.objects.get(pk=chat_id)
            return RepositoryResponse(
                success=True, message="Chat retrieved successfully", data=chat
            )
        except Chat.DoesNotExist:
            return RepositoryResponse(
                success=False, message="Chat not found", data=None
            )

    def get_chats_by_sender_id(self, sender_id: int) -> RepositoryResponse:
        """
        Get all chat messages sent by a sender.
        """
        try:
            chats = Chat.objects.filter(sender_id=sender_id)
            if chats:
                return RepositoryResponse(
                    success=True, message="Chats retrieved successfully", data=chats
                )
            return RepositoryResponse(
                success=False, message="No chats found for this sender", data=[]
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(success=False, message="Error occured while getting sender chats", data=None)

    def get_chats_by_receiver_id(self, receiver_id: int) -> RepositoryResponse:
        """
        Get all chat messages received by a receiver.
        """
        try:
            chats = Chat.objects.filter(receiver_id=receiver_id)
            if chats:
                return RepositoryResponse(
                    success=True, message="Chats retrieved successfully", data=chats
                )
            return RepositoryResponse(
                success=False, message="No chats found for this receiver", data=[]
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(success=False, message="Error occured while getting receiver chats", data=None)
