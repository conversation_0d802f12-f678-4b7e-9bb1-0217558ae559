import factory
from factory.django import DjangoModelFactory
from datetime import datetime, timezone
from .models import (
    WorkExperience,
    AcademicEducation,
    Candidate,
    Interview,
    Application,
    Chat,
)
from django.utils import timezone


class WorkExperienceFactory(DjangoModelFactory):
    class Meta:
        model = WorkExperience

    name = factory.Sequence(lambda n: f"Experience {n}")
    employment_title = factory.Faker("job")
    company_name = factory.Faker("company")
    started_from = factory.Faker("date_time", tzinfo=timezone.utc)
    ended_at = factory.Faker("date_time", tzinfo=timezone.utc)
    created_by = factory.SubFactory("base.factory.UserFactory")


class AcademicEducationFactory(DjangoModelFactory):
    class Meta:
        model = AcademicEducation

    name = factory.Sequence(lambda n: f"Education {n}")
    school_name = factory.Faker("company")
    degree_attained = factory.Faker(
        "random_element", elements=["Bachelors", "Masters", "PhD"]
    )
    started_year = factory.Faker("random_int", min=2000, max=2020)
    ended_year = factory.LazyAttribute(lambda o: o.started_year + 4)
    created_by = factory.SubFactory("base.factory.UserFactory")


class CandidateFactory(DjangoModelFactory):
    class Meta:
        model = Candidate

    name = factory.LazyAttribute(lambda o: f"{o.user.first_name} {o.user.last_name}")
    user = factory.SubFactory("base.factory.UserFactory")
    # profile_picture = factory.django.ImageField()
    # resume = factory.django.FileField()
    is_employed = factory.Faker("boolean")
    linkedin_url = factory.Faker("url")
    portfolio_url = factory.Faker("url")
    gender = factory.Faker("random_element", elements=["Male", "Female"])
    date_of_birth = factory.Faker("date_time", tzinfo=timezone.utc)
    phone_number = factory.Faker("phone_number")
    bio = factory.Faker("text", max_nb_chars=500)
    started_working = factory.Faker("date_time", tzinfo=timezone.utc)
    created_by = factory.SubFactory("base.factory.UserFactory")

    @factory.post_generation
    def skills(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for skill in extracted:
                self.skills.add(skill)

    @factory.post_generation
    def work_experience(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for exp in extracted:
                self.work_experience.add(exp)
        else:
            self.work_experience.add(WorkExperienceFactory())

    @factory.post_generation
    def academic_education(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for edu in extracted:
                self.academic_education.add(edu)
        else:
            self.academic_education.add(AcademicEducationFactory())


class InterviewFactory(DjangoModelFactory):
    class Meta:
        model = Interview

    name = factory.Sequence(lambda n: f"Interview {n}")
    user = factory.SubFactory("base.factory.UserFactory")
    business = factory.SubFactory("businesses.factories.CompanyFactory")
    job_applied = factory.SubFactory("jobs.factory.JobFactory")
    interview_date = factory.Faker("future_datetime", tzinfo=timezone.utc)
    from_time = factory.Faker("time_object")
    to_time = factory.Faker("time_object")
    location = factory.Faker("random_element", elements=["Online", "In person"])
    status = factory.Faker("random_element", elements=["Pending", "Due", "Closed"])
    message = factory.Faker("text", max_nb_chars=500)
    created_by = factory.SubFactory("base.factory.UserFactory")


class ApplicationFactory(DjangoModelFactory):
    class Meta:
        model = Application

    name = factory.Sequence(lambda n: f"Application {n}")
    applicant = factory.SubFactory(CandidateFactory)
    status = factory.Faker(
        "random_element",
        elements=["Pending", "Shortlisted", "Interview", "Hired", "Rejected"],
    )
    job_applied = factory.SubFactory("jobs.factory.JobFactory")
    created_by = factory.SubFactory("base.factory.UserFactory")
    updated_by = factory.SubFactory("base.factory.UserFactory")


class ChatFactory(DjangoModelFactory):
    class Meta:
        model = Chat

    message_id = factory.Faker("uuid4")
    sender = factory.SubFactory("base.factory.UserFactory")
    receiver = factory.SubFactory("base.factory.UserFactory")
    message = factory.Faker("text", max_nb_chars=250)
    status = factory.Faker("random_element", elements=["Read", "Unread"])
    date_created = factory.Faker("date_time", tzinfo=timezone.utc)
