from re import sub
from typing import Dict, Any, List, Optional
from django.core.exceptions import ValidationError
from base.email_template.send_shortlisted_email import send_shortlisted_email
from base.email_template.send_rejected_email import send_rejected_email
from base.email_template.send_hired_email import send_hired_email
from base.email_template.interview_request import send_interview_request_email
from base.email_template.interview_request_confirmation import (
    send_interview_request_confirmation,
)
from base.services.logging import LoggingService
from candidates.models import Application
from candidates.repositories.application_repository import (
    ApplicationRepository,
    RepositoryResponse,
)
from candidates.views import interview
from candidates.views.serializers import (
    ApplicationSerializer,
    GetApplicationSerializer,
    GetInterviewSerializer,
    InterviewDataSerializer,
)
from jobs.services.job_service import JobService
from candidates.services.candidate import CandidateService
from jobs.services.job_service import JobService
from base.services.interviews import InterviewService


logging_service = LoggingService()


class ApplicationService:
    def __init__(self):
        self.repository = ApplicationRepository()
        self.job_service = JobService()
        self.candidate_service = CandidateService()
        self.job_service = JobService()
        self.interview_service = InterviewService()

    def get_all_applications(self, user_id: int) -> RepositoryResponse:
        """
        Retrieve all applications for a candidate.
        """
        if not user_id:
            return RepositoryResponse(
                success=False,
                message="User ID is required.",
                data=None,
            )
        return self.repository.get_all_applications(user_id=user_id)

    def get_application(self, application_id: int) -> RepositoryResponse:
        """
        Retrieve a single application by its ID.
        """
        if not application_id:
            return RepositoryResponse(
                success=False,
                message="Application ID is required.",
                data=None,
            )
        return self.repository.get_application(application_id)

    def create_application(self, job_slug, user_id) -> RepositoryResponse:
        """
        Create a new application.
        """
        try:
            job = self.job_service.get_job(job_slug=job_slug)
            if not job.success:
                return RepositoryResponse(
                    success=False,
                    message="Error getting job, please try again.",
                    data=None,
                )
            candidate = self.candidate_service.get_candidate(user_id=user_id)
            if not candidate.success:
                return RepositoryResponse(
                    success=False,
                    message="Error getting candidate, please try again.",
                    data=None,
                )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error retrieving job or candidate",
                data=None,
            )
        try:
            if Application.objects.filter(
                applicant=candidate.data, job_applied=job.data
            ).exists():
                return RepositoryResponse(
                    success=False,
                    message="Application already exists.",
                    data=None,
                )
            return self.repository.create_application(
                {"job_applied": job.data, "applicant": candidate.data}
            )
        except ValidationError as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Validation error",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error creating application",
                data=None,
            )

    def update_application(
        self, application_id: int, data: Dict[str, Any]
    ) -> RepositoryResponse:
        """
        Update an existing application.
        """
        if not application_id or not data:
            return RepositoryResponse(
                success=False,
                message="Application ID is required.",
                data=None,
            )

        try:
            if not "status" in data:
                return RepositoryResponse(
                    success=False,
                    message="Status is required",
                    data=None,
                )

            if not data.get("status") == "Confirmed":
                return RepositoryResponse(
                    success=False,
                    message="The status of the application can only be updated to confirmed",
                    data=None,
                )

            application_response = self.repository.update_application_status(
                application_id, data["status"]
            )
            if not application_response.success:
                return RepositoryResponse(
                    success=False,
                    message=application_response.message,
                    data=None,
                )
            return RepositoryResponse(
                success=True,
                data=application_response.data,
                message="Application status updated successfully",
            )

        except ValidationError as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Validation error",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error updating application",
                data=None,
            )
