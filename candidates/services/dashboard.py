from base.services.logging import LoggingService
from candidates.repositories.candidate_repository import CandidateRepository
from candidates.repositories.application_repository import RepositoryResponse
from candidates.views.serializers import GetApplicationSerializer, GetCandidateSerializer, GetInterviewSerializer
from candidates.repositories.application_repository import ApplicationRepository
from candidates.repositories.chat_repository import ChatRepository
from candidates.repositories.interview_repository import InterviewRepository


logging_service = LoggingService()


class DashboardService:
    def __init__(self):
        self.candidate_repository = CandidateRepository()
        self.application_repository = ApplicationRepository()
        self.chat_repository = ChatRepository()
        self.interview_repository = InterviewRepository()
    
    def get_dashboard_data(self, user) -> RepositoryResponse:
        try:
            """ get candidate """
            candidate = self.candidate_repository.get_candidate_by_user_id(user_id=user.id)

            if not candidate.success:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message=candidate.message,
                )

            profile_data = GetCandidateSerializer(candidate.data).data

            unread_messages = 0
            sent_applications = 0
            rejected_applications = 0
            upcoming_interviews = 0
            interview_data = None
            apps_serializer = None
            total_applications = 0

            """ candidate applications """
            applications = self.application_repository.get_all_applications(user_id=user.id)
            if applications.success:
                sent_applications = applications.data.count()
                rejected_applications = applications.data.filter(status="Rejected").count()
                if len(applications.data) > 3:
                    recent_applications = applications.data.order_by("date_created")[:3]
                    pass
                else:
                    recent_applications = applications.data
                apps_serializer = GetApplicationSerializer(recent_applications, many=True).data if recent_applications else []
                total_applications = {
                "applications": sent_applications,
                "shortlisted": self.get_percentage(total=sent_applications, base="Shortlisted", applications=applications.data),
                "on_hold": self.get_percentage(total=sent_applications, base="Pending", applications=applications.data),
                "rejected": self.get_percentage(total=sent_applications, base="Rejected", applications=applications.data),
                }

            """ Candidate interviews """
            interviews = self.interview_repository.get_interviews_by_user_id(user_id=user.id)
            if interviews.success:
                upcoming_interviews = interviews.data.filter(status="Upcoming").count()
                interview_data = GetInterviewSerializer(
                    interviews.data.filter(status="Upcoming").order_by("-interview_date"), many=True
                    ).data

            
            """ Candidate messages """
            messages = self.chat_repository.get_chats_by_receiver_id(receiver_id=user.id)
            if messages.success:
                unread_messages = messages.data.count()
            
            stats = {
                "upcoming_interviews": upcoming_interviews or 0,
                "unread_messages": unread_messages or 0,
                "sent_applications": sent_applications or 0,
                "rejected_applications": rejected_applications or 0,
                "recent_interview": interview_data if interview_data else [],
                "recent_applications": apps_serializer if apps_serializer else [],
                "total_applications": total_applications or 0,
                "profile_data": profile_data or None,
            }
            response_data = {"stats": stats}
            return RepositoryResponse(
                success=True,
                data=response_data,
                message="Dashboard data fetched successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to fetch dashboard data",
            )
    
    def get_percentage(self, total, base, applications):
            if total <= 0:
                percentage = 0
            else:
                rejected = applications.filter(status=base).count()
                percentage = (rejected / total) * 100
            return "{:.1f}".format(percentage)