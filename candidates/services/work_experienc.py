from typing import Dict, Any
from datetime import datetime
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from candidates.repositories.work_experienc import WorkExperienceRepository
from candidates.repositories.application_repository import RepositoryResponse


class WorkExperienceService:
    def __init__(self, repository: WorkExperienceRepository = None):
        self.repository = repository or WorkExperienceRepository()

    def _validate_work_experience_data(self, data: Dict[str, Any]):
        required_fields = ['employment_title', 'company_name', 'started_from']
        for field in required_fields:
            if field not in data or not data[field]:
                raise ValidationError({field: _("This field is required.")})
        
        if len(data.get('company_name', '')) > 255:
            raise ValidationError({'company_name': _("Company name cannot exceed 255 characters.")})
        
        self._validate_dates(data.get('started_from'), data.get('ended_at'))

    def _validate_dates(self, started_from: datetime, ended_at: datetime = None):
        if not started_from:
            raise ValidationError(_("Start date is required"))
        
        try:
            started_from = self._parse_datetime(started_from)
            
            if ended_at:
                ended_at = self._parse_datetime(ended_at)
                
                if started_from > ended_at:
                    raise ValidationError(_("Start date cannot be later than end date"))
        except ValueError:
            raise ValidationError(_("Invalid date format"))
        
        current_time = datetime.now()
        if started_from > current_time:
            raise ValidationError(_("Start date cannot be in the future"))

    def _parse_datetime(self, date_value):
        if isinstance(date_value, datetime):
            return date_value
        
        if isinstance(date_value, str):
            try:
                return datetime.fromisoformat(date_value)
            except ValueError:
                raise ValueError(_("Invalid date format"))
        
        raise ValueError(_("Invalid date type"))

    def create_work_experience(self, data: Dict[str, Any]) -> Dict[str, Any]:
        self._validate_work_experience_data(data)
        
        response = self.repository.create_work_experience(data)
        
        if not response.success:
            raise ValidationError(response.message)
        
        data = {
            'id': response.data.id,
            'employment_title': response.data.employment_title,
            'company_name': response.data.company_name,
            'started_from': response.data.started_from,
            'ended_at': response.data.ended_at,
            'date_created': response.data.date_created,
            'date_updated': response.data.date_updated,
        }
        return RepositoryResponse(
            success=True,
            data=data,
            message=_("Work experience created successfully.")
        )

    def get_work_experience_by_id(self, id: str) -> Dict[str, Any]:
        response = self.repository.get_work_experience_by_id(id)
        
        if not response.success:
            raise ValidationError(response.message)
        
        return {
            'id': response.data.id,
            'employment_title': response.data.employment_title,
            'company_name': response.data.company_name,
            'started_from': response.data.started_from,
            'ended_at': response.data.ended_at,
            'date_created': response.data.date_created,
            'date_updated': response.data.date_updated,
        }

    def update_work_experience(self, id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        current_data = self.get_work_experience_by_id(id)
        
        merged_data = {**current_data, **data}
        
        self._validate_work_experience_data(merged_data)
        
        response = self.repository.update_work_experience(id, data)
        
        if not response.success:
            raise ValidationError(response.message)
        
        return {
            'id': response.data.id,
            'employment_title': response.data.employment_title,
            'company_name': response.data.company_name,
            'started_from': response.data.started_from,
            'ended_at': response.data.ended_at,
            'date_created': response.data.date_created,
            'date_updated': response.data.date_updated,
        }

    def delete_work_experience(self, id: str) -> Dict[str, str]:
        response = self.repository.delete_work_experience(id)
        
        if not response.success:
            raise ValidationError(response.message)
        
        return {"message": "Work experience deleted successfully"}