from re import A
from accounts.services.user_services import UserService
from base import serializers
from base.services.logging import LoggingService
from base.utils.status_checker import APIResponse
from candidates.repositories.candidate_repository import CandidateRepository
from candidates.services.candidate import CandidateService
from candidates.views.serializers import GetCandidateSerializer


user_service = UserService()
candidate_repo = CandidateRepository()
candidate_service = CandidateService()
logging_service = LoggingService()


class AdminCandidateService:
    # TODO: Check if logged in user is in a group called Candidate Manager
    def create_candidate(self, user, data) -> APIResponse:
        """Create candidate"""
        try:
            # create a user
            # check if user_data is in data
            new_user = None
            if "user_data" in data:
                password = "123"
                user_data = data["user_data"]
                user_data["password"] = password
                response = user_service.create_user(user_data, user)
                if not response.success:
                    return APIResponse(
                        success=False,
                        message=response.message,
                        status=400,
                        data=None,
                    )
                new_user = response.data
            else:
                return APIResponse(
                    success=False,
                    message="User data are required",
                    data=None,
                    status=400,
                )

            # create a candidate
            # check if candidate_data is in data
            if "candidate_data" in data:
                candidate_data = data["candidate_data"]
                candidate_data["user"] = new_user
                response = candidate_repo.create_candidate(data["candidate_data"])
                if not response.success:
                    return APIResponse(
                        message=response.message,
                        status=400,
                        data=None,
                    )
                serializer = GetCandidateSerializer(response.data)
                return APIResponse(
                    success=True,
                    message="Candidate created successfully",
                    data=serializer.data,
                    status=200,
                )
            else:
                return APIResponse(
                    message="Candidate are required",
                    success=False,
                    data=None,
                    status=400,
                )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error creating candidate",
                status=500,
            )

    def get_candidate_details(self, user, candidate_id):
        """Get candidate details"""
        try:
            response = candidate_repo.get_candidate(candidate_id)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    status=400,
                )
            serializer = GetCandidateSerializer(response.data)

            return APIResponse(
                success=True,
                message="Candidate retrieved successfully",
                data=serializer.data,
                status=200,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error creating candidate",
                status=500,
            )

    def list_candidates(self, user, page=1, page_size=10):
        """Get candidate list. Apply filters and pagination"""
        try:
            response = candidate_service.get_all_candidates(
                page=page, page_size=page_size
            )
            return response
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error creating candidate",
                status=500,
            )

    def update_candidate(self, user, candidate_id, data):
        """Update candidate"""
        user_data = {
            "first_name": data.pop("first_name", None),
            "last_name": data.pop("last_name", None),
            "email": data.pop("email", None),
        }

        try:
            response = candidate_service.update_candidate(candidate_id, data)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    status=400,
                )
            return response
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error creating candidate",
                status=500,
                data=None,
            )

    def activate_candidate(self, user, candidate_id):
        """Activate candidate account"""
        try:
            response = candidate_repo.activate_candidate(candidate_id)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    status=400,
                    data=None,
                )
            serializer = GetCandidateSerializer(response.data)
            return APIResponse(
                success=True,
                message="Candidate activated successfully",
                data=serializer.data,
                status=200,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error creating candidate",
                status=500,
                data=None,
            )

    def deactivate_candidate(self, user, candidate_id):
        "Deactivate candidate account"
        try:
            response = candidate_repo.deactivate_candidate(candidate_id)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    status=400,
                    data=None,
                )
            serializer = GetCandidateSerializer(response.data)
            return APIResponse(
                success=True,
                message="Candidate deactivated successfully",
                data=serializer.data,
                status=200,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error creating candidate",
                status=500,
                data=None,
            )

    def delete_candidate(self, user, candidate_id):
        """Delete candidate"""
        try:
            response = candidate_repo.delete_candidate(candidate_id)
            return response
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error creating candidate",
                status=500,
                data=None,
            )
