from functools import partial
from typing import Dict, Any
from datetime import datetime
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from base import serializers
from base.services.logging import LoggingService
from candidates.repositories.education import AcademicEducationRepository
from candidates.repositories.application_repository import RepositoryResponse
from candidates.views.serializers import (
    AcademicEducationSerializer,
    GetAcademicEducationSerializer,
)

error_service = LoggingService()


class AcademicEducationService:
    def __init__(self, repository: AcademicEducationRepository = None):
        self.repository = repository or AcademicEducationRepository()

    def _validate_years(self, started_year: int, ended_year: int):
        try:
            started_year = int(started_year)
            ended_year = int(ended_year)
        except (ValueError, TypeError):
            raise ValidationError(_("Years must be valid integers"))

        if started_year > ended_year:
            raise ValidationError(_("Started year cannot be later than ended year"))

        current_year = datetime.now().year
        if started_year < 1900 or started_year > current_year:
            raise ValidationError(_("Invalid started year"))

        if ended_year < 1900 or ended_year > current_year + 10:
            raise ValidationError(_("Invalid ended year"))

    def create_academic_education(self, data: Dict[str, Any]) -> RepositoryResponse:
        serializer = AcademicEducationSerializer(data=data, partial=True)
        # check required fields
        missing_fields = error_service.check_required_fields(
            data=data, required_fields=["school_name", "started_year", "ended_year"]
        )
        if missing_fields:
            print(missing_fields)
            return RepositoryResponse(
                success=False,
                data=None,
                message=missing_fields,
            )
        if not serializer.is_valid():
            print(serializer.errors)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Important fields missing",
            )
        response = self.repository.create_academic_education(data)
        if not response.success:
            return RepositoryResponse(
                success=False, data=None, message=response.message
            )

        return RepositoryResponse(
            success=True,
            data=GetAcademicEducationSerializer(response.data).data,
            message=_("Academic education created successfully."),
        )

    def get_academic_education_by_id(self, id: str) -> RepositoryResponse:
        try:
            response = self.repository.get_academic_education_by_id(id)

            if not response.success:
                return RepositoryResponse(
                    success=False, data=None, message=response.message
                )

            return RepositoryResponse(
                success=True,
                data=GetAcademicEducationSerializer(response.data).data,
                message=_("Academic education retrieved successfully."),
            )
        except ValidationError as e:
            return RepositoryResponse(success=False, data=None, message=str(e))

    def update_academic_education(
        self, id: str, data: Dict[str, Any]
    ) -> RepositoryResponse:
        try:
            # First, get the current data
            current_response = self.get_academic_education_by_id(id)

            if not current_response.success:
                return current_response

            # Merge current data with new data

            # Validate the merged data

            response = self.repository.update_academic_education(id, data)

            if not response.success:
                return RepositoryResponse(
                    success=False, data=None, message=response.message
                )

            return RepositoryResponse(
                success=True,
                data=GetAcademicEducationSerializer(response.data).data,
                message=_("Academic education updated successfully."),
            )
        except ValidationError as e:
            return RepositoryResponse(success=False, data=None, message=str(e))

    def delete_academic_education(self, id: str) -> RepositoryResponse:
        try:
            response = self.repository.delete_academic_education(id)

            if not response.success:
                return RepositoryResponse(
                    success=False, data=None, message=response.message
                )

            return RepositoryResponse(
                success=True,
                data=None,
                message=_("Academic education deleted successfully."),
            )
        except ValidationError as e:
            return RepositoryResponse(success=False, data=None, message=str(e))
