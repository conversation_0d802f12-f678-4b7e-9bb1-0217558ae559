from typing import Dict, Any, List, Optional
from django.core.exceptions import ValidationError
from candidates.repositories.chat_repository import ChatRepository
from candidates.repositories.application_repository import RepositoryResponse


class ChatService:
    def __init__(self):
        self.repository = ChatRepository()
    
    def create_chat(
        self, sender_id: int, receiver_id: int, message: str
    ) -> RepositoryResponse:
        """
        Create a new chat message.
        """
        try:
            if not sender_id:
                raise ValidationError("Sender ID is required.")
            if not receiver_id:
                raise ValidationError("Receiver ID is required.")
            if not message or message == "":
                raise ValidationError("Message is required.")
            chat = self.repository.create_chat(sender_id, receiver_id, message)
            if chat:
                return RepositoryResponse(
                    success=True,
                    data=chat,
                    message="Chat message created successfully."
                )
        except ValidationError as e:
            return RepositoryResponse(success=False, data=None, message=str(e))
    
    def get_chat_by_id(self, chat_id: int) -> RepositoryResponse:
        """
        Get a chat message by its id.
        """
        try:
            if not chat_id:
                raise ValidationError("Chat ID is required.")
            chat = self.repository.get_chat_by_id(chat_id)
            if chat:
                return RepositoryResponse (
                    success=True,
                    data=chat,
                    message="Chat message retrieved successfully."
                )
        except ValidationError as e:
            return RepositoryResponse(success=False, data=None, message=str(e))
    
    def get_chats_by_sender_id(self, sender_id: int) -> RepositoryResponse:
        """
        Get all chat messages sent by a sender.
        """
        try:
            if not sender_id:
                raise ValidationError("Sender ID is required.")
            chats = self.repository.get_chats_by_sender_id(sender_id)
            if chats:
                return RepositoryResponse(
                    success=True,
                    data=chats,
                    message="Chat messages retrieved successfully."
                )
        except ValidationError as e:
            return RepositoryResponse(success=False, data=None, message=str(e))
    
    def get_chats_by_receiver_id(self, receiver_id: int) -> RepositoryResponse:
        """
        Get all chat messages received by a receiver.
        """
        try:
            if not receiver_id:
                raise ValidationError("Receiver ID is required.")
            chats = self.repository.get_chats_by_receiver_id(receiver_id)
            if chats:
                return RepositoryResponse(
                    success=True,
                    data=chats,
                    message="Chat messages retrieved successfully."
                )
        except ValidationError as e:
            return RepositoryResponse(success=False, data=None, message=str(e))