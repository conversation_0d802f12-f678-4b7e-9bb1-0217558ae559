from typing import Dict, Any, Optional, Union
from django.core.exceptions import ValidationError
from django.db import transaction
from accounts.services.profile_service import APIResponse
from base.services.logging import LoggingService
from candidates.repositories.interview_repository import InterviewRepository
from businesses.repositories.business_repository import CompanyRepository
from candidates.models import User
from rest_framework import status

logging_service = LoggingService()


class InterviewService:
    def __init__(self):
        """
        Initialize the InterviewService with repositories.
        """
        self.interview_repository = InterviewRepository()
        self.company_repository = CompanyRepository()

    @transaction.atomic
    def create_interview(self, data: Dict[str, Any]) -> APIResponse:
        try:
            response = self.interview_repository.create_interview(data)

            return APIResponse(
                success=True,
                data=response.data,
                message="Interview created successfully",
                status=status.HTTP_201_CREATED,
            )

        except ValidationError as ve:
            logging_service.log_error(ve)
            return APIResponse(
                success=False,
                data=None,
                message="Validation error while creating interview",
                status=status.HTTP_400_BAD_REQUEST,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Unexpected error while creating interview",
                status=status.HTTP_400_BAD_REQUEST,
            )

    @transaction.atomic
    def update_interview(self, interview_id: int, data: Dict[str, Any]) -> APIResponse:
        try:
            response = self.interview_repository.update_interview(interview_id, data)

            return APIResponse(
                success=True,
                data=response.data,
                message="Interview updated successfully",
                status=status.HTTP_200_OK,
            )

        except ValidationError as ve:
            logging_service.log_error(ve)
            return APIResponse(
                success=False,
                data=None,
                message="Validation error while updating interview",
                status=status.HTTP_400_BAD_REQUEST,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message=f"Unexpected error while updating interview",
                status=status.HTTP_400_BAD_REQUEST,
            )

    def delete_interview(self, interview_id: int) -> APIResponse:
        try:
            response = self.interview_repository.delete_interview(interview_id)
            return APIResponse(
                success=response.success,
                data=response.data,
                message=response.message or "Interview deleted successfully",
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Error while deleting interview",
                status=status.HTTP_400_BAD_REQUEST,
            )

    def get_interview(self, interview_id: int) -> APIResponse:
        try:
            response = self.interview_repository.get_interview(interview_id)
            return APIResponse(
                success=response.success,
                data=response.data,
                message=(
                    "Interview retrieved successfully"
                    if response.success
                    else response.message
                ),
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Error while retrieving interview",
                status=status.HTTP_400_BAD_REQUEST,
            )

    def get_interviews_by_user_id(self, user_id: int) -> APIResponse:
        try:
            response = self.interview_repository.get_interviews_by_user_id(
                user_id
            )
            return APIResponse(
                success=response.success,
                data=response.data,
                message=(
                    "Candidate interviews retrieved successfully"
                    if response.success
                    else response.message
                ),
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Error while retrieving candidate interviews",
                status=status.HTTP_400_BAD_REQUEST,
            )

    def get_interviews_by_company_slug(self, slug: str) -> APIResponse:
        try:
            company_response = self.company_repository.get_business(slug)
            if not company_response.success:
                return APIResponse(
                    success=False,
                    data=None,
                    message="Invalid company ID",
                    status=status.HTTP_400_BAD_REQUEST,
                )

            response = self.interview_repository.get_interviews_by_company(
                company_response.data
            )
            return APIResponse(
                success=response.success,
                data=response.data,
                message=(
                    "Company interviews retrieved successfully"
                    if response.success
                    else response.message
                ),
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Error while retrieving company interviews",
                status=status.HTTP_400_BAD_REQUEST,
            )
