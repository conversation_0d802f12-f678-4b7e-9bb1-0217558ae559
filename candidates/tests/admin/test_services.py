from base.tests.setup import BaseSetup
from candidates.factory import CandidateFactory
from candidates.models import Candidate
from candidates.services import candidate
from candidates.services.admin import AdminCandidateService


service = AdminCandidateService()


class TestAdminCreateCandidate(BaseSetup):
    def setUp(self):
        super().setUp()

    def test_create_candidate_success(self):
        data = {
            "user_data": {
                "username": "<EMAIL>",
                "email": "<EMAIL>",
            },
            "candidate_data": {
                "phone_number": "1234567890",
                "gender": "male",
                "date_of_birth": "2000-01-01",
            },
        }
        response = service.create_candidate(self.admin, data)

        if not response.success:
            print(f"Failed to create a candidate: {response}")
        self.assertEqual(response.success, True)

        self.assertIsNotNone(response.data)
        self.assertEqual(response.data["phone_number"], "1234567890")
        self.assertEqual(response.data["gender"], "male")
        self.assertEqual(response.data["date_of_birth"], "2000-01-01")


class TestAdminUpdateCandidate(BaseSetup):
    def setUp(self):
        super().setUp()
        self.candidate = CandidateFactory()

    def test_update_candidate_success(self):
        data = {
            "username": "<EMAIL>",
            "email": "<EMAIL>",
            "phone_number": "1234567890",
            "gender": "male",
            "date_of_birth": "2000-01-01",
        }
        response = service.update_candidate(self.admin, self.candidate.id, data)

        if not response.success:
            print(f"Failed to create a candidate: {response}")
        self.assertEqual(response.success, True)

        self.assertIsNotNone(response.data)
        self.assertEqual(response.data["phone_number"], "1234567890")
        self.assertEqual(response.data["gender"], "male")
        self.assertEqual(response.data["date_of_birth"], "2000-01-01")


class TestAdminGetCandidate(BaseSetup):
    def setUp(self):
        super().setUp()
        self.candidate = CandidateFactory()

    def test_get_candidate_success(self):
        response = service.get_candidate_details(self.admin, self.candidate.id)

        if not response.success:
            print(f"Failed to create a candidate: {response}")
        self.assertEqual(response.success, True)

        self.assertIsNotNone(response.data)
        self.assertEqual(response.data["phone_number"], self.candidate.phone_number)
        self.assertEqual(response.data["gender"], self.candidate.gender)


class TestAdminListCandidates(BaseSetup):
    def setUp(self):
        super().setUp()

        self.candidates = [CandidateFactory() for _ in range(3)]

    def test_list_candidates_success(self):

        response = service.list_candidates(self.admin)

        if not response.success:
            print(f"Failed to create a candidate: {response}")
        self.assertEqual(response.success, True)

        self.assertIsNotNone(response.data)
        self.assertEqual(len(response.data["candidates"]), 3)


class TestAdminDeleteCandidate(BaseSetup):
    def setUp(self):
        super().setUp()
        self.candidate = CandidateFactory()

    def test_delete_candidate_success(self):
        response = service.delete_candidate(self.admin, self.candidate.id)

        if not response.success:
            print(f"Failed to create a candidate: {response}")
        self.assertEqual(response.success, True)
        self.assertFalse(Candidate.objects.filter(id=self.candidate.id).exists())


class TestAdminActivateCandidate(BaseSetup):
    def setUp(self):
        super().setUp()
        self.candidate = CandidateFactory()

    def test_activate_candidate_success(self):
        response = service.activate_candidate(self.admin, self.candidate.id)

        if not response.success:
            print(f"Failed to create a candidate: {response}")
        self.candidate.refresh_from_db()
        self.assertEqual(response.success, True)
        self.assertTrue(response.data["is_active"])


class TestAdminDeactivateCandidate(BaseSetup):
    def setUp(self):
        super().setUp()
        self.candidate = CandidateFactory()

    def test_deactivate_candidate_success(self):
        response = service.deactivate_candidate(self.admin, self.candidate.id)

        if not response.success:
            print(f"Failed to create a candidate: {response}")
        self.candidate.refresh_from_db()
        self.assertEqual(response.success, True)
        self.assertFalse(response.data["is_active"])
