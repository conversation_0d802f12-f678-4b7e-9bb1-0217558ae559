from base.tests.setup import BaseSetup
from candidates.factory import CandidateFactory
from candidates.models import Candidate


class TestAdminCreateCandidate(BaseSetup):
    def setUp(self):
        super().setUp()

    def test_create_candidate_success(self):
        data = {
            "user_data": {
                "username": "<EMAIL>",
                "email": "<EMAIL>",
            },
            "candidate_data": {
                "phone_number": "1234567890",
                "gender": "male",
                "date_of_birth": "2000-01-01",
            },
        }

        self._authenticate_user(self.admin)

        response = self.client.post(
            f"{self.admin_endpoints}/candidates/", data, format="json"
        )

        if not response.status_code == 201:
            self.fail(f"Failed to create a candidate: {response.data}")

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["phone_number"], "1234567890")
        self.assertEqual(response.data["gender"], "male")
        self.assertEqual(response.data["date_of_birth"], "2000-01-01")
        self.assertEqual(response.data["user"]["email"], "<EMAIL>")


class TestAdminUpdateCandidate(BaseSetup):
    def setUp(self):
        super().setUp()
        self.candidate = CandidateFactory()

    def test_update_candidate_success(self):
        data = {
            "username": "<EMAIL>",
            "email": "<EMAIL>",
            "phone_number": "1234567890",
            "gender": "male",
            "date_of_birth": "2000-01-01",
            "bio": "Updated bio",
        }

        self._authenticate_user(self.admin)

        response = self.client.put(
            f"{self.admin_endpoints}/candidates/{self.candidate.id}/",
            data,
            format="json",
        )

        if not response.status_code == 200:
            self.fail(f"Failed to update a candidate: {response.data}")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["phone_number"], "1234567890")
        self.assertEqual(response.data["gender"], "male")
        self.assertEqual(response.data["date_of_birth"], "2000-01-01")

        self.candidate.refresh_from_db()
        self.assertEqual(self.candidate.phone_number, "1234567890")
        self.assertEqual(self.candidate.gender, "male")
        self.assertEqual(self.candidate.bio, "Updated bio")


class TestAdminGetCandidate(BaseSetup):
    def setUp(self):
        super().setUp()
        self.candidate = CandidateFactory(
            phone_number="1234567890",
            gender="male",
        )

    def test_get_candidate_success(self):
        self._authenticate_user(self.admin)

        response = self.client.get(
            f"{self.admin_endpoints}/candidates/{self.candidate.id}/"
        )

        if not response.status_code == 200:
            self.fail(f"Failed to get a candidate: {response.data}")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["phone_number"], self.candidate.phone_number)
        self.assertEqual(response.data["gender"], self.candidate.gender)

        self.candidate.refresh_from_db()
        self.assertEqual(self.candidate.phone_number, "1234567890")
        self.assertEqual(self.candidate.gender, "male")


class TestAdminListCandidates(BaseSetup):
    def setUp(self):
        super().setUp()
        self.candidates = [CandidateFactory() for _ in range(3)]

    def test_list_candidates_success(self):
        self._authenticate_user(self.admin)

        response = self.client.get(f"{self.admin_endpoints}/candidates/")

        if not response.status_code == 200:
            self.fail(f"Failed to list candidates: {response.data}")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["candidates"]), 3)


class TestAdminDeleteCandidate(BaseSetup):
    def setUp(self):
        super().setUp()
        self.candidate = CandidateFactory()

    def test_delete_candidate_success(self):
        self._authenticate_user(self.admin)

        response = self.client.delete(
            f"{self.admin_endpoints}/candidates/{self.candidate.id}/"
        )

        if not response.status_code == 204:
            self.fail(f"Failed to delete a candidate: {response.data}")

        self.assertEqual(response.status_code, 204)
        self.assertFalse(Candidate.objects.filter(id=self.candidate.id).exists())


class TestAdminActivateCandidate(BaseSetup):
    def setUp(self):
        super().setUp()
        self.candidate = CandidateFactory(is_active=False)

    def test_activate_candidate_success(self):
        self._authenticate_user(self.admin)

        data = {
            "action": "activate",
        }

        response = self.client.patch(
            f"{self.admin_endpoints}/candidates/{self.candidate.id}/",
            data,
        )

        if not response.status_code == 200:
            self.fail(f"Failed to activate a candidate: {response.data}")

        self.assertEqual(response.status_code, 200)
        self.candidate.refresh_from_db()
        self.assertTrue(self.candidate.is_active)


class TestAdminDeactivateCandidate(BaseSetup):
    def setUp(self):
        super().setUp()
        self.candidate = CandidateFactory(is_active=True)

    def test_deactivate_candidate_success(self):
        self._authenticate_user(self.admin)

        data = {
            "action": "deactivate",
        }

        response = self.client.patch(
            f"{self.admin_endpoints}/candidates/{self.candidate.id}/",
            data,
        )

        if not response.status_code == 200:
            self.fail(f"Failed to deactivate a candidate: {response.data}")

        self.assertEqual(response.status_code, 200)
        self.candidate.refresh_from_db()
        self.assertFalse(self.candidate.is_active)
