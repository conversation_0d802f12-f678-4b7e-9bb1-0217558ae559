from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from django.test import TestCase, override_settings
from django.core.cache import cache
from datetime import datetime, timedelta
import jwt
from rest_framework_simplejwt.tokens import RefreshToken
from django.urls import reverse
from base.factory import UserFactory
from base.models import Address, City, Country, State
from candidates.factory import CandidateFactory
from candidates.services import candidate
from core.settings import SECRET_KEY
from rest_framework.authtoken.models import Token
from django.conf import settings
from rest_framework import status
from candidates.models import (
    Candidate,
    Skill,
    WorkExperience,
    AcademicEducation,
    SocialMediaPlatforms,
    UserLanguage,
)
from candidates.services.candidate import CandidateService

User = get_user_model()
candidate_service = CandidateService()


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class CandidateEndpointTests(TestCase):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        self.client = APIClient()

        # Create test user
        User = get_user_model()
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
        )

        # Authenticate the user
        self.token = self._get_token(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {self.token}")

        # Create test data
        country = Country.objects.create(country_code="RW", name="Rwanda")
        state = State.objects.create(country_name=country)
        city = City.objects.create(state_name=state)
        address = Address.objects.create(
            street_address="123 Test Street", city_name=city
        )
        skill1 = Skill.objects.create(name="Python")
        skill2 = Skill.objects.create(name="Django")
        work_experience = WorkExperience.objects.create(
            company_name="XYZ Corp",
            employment_title="Developer",
            started_from="2020-01-01",
        )
        academic_education = AcademicEducation.objects.create(
            school_name="Test University",
            degree_attained="Bachelor's",
            started_year=2015,
            ended_year=2020,
        )
        language = UserLanguage.objects.create(name="English")
        social_platform = SocialMediaPlatforms.objects.create(
            handle="GitHub", link="https://github.com/johndoe"
        )

        # Create a complete candidate profile
        self.complete_candidate = Candidate.objects.create(
            user=self.user,
            profile_picture="profile.png",
            resume="resume.pdf",
            is_employed=True,
            location=address,
            linkedin_url="https://linkedin.com/in/johndoe",
            portfolio_url="https://portfolio.com/johndoe",
            gender="Male",
            bio="Experienced software developer",
            date_of_birth="1990-01-01",
            started_working="2015-01-01",
            phone_number="+9876543210",
            created_by=self.user
        )
        self.complete_candidate.skills.add(skill1, skill2)
        self.complete_candidate.work_experience.add(work_experience)
        self.complete_candidate.academic_education.add(academic_education)
        self.complete_candidate.languages.add(language)
        self.complete_candidate.social_platforms.add(social_platform)

        # Create an incomplete candidate profile
        self.user_incomplete = User.objects.create_user(
            username="janedoe", email="<EMAIL>"
        )
        self.incomplete_token = self._get_token(self.user_incomplete)
        self.incomplete_candidate = Candidate.objects.create(
            user=self.user_incomplete,
            is_employed=False,
            location=address,
            created_by=self.user_incomplete
        )
        self.incomplete_candidate.skills.add(skill1)
        self.user_without_profile = User.objects.create_user(
        username="noprofile",
        email="<EMAIL>",
        password="testpass789"
    )
        self.token_without_profile = self._get_token(self.user_without_profile)

    def _get_token(self, user):
        """Helper method to get JWT token"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)

    def tearDown(self):
        cache.clear()  # Clear cache after each test

    def test_get_candidate_status_complete(self):
        """Test retrieving the status of a complete candidate profile"""
        url = reverse("get_profile_status")
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {self.token}")

        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["complete"], True)
        self.assertEqual(response.data["current_step"], 5)

    def test_get_candidate_status_incomplete(self):
        """Test retrieving the status of an incomplete candidate profile"""
        url = reverse("get_profile_status")
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {self.incomplete_token}")

        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["complete"], False)
        self.assertEqual(response.data["current_step"], 2)

    def test_get_candidate_status_not_found(self):
        """Test retrieving the status of a non-existent candidate profile"""
        url = reverse("get_profile_status")
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {self.token_without_profile}")

        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_get_candidate_status_unauthenticated(self):
        """Test retrieving candidate status without authentication"""
        url = reverse("get_profile_status")

        self.client.credentials()  # Remove authentication
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_complete_profile(self):
        url = reverse("complete_profile")
        ids = [self.complete_candidate.id]
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {self.token}")
        response = self.client.post(url, {"candidate_ids": ids}, format="json")

        print(response.data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["message"], "Profile completion email sent successfully")
    
    def test_complete_profile_nonexistent(self):
        url = reverse("complete_profile")
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {self.token}")
        response = self.client.post(url, {"candidate_ids": [0,1]}, format="json")
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_complete_profile_unauthenticate(self):
        url = reverse("complete_profile")
        ids = [self.complete_candidate.id]
        self.client.credentials()
        response = self.client.post(url, {"candidate_ids": ids}, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_get_all_candidates(self):
        """Test retrieving all candidates"""
        url = reverse("get_all_candidates")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_paginator_success(self):
        """Test pagination of candidates"""
        url = reverse("get_all_candidates")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_page_size(self):
        """Test page size of candidates"""
        url = reverse("get_all_candidates")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class UpdateProfileEndpointTest(APITestCase):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        # Create a test user
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="password123",
        )
        candidate_data = {
            "user": self.user,
            "name": "Candidate",
            "linkedin_url": "https://initial.linkedin.com",
            "portfolio_url": "https://initial.portfolio.com",
            "gender": "Male",
            "date_of_birth": "1990-01-01",
            "phone_number": "1234567890",
            "bio": "Initial bio",
            "is_employed": True,
            "created_by": self.user
        }
        self.candidate = candidate_service.create_candidate(data=candidate_data)

        # Generate a JWT token
        token_payload = {
            "user_id": self.user.id,
            "exp": datetime.now() + timedelta(days=1),
        }
        jwt_token = jwt.encode(token_payload, SECRET_KEY, algorithm="HS256")

        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        # Set the Authorization header with the Bearer token
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {jwt_token}")

        # Prepare endpoint and sample data
        self.url = "/api/candidates/profile/update/"
        self.profile_data = {
            "linkedin_url": "https://linkedin.com/in/testuser",
            "portfolio_url": "https://portfolio.com/testuser",
            "gender": "Female",
            "date_of_birth": "1990-01-01",
            "phone_number": "1234567890",
            "bio": "Experienced software developer",
            "is_employed": False,
            "started_working": "2010-06-15",
            "skills": [
                {"name": "Python", "category_name": "Programming"},
                {"name": "Django", "category_name": "Programming"},
            ],
            "work_experiences": [
                {
                    "employment_title": "Software Engineer",
                    "company_name": "TechCorp",
                    "started_from": "2015-05-01",
                    "ended_at": "2020-04-30",
                }
            ],
            "education_list": [
                {
                    "school_name": "Kigali p school",
                    "degree_attained": "B.Sc. Computer Sciences",
                    "started_year": 2009,
                    "ended_year": 2012,
                }
            ],
            "languages": [{"name": "English"}, {"name": "French"}],
            "social_platforms": [
                {
                    "name": "LinkedIn",
                    "handle": "LinkedIn",
                    "link": "https://linkedin.com/in/testuser",
                },
                {
                    "name": "GitHub",
                    "handle": "GitHub",
                    "link": "https://github.com/testuser",
                },
            ],
        }

    def tearDown(self):
        cache.clear()  # Clear cache after each test

    def test_update_profile_success(self):
        """Test updating a candidate profile successfully"""
        response = self.client.patch(self.url, self.profile_data, format="json")
        print("Response is: ", response.data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify candidate data in database
        candidate = Candidate.objects.get(user=self.user)
        self.assertEqual(candidate.linkedin_url, "https://linkedin.com/in/testuser")
        self.assertEqual(candidate.portfolio_url, "https://portfolio.com/testuser")
        self.assertEqual(candidate.gender, "Female")
        self.assertEqual(candidate.date_of_birth.strftime("%Y-%m-%d"), "1990-01-01")
        self.assertEqual(candidate.bio, "Experienced software developer")

        # Verify related models
        skills = Skill.objects.filter(candidate=candidate)
        self.assertEqual(skills.count(), 2)
        self.assertTrue(skills.filter(name="Python").exists())

        work_experiences = WorkExperience.objects.filter(candidate=candidate)
        self.assertEqual(work_experiences.count(), 1)
        self.assertEqual(work_experiences.first().employment_title, "Software Engineer")

        languages = UserLanguage.objects.filter(candidate=candidate)
        self.assertEqual(languages.count(), 2)
        self.assertTrue(languages.filter(name="English").exists())

        social_platforms = SocialMediaPlatforms.objects.filter(candidate=candidate)
        self.assertEqual(social_platforms.count(), 2)
        self.assertTrue(social_platforms.filter(name="LinkedIn").exists())

    def test_update_profile_invalid_date(self):
        """Test update profile with invalid date_of_birth"""
        invalid_data = self.profile_data.copy()
        invalid_data["date_of_birth"] = "invalid-date"
        response = self.client.patch(self.url, invalid_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_profile_unauthenticated(self):
        """Test update profile without authentication"""
        self.client.logout()
        response = self.client.post(self.url, self.profile_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn("detail", response.data)
        self.assertEqual(
            response.data["detail"], "Authentication credentials were not provided."
        )


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class DeleteCandidateTests(APITestCase):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        self.client = APIClient()
        self.user = UserFactory()
        self.candidate = CandidateFactory(user=self.user,created_by=self.user)
        self.user_without_profile = UserFactory()

        self.token = self._get_token(self.user)
        self.token_without_profile = self._get_token(self.user_without_profile)
        
        self.url = reverse("delete_candidate")
    
    def _get_token(self, user):
        """Helper method to get JWT token"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)

    def tearDown(self):
        cache.clear()  # Clear cache after each test

    def test_delete_candidate_success(self):
        """Test successful candidate profile deletion"""
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {self.token}")
        response = self.client.delete(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_delete_candidate_not_found(self):
        """Test attempting to delete a non-existent candidate profile"""
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {self.token_without_profile}")
        response = self.client.delete(self.url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        
    def test_delete_candidate_unauthenticated(self):
        """Test attempting to delete a candidate profile without authentication"""
        self.client.logout()
        response = self.client.delete(self.url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
