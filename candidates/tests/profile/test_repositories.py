from django.dispatch import receiver
from django.test import TestCase, override_settings
from django.core.cache import cache
from candidates.models import Candidate
from businesses.models import Company
from candidates.repositories.candidate_repository import CandidateRepository
from candidates.models import AcademicEducation
from candidates.repositories.education import AcademicEducationRepository
from candidates.models import WorkExperience
from candidates.repositories.work_experienc import WorkExperienceRepository
from django.contrib.auth import get_user_model
from candidates.models import (
    Candidate,
    WorkExperience,
    AcademicEducation,
    Interview,
    Chat,
    Application,
)
from candidates.repositories.application_repository import ApplicationRepository
from candidates.repositories.chat_repository import ChatRepository
from candidates.repositories.interview_repository import InterviewRepository
from base.models import Skill, UserLanguage, SocialMediaPlatforms
from datetime import datetime


User = get_user_model()


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestCandidateRepository(TestCase):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        # Create a user for the candidate
        self.user = User.objects.create_user(
            username="testuser",
            first_name="Test",
            last_name="User",
            password="password123",
        )
        self.user1 = User.objects.create_user(
            username="testuser1",
            first_name="Test1",
            last_name="User1",
            password="password123",
        )

        # Initialize the repository
        self.repository = CandidateRepository()

        # Sample data for testing
        self.candidate_data = {
            "user": self.user,
            "linkedin_url": "https://linkedin.com/in/testuser",
            "portfolio_url": "https://portfolio.com/testuser",
            "gender": "Female",
            "date_of_birth": datetime(1990, 1, 1),
            "phone_number": "1234567890",
            "bio": "Experienced software developer",
            "is_employed": False,
            "started_working": datetime(2010, 6, 15),
        }

    def tearDown(self):
        cache.clear()  # Clear cache after each test

        # Create a candidate for testing
        self.candidate = Candidate.objects.create(**self.candidate_data)

    def test_create_candidate_success(self):
        new_candidate_data = {
            "user": self.user1,
            "gender": "Male",
            "date_of_birth": datetime(1995, 5, 5),
            "phone_number": "0987654321",
            "bio": "Aspiring data scientist",
            "is_employed": True,
            "started_working": datetime.now(),
        }
        response = self.repository.create_candidate(new_candidate_data)
        self.assertTrue(response.success)
        self.assertEqual(response.data.gender, "Male")

    def test_create_candidate_failure(self):
        response = self.repository.create_candidate({"invalid_field": "value"})
        self.assertFalse(response.success)

    def test_get_candidate_success(self):
        response = self.repository.get_candidate(self.candidate.id)
        self.assertTrue(response.success)
        self.assertEqual(response.data.phone_number, "1234567890")

    def test_get_candidate_not_found(self):
        response = self.repository.get_candidate(9999)
        self.assertFalse(response.success)

    def test_update_candidate_success(self):
        updated_data = {
            "bio": "Updated bio",
            "is_employed": True,
        }
        response = self.repository.update_candidate(self.candidate.id, updated_data)
        self.assertTrue(response.success)
        self.assertEqual(response.data.bio, "Updated bio")
        self.assertEqual(response.data.is_employed, True)

    def test_update_candidate_not_found(self):
        response = self.repository.update_candidate(9999, {"bio": "Updated bio"})
        self.assertFalse(response.success)

    def test_delete_candidate_success(self):
        response = self.repository.delete_candidate(self.user.id)
        self.assertTrue(response.success)

    def test_delete_candidate_not_found(self):
        response = self.repository.delete_candidate(9999)
        self.assertFalse(response.success)


class TestAcademicEducationRepository(TestCase):
    def setUp(self):
        self.repository = AcademicEducationRepository()
        self.sample_data = {
            "degree_attained": "Bachelor's in Computer Science",
            "started_year": 2015,
            "ended_year": 2019,
        }
        self.education = AcademicEducation.objects.create(**self.sample_data)

    def test_create_academic_education_success(self):
        response = self.repository.create_academic_education(self.sample_data)
        self.assertTrue(response.success)
        self.assertEqual(
            response.data.degree_attained, self.sample_data["degree_attained"]
        )

    def test_create_academic_education_failure(self):
        response = self.repository.create_academic_education({"invalid_field": "value"})
        self.assertFalse(response.success)
        self.assertIn("Failed to create", response.message)

    def test_get_academic_education_by_id_success(self):
        response = self.repository.get_academic_education_by_id(self.education.id)
        self.assertTrue(response.success)
        self.assertEqual(response.data.id, self.education.id)

    def test_get_academic_education_by_id_not_found(self):
        response = self.repository.get_academic_education_by_id(9999)
        self.assertFalse(response.success)
        self.assertEqual(response.message, "Academic education not found")

    def test_update_academic_education_success(self):
        updated_data = {"degree_attained": "Master's in Computer Science"}
        response = self.repository.update_academic_education(
            self.education.id, updated_data
        )
        self.assertTrue(response.success)
        self.assertEqual(response.data.degree_attained, updated_data["degree_attained"])

    def test_update_academic_education_not_found(self):
        response = self.repository.update_academic_education(
            9999, {"degree_attained": "Master's"}
        )
        self.assertFalse(response.success)

    def test_delete_academic_education_success(self):
        response = self.repository.delete_academic_education(self.education.id)
        self.assertTrue(response.success)

    def test_delete_academic_education_not_found(self):
        response = self.repository.delete_academic_education(9999)
        self.assertFalse(response.success)


class TestWorkExperienceRepository(TestCase):
    def setUp(self):
        self.repository = WorkExperienceRepository()
        self.sample_data = {
            "company_name": "Tech Corp",
            "employment_title": "Software Engineer",
            "started_from": "2020-01-01",
            "ended_at": "2023-01-01",
        }
        self.work_experience = WorkExperience.objects.create(**self.sample_data)

    def test_create_work_experience_success(self):
        response = self.repository.create_work_experience(self.sample_data)
        self.assertTrue(response.success)
        self.assertEqual(response.data.company_name, self.sample_data["company_name"])

    def test_create_work_experience_failure(self):
        response = self.repository.create_work_experience({"invalid_field": "value"})
        self.assertFalse(response.success)
        self.assertIn("Failed to create work experience", response.message)

    def test_get_work_experience_by_id_success(self):
        response = self.repository.get_work_experience_by_id(self.work_experience.id)
        self.assertTrue(response.success)
        self.assertEqual(response.data.id, self.work_experience.id)

    def test_get_work_experience_by_id_not_found(self):
        response = self.repository.get_work_experience_by_id(9999)
        self.assertFalse(response.success)

    def test_update_work_experience_success(self):
        updated_data = {"employment_title": "Senior Software Engineer"}
        response = self.repository.update_work_experience(
            self.work_experience.id, updated_data
        )
        self.assertTrue(response.success)
        self.assertEqual(
            response.data.employment_title, updated_data["employment_title"]
        )

    def test_update_work_experience_not_found(self):
        response = self.repository.update_work_experience(
            9999, {"employment_title": "Manager"}
        )
        self.assertFalse(response.success)

    def test_delete_work_experience_success(self):
        response = self.repository.delete_work_experience(self.work_experience.id)
        self.assertTrue(response.success)
        self.assertFalse(
            WorkExperience.objects.filter(pk=self.work_experience.id).exists()
        )

    def test_delete_work_experience_not_found(self):
        response = self.repository.delete_work_experience(9999)
        self.assertFalse(response.success)


class TestInterviewRepository(TestCase):
    def setUp(self):
        self.user = User.objects.create(username="test_user")
        self.company = Company.objects.create(
            name="Test Company", email="<EMAIL>"
        )
        self.interview_data = {
            "user": self.user,
            "business": self.company,
            "interview_date": "2024-12-20 10:00:00",
            "from_time": "10:00:00",
            "to_time": "11:00:00",
            "status": "Pending",
            "message": "Initial interview",
        }

    def test_create_interview(self):
        response = InterviewRepository.create_interview(self, data=self.interview_data)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

    def test_update_interview(self):
        interview = Interview.objects.create(**self.interview_data)
        updated_data = {"status": "Due", "message": "Updated interview"}
        response = InterviewRepository.update_interview(
            self, interview_id=interview.id, data=updated_data
        )
        self.assertTrue(response.success)
        self.assertEqual(response.data.status, "Due")

    def test_delete_interview(self):
        interview = Interview.objects.create(**self.interview_data)
        response = InterviewRepository.delete_interview(self, interview_id=interview.id)
        self.assertTrue(response.success)
        self.assertIsNone(response.data)
        self.assertFalse(Interview.objects.filter(id=interview.id).exists())

    def test_get_interview(self):
        interview = Interview.objects.create(**self.interview_data)
        response = InterviewRepository.get_interview(self, interview_id=interview.id)
        self.assertTrue(response.success)
        self.assertEqual(response.data.id, interview.id)

    def test_get_interviews_by_user_id(self):
        interview = Interview.objects.create(**self.interview_data)
        response = InterviewRepository.get_interviews_by_user_id(
            self, user_id=self.user.id
        )
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0].id, interview.id)


class TestChatRepository(TestCase):
    def setUp(self):
        self.repository = ChatRepository()
        self.user1 = User.objects.create(username="user1")
        self.user2 = User.objects.create(username="user2")
        self.chat_data = {
            "sender": self.user1,
            "receiver": self.user2,
            "message": "Hello, user2!",
        }

    def test_create_chat(self):
        response = self.repository.create_chat(
            sender_id=self.chat_data["sender"].id,
            receiver_id=self.chat_data["receiver"].id,
            message=self.chat_data["message"],
        )
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.sender.id, self.user1.id)

    def test_get_chat_by_id(self):
        chat = Chat.objects.create(**self.chat_data)
        response = self.repository.get_chat_by_id(chat.message_id)
        self.assertTrue(response.success)
        self.assertEqual(response.data.message_id, chat.message_id)
        self.assertEqual(response.data.sender.id, self.user1.id)

    def test_get_chat_by_id_failure(self):
        response = self.repository.get_chat_by_id(9999)
        self.assertFalse(response.success)

    def test_get_chats_by_sender_id(self):
        chat = Chat.objects.create(**self.chat_data)
        response = self.repository.get_chats_by_sender_id(self.user1.id)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0].message_id, chat.message_id)
        self.assertEqual(response.data[0].sender.id, self.user1.id)

    def test_get_chats_by_sender_id_failure(self):
        response = self.repository.get_chats_by_sender_id(0)
        self.assertFalse(response.success)

    def test_get_chats_by_receiver_id(self):
        chat = Chat.objects.create(**self.chat_data)
        response = self.repository.get_chats_by_receiver_id(self.user2.id)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0].message_id, chat.message_id)
        self.assertEqual(response.data[0].sender.id, self.user1.id)

    def test_get_chats_by_receiver_id_failure(self):
        response = self.repository.get_chats_by_receiver_id(0)
        self.assertFalse(response.success)


class TestApplicationRepository(TestCase):
    def setUp(self):
        self.repository = ApplicationRepository()
        self.user = User.objects.create(username="test_user", password="tesT12@3")
        self.candidate = Candidate.objects.create(
            user=self.user,
        )

        self.company = Company.objects.create(
            name="Test Company", email="<EMAIL>"
        )
        self.application_data = {"applicant": self.candidate, "status": "Pending"}

    def test_create_application(self):
        response = Application.objects.create(**self.application_data)
        self.assertTrue(response.status)
        self.assertIsNotNone(response)
        self.assertEqual(response.applicant.id, self.candidate.id)

    def test_get_application_by_id_failure(self):
        response = self.repository.get_application(9999)
        self.assertFalse(response.success)
        self.assertIsNone(response.data)

    def test_get_application_by_id(self):
        application = Application.objects.create(**self.application_data)
        response = self.repository.get_application(application.id)
        self.assertTrue(response.success)
        self.assertIsNotNone(response)
        self.assertEqual(response.data.applicant.id, self.candidate.id)

    def test_update_application(self):
        application = Application.objects.create(**self.application_data)
        updated_data = {"status": "Accepted"}
        response = self.repository.update_application(application.id, updated_data)
        self.assertTrue(response.success)
        self.assertIsNotNone(response)
        self.assertEqual(response.data.status, "Accepted")

    def test_update_application_failure(self):
        response = self.repository.update_application(9999, {"status": "Accepted"})
        self.assertFalse(response.success)
        self.assertIsNone(response.data)

    def test_delete_application(self):
        application = Application.objects.create(**self.application_data)
        response = self.repository.delete_application(application.id)
        self.assertTrue(response.success)
        self.assertIsNone(response.data)
        self.assertFalse(Application.objects.filter(id=application.id).exists())

    def test_delete_application_failure(self):
        response = self.repository.delete_application(9999)
        self.assertFalse(response.success)
        self.assertIsNone(response.data)

class TestCandidateRepository(TestCase):
    def setUp(self):
        self.repository = CandidateRepository()
        self.user = User.objects.create(username="test_user")
        self.company = Company.objects.create(
            name="Test Company", email="<EMAIL>"
        )
    
    def test_get_all_candidates_success(self):
        response = self.repository.get_all_candidates()
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
    
