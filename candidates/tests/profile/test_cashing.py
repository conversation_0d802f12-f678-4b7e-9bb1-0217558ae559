from django.test import TestCase

from base.factory import UserFactory
from base.services.caching import CachingService
from candidates.factory import CandidateFactory
from candidates.models import Candidate
from candidates.services.candidate import CandidateService
from candidates.views.serializers import CandidateDataSerializer
from django.contrib.auth.models import User


class TestQueryFromDatabase(TestCase):
    def setUp(self):
        self.cache = CachingService()
        # Clear cache before each test to ensure test isolation
        self.cache.clearCache()
        # self.user = UserFactory(
        #     first_name="search_test",
        #     last_name="last_test",
        #     email="<EMAIL>",
        # )
        self.candidate_service = CandidateService()
        # self.candidate_active = CandidateFactory(
        #     user=self.user,
        #     is_employed=True,
        #     is_active=True,
        #     gender="Male",
        #     bio="Python developer",
        # )
        # self.candidate_inactive = CandidateFactory(
        #     is_employed=False, is_active=False, gender="Female", bio="Java developer"
        # )
        # self.candidate_other = CandidateFactory(
        #     is_employed=True,
        #     is_active=True,
        #     gender="Other",
        #     bio="Go developer",
        #     user=UserFactory(
        #         first_name="other", last_name="other_last", email="<EMAIL>"
        #     ),
        # )

    def tearDown(self):
        # Clear cache after each test to ensure test isolation
        self.cache.clearCache()

    """Test class for query cashing functionality in the profile module."""

    def test_search_by_first_name(self):
        user = UserFactory(first_name="search_testt", last_name="Doe")
        candidate = CandidateFactory(user=user, is_active=True, is_verified=True)

        params = {"q": "search_testt", "page_size": 10, "page": 1}
        response = self.candidate_service.handle_query(params)

        self.assertTrue(response.success)
        self.assertGreaterEqual(len(response.data["candidates"]), 1)

        first_names = [c["user"]["first_name"] for c in response.data["candidates"]]
        print(first_names)
        self.assertIn("search_testt", first_names)


    def test_search_by_last_name(self):
        user = UserFactory(first_name="search_testt", last_name="last_testt")
        CandidateFactory(user=user, is_active=True, is_verified=True)

        params = {"q": "last_testt", "page_size": 10, "page": 1}
        response = self.candidate_service.handle_query(params)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["candidates"]), 1)
        self.assertEqual(
            response.data["candidates"][0]["user"]["last_name"], "last_testt"
        )

    def test_search_by_email(self):
        user = UserFactory(first_name="search_testt", last_name="Doe", email="<EMAIL>"
        )
        CandidateFactory(user=user, is_active=True, is_verified=True)

        params = {"q": "<EMAIL>", "page_size": 10, "page": 1}
        response = self.candidate_service.handle_query(params)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["candidates"]), 1)
        self.assertEqual(
            response.data["candidates"][0]["user"]["email"], "<EMAIL>"
        )

    def test_search_by_bio(self):
        user = UserFactory(first_name="search_testt", last_name="Doe")
        CandidateFactory(user=user, is_active=True, is_verified=True, bio="Python developer")
        params = {"q": "Python developer", "page_size": 10, "page": 1}
        response = self.candidate_service.handle_query(params)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["candidates"]), 1)
        self.assertEqual(response.data["candidates"][0]["bio"], "Python developer")

    def test_filter_by_is_employed(self):
        user = UserFactory(first_name="employed_test", last_name="Doe")
        CandidateFactory(user=user, is_active=True, is_verified=True, is_employed=True)
        user2 = UserFactory(first_name="employed_test2", last_name="Doe")
        CandidateFactory(user=user2, is_active=True, is_verified=True, is_employed=True)
        params = {"is_employed": True, "page_size": 10, "page": 1}
        response = self.candidate_service.handle_query(params)
        self.assertTrue(response.success)
        self.assertGreaterEqual(
            len(response.data["candidates"]), 2
        )
        for c in response.data["candidates"]:
            self.assertTrue(c["is_employed"])

    def test_filter_by_is_active(self):
        user = UserFactory(first_name="active_test", last_name="Doe")
        CandidateFactory(user=user, is_active=True, is_verified=True)
        user2 = UserFactory(first_name="active_test2", last_name="Doe")
        CandidateFactory(user=user2, is_active=True, is_verified=True)
        params = {"is_active": True, "page_size": 10, "page": 1}
        response = self.candidate_service.handle_query(params)
        self.assertTrue(response.success)
        self.assertGreaterEqual(len(response.data["candidates"]), 2)
        for c in response.data["candidates"]:
            self.assertTrue(c["is_active"])

    def test_filter_by_gender(self):
        user = UserFactory(first_name="male_test", last_name="Doe")
        CandidateFactory(user=user, is_active=True, is_verified=True, gender="Male")
        params = {"gender": "Male", "page_size": 10, "page": 1}
        response = self.candidate_service.handle_query(params)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["candidates"]), 1)
        self.assertEqual(response.data["candidates"][0]["gender"], "Male")

    def test_search_and_filter_combined(self):
        user = UserFactory(first_name="combined_test", last_name="Doe")
        CandidateFactory(
            user=user, is_active=True, is_verified=True, is_employed=True, gender="Male", bio="Python developer"
        )
        params = {
            "q": "Python",
            "is_employed": True,
            "gender": "Male",
            "page_size": 10,
            "page": 1,
        }
        response = self.candidate_service.handle_query(params)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["candidates"]), 1)
        self.assertEqual(response.data["candidates"][0]["bio"], "Python developer")
        self.assertTrue(response.data["candidates"][0]["is_employed"])
        self.assertEqual(response.data["candidates"][0]["gender"], "Male")

    def test_pagination(self):
        # Create 2 candidates to test pagination
        user1 = UserFactory(first_name="pagination_test1", last_name="Doe")
        CandidateFactory(user=user1, is_active=True, is_verified=True)
        user2 = UserFactory(first_name="pagination_test2", last_name="Doe")
        CandidateFactory(user=user2, is_active=True, is_verified=True)

        params = {"page_size": 1, "page": 2}
        response = self.candidate_service.handle_query(params)
        self.assertTrue(response.success)
        self.assertEqual(response.data["current_page"], 2)
        self.assertEqual(len(response.data["candidates"]), 1)

    def test_cache_hit_and_miss(self):
        user = UserFactory(first_name="cache_test", last_name="Doe")
        CandidateFactory(user=user, is_active=True, is_verified=True)
        params = {"q": "search_test", "page_size": 10, "page": 1}
        # First call: should hit DB and cache result
        response = self.candidate_service.handle_query(params)
        self.assertTrue(response.success)
        # Second call: should hit cache
        response_cached = self.candidate_service.handle_query(params)
        self.assertTrue(response_cached.success)
        self.assertEqual(len(response.data), len(response_cached.data))
