from django.test import TestCase, override_settings
from django.core.cache import cache
from django.core.exceptions import ValidationError
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
from accounts.services.profile_service import ProfileService
from base.models import (
    Address,
    City,
    Country,
    Skill,
    SocialMediaPlatforms,
    State,
    UserLanguage,
)
from candidates.services.application import ApplicationService
from candidates.services.candidate import CandidateService
from candidates.services.education import AcademicEducationService
from candidates.services.work_experienc import WorkExperienceService
from candidates.models import (
    AcademicEducation,
    Application,
    Candidate,
    Interview,
    Chat,
    WorkExperience,
)
from businesses.models import Company
from candidates.services.interview import InterviewService
from candidates.services.chat import ChatService
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, time


User = get_user_model()


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestCandidateService(TestCase):
    def setUp(self):
        cache.clear()
        self.service = CandidateService()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="password123"
        )
        self.valid_data = {
            "user": self.user,
            "name": "Test Candidate",
            "linkedin_url": "https://linkedin.com/in/testcandidate",
            "is_employed": True,
            "date_of_birth": "1990-01-01",
            "started_working": "2010-01-01",
        }
        self.candidate = Candidate.objects.create(**self.valid_data)

    def tearDown(self):
        cache.clear()

    def test_create_candidate_success(self):
        new_user = User.objects.create_user(
            username="newuser", email="<EMAIL>", password="password123"
        )
        data = {
            "user": new_user,
            "name": "New Candidate",
            "linkedin_url": "https://linkedin.com/in/newcandidate",
            "is_employed": False,
            "date_of_birth": "1985-05-05",
            "started_working": "2005-05-05",
        }
        response = self.service.create_candidate(data)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.name, "New Candidate")

    def test_get_candidate_success(self):
        response = self.service.get_candidate(self.user.id)
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, self.candidate.name)

    def test_get_candidate_not_found(self):
        response = self.service.get_candidate("99999")
        self.assertFalse(response.success)

    def test_update_candidate_success(self):
        updated_data = {
            "name": "Updated Candidate",
            "is_employed": False,
        }
        response = self.service.update_candidate(self.candidate.id, updated_data)
        self.assertTrue(response.success)
        updated_candidate = Candidate.objects.get(id=self.candidate.id)
        self.assertEqual(updated_candidate.name, "Updated Candidate")
        self.assertFalse(updated_candidate.is_employed)

    def test_delete_candidate_success(self):
        response = self.service.delete_candidate(self.user.id)
        print(response)
        self.assertTrue(response.success)
        self.assertFalse(Candidate.objects.filter(id=self.candidate.id).exists())

    def test_get_all_candidates_success(self):
        response = self.service.get_all_candidates()
        self.assertTrue(response.success)
        self.assertGreaterEqual(len(response.data), 1)


class AcademicEducationServiceTests(TestCase):
    def setUp(self):
        self.user = User.objects.create(
            username="testuser",
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
        )

        self.service = AcademicEducationService()

        self.valid_data = {
            "school_name": "Test University",
            "degree_attained": "Bachelor's in Computer Science",
            "started_year": 2018,
            "ended_year": 2022,
            "name": "Test Education",
        }

        self.test_education = AcademicEducation.objects.create(
            school_name="Test University",
            degree_attained="Bachelor's in Computer Science",
            started_year=2018,
            ended_year=2022,
            name="Test Education",
        )

    def test_create_academic_education_success(self):
        response = self.service.create_academic_education(self.valid_data)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data["school_name"], "Test University")
        self.assertEqual(
            response.data["degree_attained"], "Bachelor's in Computer Science"
        )

    def test_create_academic_education_missing_required_field(self):
        invalid_data = self.valid_data.copy()
        del invalid_data["school_name"]
        response = self.service.create_academic_education(invalid_data)
        self.assertFalse(response.success)
        self.assertIn("field: school_name", response.message.lower())

    def test_get_academic_education_by_id(self):
        response = self.service.get_academic_education_by_id(self.test_education.id)
        self.assertTrue(response.success)
        self.assertEqual(response.data["school_name"], "Test University")

    def test_get_academic_education_invalid_id(self):
        response = self.service.get_academic_education_by_id("invalid-uuid")
        self.assertFalse(response.success)

    def test_update_academic_education(self):
        update_data = {
            "school_name": "Updated University",
            "degree_attained": "Master's in Computer Science",
        }
        response = self.service.update_academic_education(
            self.test_education.id, update_data
        )
        self.assertTrue(response.success)
        self.assertEqual(response.data["school_name"], "Updated University")
        self.assertEqual(
            response.data["degree_attained"], "Master's in Computer Science"
        )

    def test_delete_academic_education(self):
        response = self.service.delete_academic_education(self.test_education.id)
        self.assertTrue(response.success)

        get_response = self.service.get_academic_education_by_id(self.test_education.id)
        self.assertFalse(get_response.success)


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestWorkExperienceService(TestCase):
    def setUp(self):
        cache.clear()
        self.mock_repository = Mock()
        self.service = WorkExperienceService(repository=self.mock_repository)

    def tearDown(self):
        cache.clear()

    def test_create_work_experience_success(self):
        start_date = datetime.now() - timedelta(days=365)
        mock_response = Mock()
        mock_response.success = True
        mock_response.data.id = "1"
        mock_response.data.employment_title = "Software Engineer"
        mock_response.data.company_name = "Tech Corp"
        mock_response.data.started_from = start_date
        mock_response.data.ended_at = None
        mock_response.data.date_created = datetime.now()
        mock_response.data.date_updated = datetime.now()

        self.mock_repository.create_work_experience.return_value = mock_response

        data = {
            "employment_title": "Software Engineer",
            "company_name": "Tech Corp",
            "started_from": start_date,
        }
        result = self.service.create_work_experience(data)

        self.assertEqual(result.data["employment_title"], "Software Engineer")
        self.assertEqual(result.data["company_name"], "Tech Corp")
        self.mock_repository.create_work_experience.assert_called_once_with(data)

    def test_create_work_experience_future_start_date(self):
        future_date = datetime.now() + timedelta(days=365)

        with self.assertRaises(ValidationError) as context:
            self.service.create_work_experience(
                {
                    "employment_title": "Software Engineer",
                    "company_name": "Tech Corp",
                    "started_from": future_date,
                }
            )

        self.assertIn("Start date cannot be in the future", str(context.exception))

    def test_create_work_experience_invalid_end_date(self):
        start_date = datetime.now() - timedelta(days=365)
        end_date = start_date - timedelta(days=30)

        with self.assertRaises(ValidationError) as context:
            self.service.create_work_experience(
                {
                    "employment_title": "Software Engineer",
                    "company_name": "Tech Corp",
                    "started_from": start_date,
                    "ended_at": end_date,
                }
            )

        self.assertIn(
            "Start date cannot be later than end date", str(context.exception)
        )

    def test_update_work_experience_success(self):
        start_date = datetime.now() - timedelta(days=365)
        get_mock_response = Mock()
        get_mock_response.success = True
        get_mock_response.data.id = "1"
        get_mock_response.data.employment_title = "Software Engineer"
        get_mock_response.data.company_name = "Tech Corp"
        get_mock_response.data.started_from = start_date
        get_mock_response.data.ended_at = None
        self.mock_repository.get_work_experience_by_id.return_value = get_mock_response

        update_mock_response = Mock()
        update_mock_response.success = True
        update_mock_response.data.id = "1"
        update_mock_response.data.employment_title = "Senior Software Engineer"
        update_mock_response.data.company_name = "Tech Corp"
        update_mock_response.data.started_from = start_date
        update_mock_response.data.ended_at = datetime.now()
        self.mock_repository.update_work_experience.return_value = update_mock_response

        result = self.service.update_work_experience(
            "1", {"employment_title": "Senior Software Engineer"}
        )

        self.assertEqual(result["employment_title"], "Senior Software Engineer")
        self.assertIsNotNone(result["ended_at"])
        self.mock_repository.update_work_experience.assert_called_once_with(
            "1", {"employment_title": "Senior Software Engineer"}
        )

    def test_delete_work_experience_success(self):
        mock_response = Mock()
        mock_response.success = True

        self.mock_repository.delete_work_experience.return_value = mock_response

        result = self.service.delete_work_experience("1")

        self.mock_repository.delete_work_experience.assert_called_once_with("1")


class InterviewServiceTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create(
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            password="testpass123",
        )

        self.company = Company.objects.create(
            name="Test Company", email="<EMAIL>"
        )

        self.interview = Interview.objects.create(
            user=self.user,
            business=self.company,
            interview_date=timezone.now(),
            from_time=time(10, 0),
            to_time=time(11, 0),
            location="Online",
            status="Pending",
            message="Test interview",
        )

        self.service = InterviewService()

    def test_create_interview(self):
        data = {
            "user": self.user,
            "business": self.company,
            "interview_date": timezone.now(),
            "from_time": "10:00",
            "to_time": "11:00",
            "location": "Online",
            "status": "Pending",
            "message": "New test interview",
        }

        response = self.service.create_interview(data)
        self.assertTrue(response.success)

    def test_update_interview(self):
        update_data = {
            "message": "Updated test interview",
            "status": "Due",
        }

        response = self.service.update_interview(self.interview.id, update_data)
        print("Updated test interview:", response)
        self.assertTrue(response.success)

        self.assertEqual(response.data.status, "Due")

    def test_delete_interview(self):
        response = self.service.delete_interview(self.interview.id)
        self.assertTrue(response.success)

        with self.assertRaises(Interview.DoesNotExist):
            Interview.objects.get(id=self.interview.id)

    def test_get_interview(self):
        response = self.service.get_interview(self.interview.id)
        self.assertTrue(response.success)
        self.assertEqual(response.data.id, self.interview.id)

    def test_get_interviews_by_user_id(self):
        response = self.service.get_interviews_by_user_id(self.user.id)
        self.assertTrue(response.success)
        self.assertEqual(
            response.message, "Candidate interviews retrieved successfully"
        )
        self.assertEqual(len(response.data), 1)

    def test_get_interviews_by_company_slug(self):
        response = self.service.get_interviews_by_company_slug(self.company.slug)
        print(response)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 1)

    def test_invalid_company_id(self):
        response = self.service.get_interviews_by_company_slug(999)
        self.assertFalse(response.success)


# class TestChatService(TestCase):
#     def setUp(self):
#         self.service = ChatService()
#         self.user1 = User.objects.create(username="user1")
#         self.user2 = User.objects.create(username="user2")
#         self.chat_data = {
#             "sender": self.user1,
#             "receiver": self.user2,
#             "message": "Hello User 2",
#         }

#     def test_create_chat_success(self):
#         response = self.service.create_chat(
#             sender_id=self.chat_data["sender"].id,
#             receiver_id=self.chat_data["receiver"].id,
#             message=self.chat_data["message"],
#         )
#         self.assertTrue(response.success)
#         self.assertIsNotNone(response.data)

#     def test_create_chat_failure(self):
#         response = self.service.create_chat(
#             sender_id=self.chat_data["sender"].id,
#             receiver_id=self.chat_data["receiver"].id,
#             message="",
#         )
#         self.assertFalse(response.success)

#     def test_get_chats_by_sender_id(self):
#         chat1 = Chat.objects.create(**self.chat_data)
#         chat2 = Chat.objects.create(
#             sender=self.chat_data["receiver"],
#             receiver=self.chat_data["sender"],
#             message="Hello User 1",
#         )
#         response = self.service.get_chats_by_sender_id(self.chat_data["sender"].id)
#         self.assertTrue(response.success)

#     def test_get_chats_by_receiver_id_no_chats(self):
#         response = self.service.get_chats_by_receiver_id(self.chat_data["receiver"].id)
#         self.assertTrue(response.success)

#     def test_get_chats_by_receiver_id(self):
#         chat1 = Chat.objects.create(**self.chat_data)
#         chat2 = Chat.objects.create(
#             sender=self.chat_data["receiver"],
#             receiver=self.chat_data["sender"],
#             message="Hello User 1",
#         )
#         response = self.service.get_chats_by_receiver_id(self.chat_data["receiver"].id)
#         self.assertTrue(response.success)

#     def test_get_chats_by_receiver_id_failure(self):
#         response = self.service.get_chats_by_receiver_id(0)
#         self.assertFalse(response.success)


# class TestApplicationService(TestCase):
#     def setUp(self):
#         self.service = ApplicationService()
#         self.user = User.objects.create(username="test_user", password="tesT12@3")
#         self.candidate = Candidate.objects.create(
#             user=self.user, location="Test Location"
#         )

#         self.company = Company.objects.create(
#             name="Test Company", email="<EMAIL>"
#         )
#         self.application_data = {"applicant": self.candidate, "status": "Pending"}

#     def test_create_application_success(self):
#         response = Application.objects.create(**self.application_data)
#         self.assertTrue(response.status)
#         self.assertIsNotNone(response)

#     def test_get_application(self):
#         application = Application.objects.create(**self.application_data)
#         response = self.service.get_application(application.id)
#         self.assertTrue(response.success)
#         self.assertEqual(response.data.id, application.id)

#     def test_get_application_failure(self):
#         response = self.service.get_application(0)
#         self.assertFalse(response.success)

#     def test_update_application(self):
#         application = Application.objects.create(**self.application_data)
#         data = {"status": "Hired"}
#         response = self.service.update_application(application.id, data)
#         self.assertTrue(response.success)
#         self.assertEqual(response.data.status, data["status"])

#     def test_update_application_failure(self):
#         response = self.service.update_application(0, {})
#         self.assertFalse(response.success)

#     def test_delete_application(self):
#         application = Application.objects.create(**self.application_data)
#         response = self.service.delete_application(application.id)
#         self.assertTrue(response.success)
#         self.assertFalse(Application.objects.filter(id=application.id).exists())

#     def test_delete_application_failure(self):
#         response = self.service.delete_application(0)
#         self.assertFalse(response.success)


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestProfileService(TestCase):
    def setUp(self):
        cache.clear()
        self.service = ProfileService()
        self.user = User.objects.create(username="test_user", password="tesT12@3")
        self.candidate = Candidate.objects.create(user=self.user)

    def tearDown(self):
        cache.clear()

    def test_update_profile_skills(self):
        skills = [
            {
                "name": "New skill",
                "category_name": "New Category",
            },
            {
                "name": "Updated skill",
                "category_name": "Updated Category",
            },
            {
                "name": "Deleted skill",
            },
        ]
        response = self.service.update_skills(self.candidate, skills)
        self.assertTrue(response.success)

    def test_update_profile_with_skills(self):
        skills = [
            {
                "name": "New skill",
                "category_name": "New Category",
            },
            {
                "name": "Updated skill",
                "category_name": "Updated Category",
            },
            {
                "name": "Deleted skill",
                "category_name": "Updated Category",
            },
        ]
        response = self.service.update_profile(self.user, {"skills": skills})
        self.assertTrue(response.success)

    def test_update_profile_with_address(self):
        data = {
            "address": {
                "address": "123 Main St",
                "city": "New York",
                "state": "NY",
                "country": "USA",
                "postal_code": "10001",
            }
        }
        response = self.service.update_profile(self.user, data)
        self.assertTrue(response.success)

    def test_update_profile_with_education(self):
        education = [
            {
                "school_name": "Test University",
                "degree_attained": "Bachelor's",
                "started_year": 2015,
                "ended_year": 2020,
            },
            {
                "school_name": "Updated University",
                "degree_attained": "Master's",
                "started_year": 2020,
                "ended_year": 2022,
            },
            {
                "school_name": "Deleted University",
                "degree_attained": "PhD",
                "started_year": 2022,
                "ended_year": 2024,
            },
        ]
        response = self.service.update_profile(self.user, {"education_list": education})
        print(f"test update_profile with education: {response}")
        self.assertTrue(response.success)
    
    def test_complete_profile(self):
        response = self.service.complete_profile([self.candidate.id])
        self.assertTrue(response.success)

    def test_complete_profile_failure(self):
        response = self.service.complete_profile([0])
        self.assertFalse(response.success)


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestCandidateProfileStatus(TestCase):
    def setUp(self):
        cache.clear()
        self.service = ProfileService()

        self.user = User.objects.create(username="johndoe", email="<EMAIL>")
        country = Country.objects.create(country_code="RW", name="Rwanda")
        state = State.objects.create(country_name=country)
        city = City.objects.create(state_name=state)
        address = Address.objects.create(
            street_address="123 Test Street", city_name=city
        )
        skill1 = Skill.objects.create(name="Python")
        skill2 = Skill.objects.create(name="Django")
        work_experience = WorkExperience.objects.create(
            company_name="XYZ Corp",
            employment_title="Developer",
            started_from="2020-01-01",
        )
        academic_education = AcademicEducation.objects.create(
            school_name="Test University",
            degree_attained="Bachelor's",
            started_year=2015,
            ended_year=2020,
        )
        language = UserLanguage.objects.create(name="English")
        social_platform = SocialMediaPlatforms.objects.create(
            handle="GitHub", link="https://github.com/johndoe"
        )

        self.complete_candidate = Candidate.objects.create(
            user=self.user,
            profile_picture="profile.png",
            resume="resume.pdf",
            is_employed=True,
            location=address,
            linkedin_url="https://linkedin.com/in/johndoe",
            portfolio_url="https://portfolio.com/johndoe",
            gender="Male",
            bio="Experienced software developer",
            date_of_birth="1990-01-01",
            started_working="2015-01-01",
            phone_number="+9876543210",
        )
        self.complete_candidate.skills.add(skill1, skill2)
        self.complete_candidate.work_experience.add(work_experience)
        self.complete_candidate.academic_education.add(academic_education)
        self.complete_candidate.languages.add(language)
        self.complete_candidate.social_platforms.add(social_platform)

        self.user_incomplete = User.objects.create(
            username="janedoe", email="<EMAIL>"
        )
        self.incomplete_candidate = Candidate.objects.create(
            user=self.user_incomplete,
            is_employed=False,
            location=address,
        )
        self.incomplete_candidate.skills.add(skill1)

    def tearDown(self):
        cache.clear()

    def test_complete_candidate_profile_status(self):
        response = self.service.get_profile_status(self.user)
        self.assertEqual(response.success, True)
        self.assertEqual(response.data["complete"], True)
        self.assertEqual(response.data["current_step"], 5)

    def test_incomplete_candidate_profile_status(self):
        response = self.service.get_profile_status(self.user_incomplete)
        self.assertEqual(response.success, True)
        # self.assertEqual(response.data["complete"], False)
        self.assertEqual(response.data["current_step"], 2)
