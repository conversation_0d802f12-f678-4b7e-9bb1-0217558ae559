from django.test import TestCase
from base.factory import UserFactory
from businesses.factory import CompanyFactory
from candidates.factory import InterviewFactory
from jobs.factory import JobFactory
from rest_framework.test import APIClient
from datetime import datetime, timedelta
import jwt
from core.settings import SECRET_KEY


class CandidateInterviewsEndpointsCase(TestCase):
    def setUp(self):
        self.user = UserFactory()
        self.user2 = UserFactory()
        self.business = CompanyFactory(
            created_by=self.user,
        )
        self.job = JobFactory(company_name=self.business)
        self.job2 = JobFactory(company_name=self.business)
        self.job3 = JobFactory(company_name=self.business)
        self.interview = InterviewFactory(
                user=self.user2,
                business=self.business,
                job_applied=self.job,
                from_time="10:00",
                to_time="11:00"
            )
        self.interview2 = InterviewFactory(
                user=self.user2,
                business=self.business,
                job_applied=self.job2,
                from_time="10:00",
                to_time="11:00"
            )
        self.interview3 = InterviewFactory(
                user=self.user2,
                business=self.business,
                job_applied=self.job3,
                from_time="10:00",
                to_time="11:00"
        )

        token_payload = {
            "user_id": self.user2.id,
            "exp": datetime.now() + timedelta(days=1),
        }
        jwt_token = jwt.encode(token_payload, SECRET_KEY, algorithm="HS256")

        self.client = APIClient()

        self.client.force_authenticate(user=self.user2)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {jwt_token}")

        self.endpoint = f"/api/candidates/interviews/"

    def test_get_candidate_interviews(self):
        response = self.client.get(self.endpoint)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 3)
    
    def test_get_candidate_interviews_unauthorized(self):
        self.client.logout()
        response = self.client.get(self.endpoint)
        self.assertEqual(response.status_code, 403)
    
    def test_get_candidate_interviews_no_token(self):
        self.client.credentials()
        response = self.client.get(self.endpoint)
        self.assertEqual(response.status_code, 403)
