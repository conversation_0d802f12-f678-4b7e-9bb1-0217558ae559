from django.contrib.auth import get_user_model
from django.test import override_settings
from django.core.cache import cache
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework.authtoken.models import Token
from base.factory import UserFactory
from businesses.factory import CompanyFactory
from businesses.models import Company
from candidates.factory import ApplicationFactory, CandidateFactory
from candidates.models import Application, Candidate
from core.settings import SECRET_KEY
import jwt
from candidates.services.application import ApplicationService
from jobs.factory import JobFactory
from jobs.models import Job


User = get_user_model()
service = ApplicationService()
client = APIClient()


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestApplicationEndpoint(APITestCase):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="password123",
        )
        # generate jwt token
        token = jwt.encode({"user_id": self.user.id}, SECRET_KEY, algorithm="HS256")
        client.force_authenticate(user=self.user)
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {token}")
        self.url = "/api/candidates/applications"
        self.candidate = Candidate.objects.create(user=self.user, created_by=self.user)
        self.company = Company.objects.create(name="Test Company")
        self.job = Job.objects.create(
            company_name=self.company,
            name="Test Job",
            min_salary=1000,
            max_salary=5000,
            job_description="Test Job Description",
        )

    def tearDown(self):
        cache.clear()

    def test_save_application(self):
        save_url = f"/api/jobs/{self.job.slug}/apply/"
        response = client.post(save_url, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_save_application_unauthenticated(self):
        client.logout()
        save_url = f"/api/jobs/{self.job.slug}/apply/"
        response = client.post(save_url, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_get_applications(self):
        application = service.create_application(
            job_slug=self.job.slug, user_id=self.user.id
        )
        if application.data:
            application.data.save()
        response = client.get(f"{self.url}/")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_get_applications_unauthenticated(self):
        client.logout()
        response = client.get(f"{self.url}/")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_application(self):
        application = ApplicationFactory(applicant=self.candidate)
        update_data = {
            "status": "Confirmed",
        }

        response = client.patch(
            f"{self.url}/{application.id}/update/", update_data, format="json"
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_update_application_unauthenticated(self):
        client.logout()
        application = service.create_application(
            job_slug=self.job.slug, user_id=self.user.id
        )
        update_data = {
            "status": "Accepted",
        }
        endpoint = f"{self.url}/{application.data.id}/update/"
        response = client.patch(endpoint, update_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

