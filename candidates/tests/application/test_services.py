from django.test import TestCase, override_settings
from django.core.cache import cache
from candidates.services.application import ApplicationService
from django.contrib.auth import get_user_model
from candidates.models import Candidate
from jobs.models import Job
from businesses.models import Company


User = get_user_model()


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestApplicationService(TestCase):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        self.user = User.objects.create_user(username="testuser", password="password")
        self.service = ApplicationService()
        self.candidate = Candidate.objects.create(user=self.user)
        self.company = Company.objects.create(name="Test Company")
        self.job = Job.objects.create(
            company_name=self.company,
            name="Test Job",
            min_salary=1000,
            max_salary=5000,
            job_description="Test Job Description",
        )
        self.job2 = self.job = Job.objects.create(
            company_name=self.company,
            name="Test Job 2",
            min_salary=2000,
            max_salary=6000,
            job_description="Test Job Description 2",
        )

    def tearDown(self):
        cache.clear()  # Clear cache after each test

    def test_create_application(self):
        response = self.service.create_application(
            job_slug=self.job.slug, user_id=self.user.id
        )
        # print(response.message)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

    def test_create_application_fail(self):
        response = self.service.create_application(job_slug=self.job.slug, user_id=999)
        self.assertFalse(response.success)
        self.assertIsNone(response.data)

    def test_get_application(self):
        response = self.service.create_application(
            job_slug=self.job.slug, user_id=self.user.id
        )
        get_application = self.service.get_application(response.data.id)

        self.assertTrue(get_application.success)
        self.assertIsNotNone(get_application.data)

    def test_get_application_fail(self):
        response = self.service.get_application("")

        self.assertFalse(response.success)
        self.assertIsNone(response.data)

    def test_get_all_applications(self):
        application1 = self.service.create_application(
            job_slug=self.job.slug, user_id=self.user.id
        )
        application2 = self.service.create_application(
            job_slug=self.job2.slug, user_id=self.user.id
        )
        response = self.service.get_all_applications(self.user.id)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

    def test_get_all_applications_empty_id(self):
        response = self.service.get_all_applications(user_id=None)
        self.assertFalse(response.success)
        self.assertIsNone(response.data)

    def test_get_all_applications_wrong_id(self):
        response = self.service.get_all_applications(999)
        self.assertFalse(response.success)
        self.assertIsNone(response.data)

    def test_update_application(self):
        application = self.service.create_application(
            job_slug=self.job.slug, user_id=self.user.id
        )
        updated_data = {"status": "Confirmed"}
        response = self.service.update_application(application.data.id, updated_data)
        print(response)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

    def test_update_application_missing_id(self):
        updated_data = {"status": "Confirmed"}
        response = self.service.update_application(
            application_id=None, data=updated_data
        )
        self.assertFalse(response.success)
        self.assertIsNone(response.data)

    def test_update_application_empty_data(self):
        application = self.service.create_application(
            job_slug=self.job.slug, user_id=self.user.id
        )
        updated_data = {"status": "Confirmed"}
        response = self.service.update_application(
            application_id=application.data.id, data={}
        )
        self.assertFalse(response.success)
        self.assertIsNone(response.data)

    def test_update_application_wrong_id(self):
        updated_data = {"status": "Confirmed"}
        response = self.service.update_application(
            application_id=999, data=updated_data
        )
        self.assertFalse(response.success)
        self.assertIsNone(response.data)
