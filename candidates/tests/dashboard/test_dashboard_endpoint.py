import datetime
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from core.settings import SECRET_KEY
import jwt
from rest_framework import status
from candidates.models import Candidate, Application, Chat, Interview
from businesses.models import Company
from jobs.models import Job
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import AccessToken


User = get_user_model()
client = APIClient()
client2 = APIClient()


class TestDashboardDataEndpoint(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="password123"
        )
        self.user2 = User.objects.create_user(
            username="testuser2", email="<EMAIL>", password="password123"
        )
        token = jwt.encode(
            {"user_id": self.user.id}, SECRET_KEY, algorithm="HS256"
        )
        client.force_authenticate(user=self.user)
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {token}")
        client2.force_authenticate()
        self.url = "/api/candidates/dashboard/"

        self.candidate = Candidate.objects.create(user=self.user, created_by=self.user)
        self.company = Company.objects.create(name="Test Company", email="<EMAIL>")
        self.job = Job.objects.create(name="Test Job", company_name=self.company, min_salary=100, max_salary=300)
        self.job2 = Job.objects.create(name="Test Job2", company_name=self.company, min_salary=200, max_salary=400)
        self.job3 = Job.objects.create(name="Test Job3", company_name=self.company, min_salary=300, max_salary=500)
        self.job4 = Job.objects.create(name="Test Job4", company_name=self.company, min_salary=400, max_salary=600)
        self.application = Application.objects.create(
            applicant=self.candidate, job_applied=self.job, status="Upcoming"
        )
        self.application2 = Application.objects.create(
            applicant=self.candidate, job_applied=self.job2, status="Rejected"
        )
        self.application3 = Application.objects.create(
            applicant=self.candidate, job_applied=self.job3, status="Pending"
        )
        self.application4 = Application.objects.create(
            applicant=self.candidate, job_applied=self.job4, status="Pending"
        )
        self.interview = Interview.objects.create(
            user=self.user,
            business=self.company,
            job_applied=self.job,
            interview_date=datetime.datetime.today(),
            location="Online"
        )
        self.chat = Chat.objects.create(
            sender=self.user2,
            receiver=self.user,
            message="Test chat message"
        )

    def test_dashboard_data_success(self):
        response = client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertIn("stats", data)
        self.assertEqual(data["stats"]["sent_applications"], 4)
        self.assertEqual(data["stats"]["upcoming_interviews"], 1)
        self.assertEqual(data["stats"]["unread_messages"], 1)
        self.assertEqual(data["stats"]["rejected_applications"], 1)

    def test_dashboard_data_unauthorized(self):
        client.logout()
        response = client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_dashboard_data_no_token(self):
        response = client2.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
