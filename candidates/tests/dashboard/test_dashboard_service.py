import datetime
from candidates.models import Candidate, Application, Chat, Interview
from businesses.models import Company
from jobs.models import Job
from django.test import TestCase
from candidates.services.dashboard import DashboardService
from django.contrib.auth import get_user_model


User = get_user_model()


class TestDashboardService(TestCase):
    def setUp(self):
        self.service = DashboardService()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="password123"
        )
        self.user2 = User.objects.create_user(
            username="testuser2", email="<EMAIL>", password="password123"
        )
        self.candidate = Candidate.objects.create(user=self.user)
        self.company = Company.objects.create(name="Test Company", email="<EMAIL>")
        self.job = Job.objects.create(name="Test Job", company_name=self.company, min_salary=100, max_salary=300)
        self.job2 = Job.objects.create(name="Test Job2", company_name=self.company, min_salary=200, max_salary=400)
        self.application = Application.objects.create(
            applicant=self.candidate, job_applied=self.job, status="Upcoming"
        )
        self.application2 = Application.objects.create(
            applicant=self.candidate, job_applied=self.job2, status="Rejected"
        )
        self.interview = Interview.objects.create(
            user=self.user,
            business=self.company,
            job_applied=self.job,
            interview_date=datetime.datetime.today(),
            location="Online"
        )
        self.chat = Chat.objects.create(
            sender=self.user2,
            receiver=self.user,
            message="Test chat message"
        )
    
    def test_get_candidate_dashboard_data(self):
        response = self.service.get_dashboard_data(self.user)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data["stats"]["sent_applications"], 2)
        self.assertEqual(response.data["stats"]["upcoming_interviews"], 1)
        self.assertEqual(response.data["stats"]["unread_messages"], 1)
        self.assertEqual(response.data["stats"]["rejected_applications"], 1)
    
    def test_get_candidate_dashboard_data_fail(self):
        response = self.service.get_dashboard_data(self.user2)
        self.assertFalse(response.success)
        self.assertIsNone(response.data)
