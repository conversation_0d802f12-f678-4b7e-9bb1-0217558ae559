# Generated by Django 5.1.4 on 2025-01-08 13:47

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('base', '0006_remove_subscription_user_subscription_email'),
        ('candidates', '0006_remove_candidate_location'),
    ]

    operations = [
        migrations.AddField(
            model_name='candidate',
            name='location',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='user_address', to='base.address'),
        ),
    ]
