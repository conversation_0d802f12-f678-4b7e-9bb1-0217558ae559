# Generated by Django 4.2.11 on 2024-04-15 17:28

import candidates.models
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('jobs', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('businesses', '0001_initial'),
        ('base', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AcademicEducation',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('degree_attained', models.CharField(blank=True, max_length=255, null=True)),
                ('started_year', models.IntegerField()),
                ('ended_year', models.IntegerField()),
            ],
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='WorkExperience',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('employment_title', models.CharField(blank=True, max_length=255, null=True)),
                ('company_name', models.CharField(blank=True, max_length=255, null=True)),
                ('started_from', models.DateTimeField(null=True)),
                ('ended_at', models.DateTimeField(null=True)),
            ],
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='Interview',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('interview_date', models.DateTimeField()),
                ('from_time', models.TimeField(null=True)),
                ('to_time', models.TimeField(null=True)),
                ('location', models.CharField(choices=[('Online', 'Online'), ('In person', 'In person')], default='On site', max_length=255)),
                ('status', models.CharField(choices=[('Pending', 'Upcoming'), ('Due', 'Due'), ('Closed', 'Closed')], default='Upcoming', max_length=255)),
                ('message', models.TextField(max_length=500, null=True)),
                ('business', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='businesses.company')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='Chat',
            fields=[
                ('message_id', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False, unique=True)),
                ('message', models.CharField(max_length=250)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True)),
                ('receiver', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='receiver', to=settings.AUTH_USER_MODEL)),
                ('sender', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Candidate',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('profile_picture', models.ImageField(blank=True, null=True, upload_to=candidates.models.profile_image_path)),
                ('resume', models.FileField(blank=True, upload_to=candidates.models.cv_file_path)),
                ('is_employed', models.BooleanField(default=False)),
                ('linkedin_url', models.URLField(blank=True, null=True)),
                ('portfolio_url', models.URLField(blank=True, null=True)),
                ('gender', models.CharField(blank=True, choices=[('Male', 'Male'), ('Female', 'Female'), ('Female', 'Female')], default='Female', max_length=500, null=True)),
                ('date_of_birth', models.DateTimeField(blank=True, null=True)),
                ('bio', models.CharField(blank=True, max_length=500, null=True)),
                ('started_working', models.DateTimeField(null=True)),
                ('academic_education', models.ManyToManyField(blank=True, null=True, to='candidates.academiceducation')),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='base.city')),
                ('skills', models.ManyToManyField(blank=True, to='base.skill')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('work_experience', models.ManyToManyField(blank=True, null=True, to='candidates.workexperience')),
            ],
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='Application',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('status', models.CharField(choices=[('Pending', 'Pending'), ('Shortlisted', 'Shortlisted'), ('Interview', 'Interview'), ('Hired', 'Hired'), ('Rejected', 'Rejected')], default='Pending', max_length=255)),
                ('applicant', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='candidates.candidate')),
                ('job_applied', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='jobs.job')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            bases=('base.basemodel',),
        ),
    ]
