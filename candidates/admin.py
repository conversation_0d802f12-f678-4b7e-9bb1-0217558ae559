from django.contrib import admin
from .models import *
from base.admin import BaseModelAdminMixin


# Register your models here.


@admin.register(Candidate)
class AdminCandidate(admin.ModelAdmin):
    list_display = ("user", "is_employed", "date_updated", "date_created")


@admin.register(AcademicEducation)
class AcademicEducationAdmin(admin.ModelAdmin):
    list_display = ("degree_attained", "started_year", "ended_year")


@admin.register(Interview)
class InterviewAdmin(admin.ModelAdmin):
    list_display = (
        "user",
        "business",
        "interview_date",
        "location",
        "status",
    )


@admin.register(Application)
class ApplicationAdmin(admin.ModelAdmin):
    list_display = ("applicant", "status")


@admin.register(Chat)
class ChatAdmin(admin.ModelAdmin):
    list_display = ("message_id", "sender", "receiver")


class WorkExperienceAdmin(BaseModelAdminMixin):
    pass


admin.site.register(WorkExperience, WorkExperienceAdmin)
