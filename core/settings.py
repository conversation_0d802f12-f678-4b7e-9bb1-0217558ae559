from datetime import timed<PERSON><PERSON>
from pathlib import Path
from tkinter import ALL
from dotenv import load_dotenv
import os

load_dotenv()

ENVIRONMENT = os.environ.get("ENVIRONMENT")
# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv("SECRET_KEY")

# Domain name configurations
if ENVIRONMENT == "production":
    DOMAIN_NAME = os.getenv("LIVE_DOMAIN_NAME")
else:
    DOMAIN_NAME = os.getenv("DEV_DOMAIN_NAME")

MAIN_EMAIL = os.getenv("MAIN_EMAIL")
# SECURITY WARNING: don't run with debug turned on in production!

DEBUG = True

ALLOWED_HOSTS = [os.getenv("ALLOWED_HOSTS")]


# Application definition

INSTALLED_APPS = [
    "api",
    "jobs",
    "base",
    "accounts",
    "candidates",
    "businesses",
    "jazzmin",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
    "rest_framework_simplejwt",
    "corsheaders",
    "ckeditor",
    "storages",
    "postmarker",
]

# django-ratelimit settings
RATELIMIT_ENABLE = True
RATELIMIT_CACHE_PREFIX = "ratelimit_"
RATELIMIT_VIEW = "base.views.rate_limit.rate_limit_view"

# Redis cache configuration
if ENVIRONMENT == "production":
    CACHES = {
        "default": {
            "BACKEND": "django_redis.cache.RedisCache",
            "LOCATION": "redis://redis-11391.c341.af-south-1-1.ec2.redns.redis-cloud.com:11391/0",
            "OPTIONS": {
                "CLIENT_CLASS": "django_redis.client.DefaultClient",
                "PASSWORD": os.getenv("REDIS_PASSWORD"),
                "SSL": True,
            },
        }
    }
else:
    CACHES = {
        "default": {
            "BACKEND": "django_redis.cache.RedisCache",
            "LOCATION": "redis://127.0.0.1:6379/0",
            "OPTIONS": {
                "CLIENT_CLASS": "django_redis.client.DefaultClient",
            },
        }
    }

REST_FRAMEWORK = {
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.AllowAny",
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework.authentication.SessionAuthentication",
        "rest_framework.authentication.BasicAuthentication",
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ],
    "DEFAULT_CACHE_RESPONSE_TIMEOUT": 60 * 60 * 24,
    "DEFAULT_THROTTLE_CLASSES": [
        "rest_framework.throttling.AnonRateThrottle",
        "rest_framework.throttling.UserRateThrottle",
    ],
    "DEFAULT_THROTTLE_RATES": {
        "anon": "100/hour" if ENVIRONMENT == "production" else "1000/hour",
        "user": "500/hour" if ENVIRONMENT == "production" else "5000/hour",
    },
}

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=90),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=90),
    # ... other JWT settings (optional)
}

REST_FRAME_SIMPLEJWT = {
    "AUTH_HEADER_NAME": "HTTP_AUTHORIZATION",
    "AUTH_TOKEN_CLASSES": ("rest_framework_simplejwt.tokens.RefreshToken",),
}

# settings.py

CKEDITOR_CONFIGS = {
    "default": {
        "removePlugins": "image, imageCaption, imageStyle, imageToolbar, imageUpload, mediaEmbed, codeBlock, Source"
    }
}


MIDDLEWARE = [
    "base.middleware.admin_skip_ratelimit.AdminSkipRateLimitMiddleware",  # Admin skip logic first
    "base.middleware.global_rate_limit.GlobalRateLimitMiddleware",  # Global rate limit after admin skip
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    # "base.views.MediaAuthenticationMiddleware",
]

EMAIL_SERVER_TOKEN = os.getenv("EMAIL_SERVER_TOKEN")

CORS_ALLOWED_ORIGINS = [
    "https://jobmatch.rw",
    "https://www.jobmatch.rw",
    "https://app.jobmatch.rw",
    "http://localhost:3000",
    "http://127.0.0.1:8000",
    "https://csrlimited.com",
    "https://cbo-api.csrlimited.com",
    "https://beta.app.jobmatch.rw",
]

CORS_ALLOWED_ORIGIN_REGEXES = [
    r"^https://jobmatch\.rw$",
    r"^https://www\.jobmatch\.rw$",
    r"^https://app\.jobmatch\.rw$",
    r"^http://localhost:3000$",
    r"^http://127\.0\.0\.1:8000$",
    r"^https://csrlimited\.com$",
    r"^https://cbo-api\.csrlimited\.com$",
    r"^https://beta\.app\.jobmatch\.rw$",
]

CORS_ALLOW_METHODS = (
    "DELETE",
    "GET",
    "OPTIONS",
    "PATCH",
    "POST",
    "PUT",
)
CORS_ALLOW_HEADERS = (
    "accept",
    "authorization",
    "content-type",
    "user-agent",
    "x-csrftoken",
    "x-requested-with",
)


ROOT_URLCONF = "core.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "core.wsgi.application"


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

if ENVIRONMENT == "production":
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.postgresql",
            "NAME": os.getenv("DB_NAME"),
            "USER": os.getenv("DB_USERNAME"),
            "PASSWORD": os.getenv("DB_PASSWORD"),
            "HOST": os.getenv("DB_HOST"),
            "PORT": os.getenv("DB_PORT"),
        }
    }
else:
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.sqlite3",
            "NAME": BASE_DIR / "db.sqlite3",
        }
    }


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "Africa/Kigali"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/


DEFAULT_FILE_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"

AWS_ACCESS_KEY_ID = os.getenv("BUCKET_ACCESS_KEY")
AWS_SECRET_ACCESS_KEY = os.getenv("BUCKET_SECRET_KEY")
AWS_STORAGE_BUCKET_NAME = "csr-staffing"
AWS_S3_ENDPOINT_URL = os.getenv("BUCKET_URL")
AWS_DEFAULT_ACL = None  # Prevent public access
AWS_AUTO_CREATE_BUCKET = True  # Automatically create bucket if needed


STATIC_URL = "/static/"
MEDIA_URL = "/media/"
MEDIA_ROOT = BASE_DIR / "media"
STATIC_ROOT = BASE_DIR / "staticfiles"
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, "static"),
]
STATICFILES_STORAGE = "whitenoise.storage.CompressedManifestStaticFilesStorage"
# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

POSTMARK_API_TOKEN = os.getenv("EMAIL_SERVER_TOKEN")
DEFAULT_FROM_EMAIL = os.getenv("MAIN_EMAIL")
