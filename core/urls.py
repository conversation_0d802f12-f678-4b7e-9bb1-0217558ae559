from django.contrib import admin
from django.urls import path, include
from . import settings
from django.conf.urls.static import static

admin.site.site_header = "talentConnect"
admin.site.site_title = "talentConnect"
urlpatterns = [
    path("admin/", admin.site.urls),
    path("api-auth/", include("rest_framework.urls")),
    path("api/accounts/", include("api.urls.accounts")),
    path("api/jobs/", include("api.urls.jobs")),
    path("api/business/", include("api.urls.business")),
    path("api/candidates/", include("api.urls.candidates")),
    path("api/dashboard/", include("api.urls.dashboard")),
    path("api/base/", include("api.urls.base")),
    # back office
    path("api/back-office/jobs/", include("api.urls.back_office.urls")),
    # admin businesses
    path("api/admin/businesses/", include("api.urls.admin.businesses")),
    # admin candidates
    path("api/admin/candidates/", include("api.urls.admin.candidates")),
    # admin jobs
    path("api/admin/jobs/", include("api.urls.admin.jobs")),
    # admin subscriptions
    path("api/admin/subscriptions/", include("api.urls.admin.subscriptions")),
    # admin invoices
    path("api/admin/invoices/", include("api.urls.admin.invoices")),
    # admin packages
    path("api/admin/packages/", include("api.urls.admin.packages")),
]

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
