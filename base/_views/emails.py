import requests

from accounts.views.email_templates.welcome_email import welcome_email_content
from core.settings import EMAIL_SERVER_TOKEN, MAIN_EMAIL

url = "https://api.postmarkapp.com/email"
headers = {
    "X-Postmark-Server-Token": EMAIL_SERVER_TOKEN,
    "Content-Type": "application/json",
}


def send_email(recipient_email, data, html_body, subject):
    body = {
        "From": MAIN_EMAIL,
        "To": recipient_email,
        "Subject": subject,
        "TemplateModel": data,
        "HtmlBody": html_body,
    }

    response = requests.post(url, headers=headers, json=body)

    if response.status_code == 200:
        print("Email is send")

    else:
        print(f"Error sending email: {response.status_code}")
        print(response.text)


def send_email_with_attachments(recipient_email, data, html_body, subject, attachments):
    body = {
        "From": MAIN_EMAIL,
        "To": recipient_email,
        "Subject": subject,
        "TemplateModel": data,
        "HtmlBody": html_body,
        "Attachments": attachments,
    }

    response = requests.post(url, headers=headers, json=body)

    if response.status_code == 200:
        print("Email with attachments is sent")

    else:
        print(f"Error sending email with attachments: {response.status_code}")
        print(response.text)
