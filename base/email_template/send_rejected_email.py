from base._views.emails import send_email

rejected_email_content = """
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<title>Application Update: Thank You for Your Interest</title>
</head>
<body style="font-family: Arial, sans-serif;">

<table cellpadding="0" cellspacing="0" border="0" width="100%">
<tr>
    <td style="padding: 20px;">
        <p>Dear {first_name},</p>
        <p>Thank you for applying for the position of <strong>{job_name}</strong> at <strong>{company_name}</strong>.</p>
        <p>After careful consideration, we regret to inform you that your application has not been successful this time. We truly appreciate the time and effort you put into applying and encourage you to explore other opportunities with us in the future.</p>
        <p style="margin: 0;">If you have any questions or need further assistance, please contact our team at <a href="mailto: <EMAIL>"> <EMAIL></a>.</p>
        <p>Wishing you success in your job search.<br/> Best regards,<br/> CSR jobMatch team</p>
    </td>
</tr>
</table>

</body>
</html>
"""

def send_rejected_email(recipient_email, first_name, job_name, company_name):
    subject = "Application Update: Thank You for Your Interest"
    html_body = rejected_email_content.format(
        first_name=first_name,
        job_name=job_name,
        company_name=company_name,
    )
    data = {
        "first_name_Value": first_name,
        "job_name_Value": job_name,
        "company_name_Value": company_name,
        "email_Value": recipient_email,
    }
    send_email(
        recipient_email=recipient_email, data=data, html_body=html_body, subject=subject
    )
