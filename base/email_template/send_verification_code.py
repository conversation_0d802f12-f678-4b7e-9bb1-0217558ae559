from base._views.emails import send_email

send_verification_code_email_content = """" 
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Email Verification</title>
</head>
<body style="font-family: Arial, sans-serif;">
  <table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
      <td style="padding: 20px;">
        <p>Hi {first_name},</p>
        <p>To verify your email, please use the following code:</p>
        <p style="font-size: 20px; font-weight: bold;">{verification_code}</p>
        <p>This code will expire in 1 minute.</p>
        <p style="margin: 0;">If you have any questions or need further assistance, please contact our team at <a href="mailto: <EMAIL>"> <EMAIL></a>.</p>
        <p>Best regards,<br/>CSR jobMatch team</p>
      </td>
    </tr>
  </table>
</body>
</html>
"""

def send_verification_code_email(recipient_email, first_name, verification_code):
    subject = "Email Verification"
    html_body = send_verification_code_email_content.format(
        first_name=first_name,
        verification_code=verification_code,
    )
    data = {
        "first_name_Value": first_name,
        "verification_code_Value": verification_code,
        "email_Value": recipient_email,
    }
    send_email(
        recipient_email=recipient_email, data=data, html_body=html_body, subject=subject
    )