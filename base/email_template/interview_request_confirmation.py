from base._views.emails import send_email

interview_request_confirmation_email_content = """
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<title>Interview Request Successfully Sent</title>
</head>
<body style="font-family: Arial, sans-serif;">

<table cellpadding="0" cellspacing="0" border="0" width="100%">
<tr>
    <td style="padding: 20px;">
        <p>Dear {company_name},</p>
        <p>Thank you for requesting an interview for the position of <strong>{job_name}</strong> with <strong>{candidate_name}</strong>.</p>
        <p>Your request has been successfully sent and the candidate has been notified.</p>
        <p style="margin: 0;">If you have any questions or need further assistance, please contact our team at <a href="mailto: <EMAIL>"> <EMAIL></a>.</p>
        <p>We appreciate your continued trust in CSR jobMatch.</p>
        <p>Best regards,<br/> CSR jobMatch team</p>
    </td>
</tr>
</table>

</body>
</html>
"""

def send_interview_request_confirmation(recipient_email, company_name, job_name, candidate_name):
    subject = "Interview Request Confirmed Successfully"
    html_body = interview_request_confirmation_email_content.format(
        company_name=company_name,
        job_name=job_name,
        candidate_name=candidate_name,
    )
    data = {
        "company_name_Value": company_name,
        "job_name_Value": job_name,
        "candidate_name_Value": candidate_name,
        "email_Value": recipient_email,
    }
    send_email(
        recipient_email=recipient_email, data=data, html_body=html_body, subject=subject
    )
