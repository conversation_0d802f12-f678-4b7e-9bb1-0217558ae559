from base._views.emails import send_email

profile_completion_email_content = """
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<title>Complete Your Profile</title>
</head>
<body style="font-family: Arial, sans-serif;">

<table cellpadding="0" cellspacing="0" border="0" width="100%">
<tr>
    <td style="padding: 20px;">
        <p>Dear {first_name},</p>
        <p>We noticed that your profile on CSR jobMatch is not yet complete. A fully completed profile helps you stand out to employers and increases your chances of finding the perfect job!</p>
        <p>Please take a moment to finish setting up your profile by clicking the link below:</p>
        <p><a href="{profile_link}" style="color: #007bff; text-decoration: none;">Complete Your Profile Now</a></p>
        <p style="margin: 0;">If you have any questions or need further assistance, please contact our team at <a href="mailto: <EMAIL>"> <EMAIL></a>.</p>
        <p>Best regards,<br/> CSR jobMatch team</p>
    </td>
</tr>
</table>

</body>
</html>
"""

def send_profile_completion_email(recipient_email, first_name, profile_link):
    subject = "Please Complete Your CSR jobMatch Profile"
    html_body = profile_completion_email_content.format(
        first_name=first_name,
        profile_link=profile_link,
    )
    data = {
        "first_name_Value": first_name,
        "profile_link_Value": profile_link,
        "email_Value": recipient_email,
    }
    send_email(
        recipient_email=recipient_email, data=data, html_body=html_body, subject=subject
    )
