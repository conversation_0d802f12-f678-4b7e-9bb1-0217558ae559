from base._views.emails import send_email

shortlisted_email_content = """
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<title>Exciting News: Your Application Has Been Shortlisted!</title>
</head>
<body style="font-family: Arial, sans-serif;">

<table cellpadding="0" cellspacing="0" border="0" width="100%">
<tr>
    <td style="padding: 20px;">
        <p>Dear {first_name},</p>
        <p>We are thrilled to inform you that your application for the position of <strong>{job_name}</strong> at <strong>{company_name}</strong> has been shortlisted!</p>
        <p>Please log in to your account for further updates.</p>
        <p style="margin: 0;">If you have any questions or need further assistance, please contact our team at <a href="mailto: <EMAIL>"> <EMAIL></a>.</p>
        <p>Best regards,<br/> CSR jobMatch team</p>
    </td>
</tr>
</table>

</body>
</html>
"""

def send_shortlisted_email(recipient_email, first_name, job_name, company_name):
    subject = "Congratulations! Your Application Has Been Shortlisted"
    html_body = shortlisted_email_content.format(
        first_name=first_name,
        job_name=job_name,
        company_name=company_name,
    )
    data = {
        "first_name_Value": first_name,
        "job_name_Value": job_name,
        "company_name_Value": company_name,
        "email_Value": recipient_email,
    }
    send_email(
        recipient_email=recipient_email, data=data, html_body=html_body, subject=subject
    )
