from base._views.emails import send_email


send_reset_password_code_email_content = """
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Password Reset Verification Code</title>
</head>
<body style="font-family: Arial, sans-serif; background-color: #f9f9f9; color: #333333; margin: 0; padding: 0;">
  <div style="max-width: 600px; margin: 30px auto; background-color: #ffffff; border: 1px solid #dddddd; padding: 20px;">
    <p style="margin: 0 0 10px 0;">Dear {first_name},</p>
    <p style="margin: 0 0 10px 0;">
      You recently requested to reset your password. Please use the verification code below to proceed:
    </p>
    <p style="font-size: 24px; font-weight: bold; color: #007bff; margin: 20px 0;">
      {verification_code}
    </p>
    <p style="margin: 0 0 10px 0;">
      Please note that this code is valid for only 1 minute. If you did not initiate this request, please disregard this email or contact our support team immediately.
    </p>
    <p style="margin: 0 0 10px 0;">Thank you for your prompt attention to this matter.</p>
    <p style="margin: 0 0 10px 0;">Sincerely,<br/>The CSR jobMatch Team</p>
    <div style="margin-top: 30px; font-size: 12px; color: #777777;">
      <p style="margin: 0;">If you have any questions or need further assistance, please contact our team at <a href="mailto: <EMAIL>"> <EMAIL></a>.</p>
    </div>
  </div>
</body>
</html>
"""


def send_reset_password_code_email(recipient_email, first_name, verification_code):
    subject = "Reset Password Verification Code"
    html_body = send_reset_password_code_email_content.format(
        first_name=first_name,
        verification_code=verification_code,
    )
    data = {
        "first_name_Value": first_name,
        "verification_code_Value": verification_code,
        "email_Value": recipient_email,
    }
    send_email(
        recipient_email=recipient_email, data=data, html_body=html_body, subject=subject
    )



