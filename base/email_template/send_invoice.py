from base._views.emails import send_email_with_attachments
import base64


invoice_email = """
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<title>Invoice from CSR jobMatch</title>
</head>
<body style="font-family: Arial, sans-serif;">

<table cellpadding="0" cellspacing="0" border="0" width="100%">
<tr>
    <td style="padding: 20px;">
        <p>Dear {first_name},</p>
        <p>Thank you for your business with CSR jobMatch. Attached to this email, you will find your invoice for the services provided.</p>
        <p>Please review the attached PDF invoice and make the payment by the due date specified. You can find payment instructions within the invoice.</p>
        <p>If you have any questions or need further assistance, please contact our billing team at <a href="mailto:<EMAIL>" style="color: #007bff; text-decoration: none;"><EMAIL></a>.</p>
        <p>Best regards,<br/>CSR jobMatch Billing Team</p>
    </td>
</tr>
</table>

</body>
</html>
"""


def send_invoice_email(recipient_email, first_name, invoice_file, invoice_number):
    subject = "Your Invoice from CSR jobMatch"
    html_body = invoice_email.format(
        first_name=first_name,
    )
    data = {
        "first_name_Value": first_name,
        "email_Value": recipient_email,
    }
    # Convert BytesIO to base64 string
    if hasattr(invoice_file, "getvalue"):
        pdf_bytes = invoice_file.getvalue()
    else:
        pdf_bytes = invoice_file
    pdf_base64 = base64.b64encode(pdf_bytes).decode("utf-8")
    send_email_with_attachments(
        recipient_email=recipient_email,
        data=data,
        html_body=html_body,
        subject=subject,
        attachments=[
            {
                "Name": f"{invoice_number}.pdf",
                "Content": pdf_base64,
                "ContentType": "application/pdf",
            }
        ],
    )
