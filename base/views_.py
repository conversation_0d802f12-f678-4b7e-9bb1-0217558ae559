from core import settings
from django.http import HttpResponseForbidden

# locally protect media folder
class MediaAuthenticationMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if request.path.startswith(settings.MEDIA_URL):
            if not request.user.is_authenticated:
                return HttpResponseForbidden('Unauthorized access to media files')
        return self.get_response(request)
