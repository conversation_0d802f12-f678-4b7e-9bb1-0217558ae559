import string
import secrets
from accounts.models import PasswordResetCode

def generate_verification_code(user) -> str:
    """
    Generate a 6-character verification code, store it in the database for the given user, 
    and return the code.
    """
    characters = string.ascii_letters + string.digits
    code = "".join(secrets.choice(characters) for _ in range(6))
    
    PasswordResetCode.objects.create(
        user=user,
        verification_code=code,
    )
    return code
