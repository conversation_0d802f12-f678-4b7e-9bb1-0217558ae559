from dataclasses import dataclass
from typing import Union, Optional, Dict, List, Any
from django.db.models import Model
from rest_framework import status
from base.services.logging import LoggingService
from businesses.views.serializers import BusinessSerializer
from candidates.models import Candidate
from candidates.views.serializers import CandidateDataSerializer


logging_service = LoggingService()


@dataclass
class APIResponse:
    success: bool
    message: str
    data: Optional[Union[Candidate, Dict, List, Model]] = None
    status: Optional[Any] = 400


class StatusChecker:
    @staticmethod
    def get_status(identifier, repository, fields, profile_type):
        """
        Generic method to check the status of a profile.
        """
        try:
            # print(f"repository in get_status: {repository}")
            resource = repository(identifier)
            if not resource.success:
                return APIResponse(
                    success=False,
                    message=resource.message,
                    data=None,
                    status=status.HTTP_404_NOT_FOUND,
                )

            if profile_type == "business":
                data = BusinessSerializer(resource.data).data
            elif profile_type == "candidate":
                data = CandidateDataSerializer(resource.data).data

            def has_value(value):
                if (
                    value is None
                    or (isinstance(value, (list, dict)) and not value)
                    or (isinstance(value, str) and not value.strip())
                ):
                    return False
                return True

            for step, required_fields in fields.items():
                missing_fields = [
                    field for field in required_fields if not has_value(data.get(field))
                ]
                if missing_fields:
                    return APIResponse(
                        success=True,
                        message=f"Incomplete profile. Please fill all required fields in step {step}.",
                        data={"complete": False, "current_step": step},
                        status=status.HTTP_200_OK,
                    )

            return APIResponse(
                success=True,
                message="Profile status retrieved successfully.",
                data={"complete": True, "current_step": len(fields)},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to retrieve profile status.",
                data=None,
                status=status.HTTP_400_BAD_REQUEST,
            )
