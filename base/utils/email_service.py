from postmarker.core import PostmarkClient
from django.conf import settings
from base.services.logging import LoggingService
from candidates.services.application import RepositoryResponse


logging_service = LoggingService()


class EmailService:
    def __init__(self):
        self.client = PostmarkClient(server_token=settings.POSTMARK_API_TOKEN)

    def send_email(self, to, subject, text_body, html_body):
        try:
            response = self.client.emails.send(
                From=settings.DEFAULT_FROM_EMAIL,
                To=to,
                Subject=subject,
                TextBody=text_body,
                HtmlBody=html_body
            )
            return RepositoryResponse(
                success=True,
                data=None,
                message=f"Email sent to {to}"
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to send email to {to}"
            )