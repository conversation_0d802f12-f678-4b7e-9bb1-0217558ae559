from datetime import datetime
from zoneinfo import ZoneInfo
from candidates.repositories.application_repository import RepositoryResponse

def convert_date_string(date_string: str, field_name: str) -> RepositoryResponse:
   """
   Convert a date string to a UTC naive datetime.
   """
   try:
       date_obj = datetime.strptime(date_string, "%Y-%m-%d")
       date_aware = datetime.combine(
           date_obj.date(),
           datetime(1, 1, 1, 23, 0).time(),
           tzinfo=ZoneInfo("Africa/Kigali")
       )
       converted_date = date_aware.astimezone(ZoneInfo("UTC")).replace(tzinfo=None)
       
       return RepositoryResponse(
           success=True,
           data=converted_date,
           message="Date converted successfully"
       )

   except ValueError:
       return RepositoryResponse(
           success=False,
           data=None,
           message=f"Invalid {field_name} format. Use YYYY-MM-DD format."
       )
