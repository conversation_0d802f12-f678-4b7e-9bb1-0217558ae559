from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny


@api_view(["GET"])
@permission_classes([AllowAny])
def rate_limit_view(request):
    """
    Endpoint to return rate limit information.
    """
    return Response(
        {
            "error": "Too many requests",
        },
        status=status.HTTP_429_TOO_MANY_REQUESTS,
    )
