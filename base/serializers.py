from rest_framework import serializers
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import datetime, date
from accounts.serializers import UserSerializer
from .models import *


from django.utils import timezone
from rest_framework import serializers
from datetime import datetime


class HumanReadableDateField(serializers.DateTimeField):
    def to_representation(self, value):
        if not value:
            return None

        if isinstance(value, str):
            try:
                value = datetime.strptime(value, "%Y-%m-%d")
            except ValueError:
                return value

        today = timezone.localtime().date()

        value_date = value.date() if isinstance(value, datetime) else value

        return value_date.strftime("%Y-%m-%d")


class HumanReadableTimeField(serializers.TimeField):
    def to_representation(self, value):
        if not value:
            return None
        return value.strftime("%I:%M %p")


class GetLocationSerializer(serializers.ModelSerializer):
    full_location = serializers.SerializerMethodField()

    class Meta:
        model = Address
        fields = ["id", "full_location"]

    def get_full_location(self, obj):
        return f"{obj.city_name.name}, {obj.street_address}"


class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = "__all__"


class GetCategorySerializer(serializers.ModelSerializer):
    created_by = UserSerializer()

    class Meta:
        model = Category
        fields = "__all__"


class SkillSerializer(serializers.ModelSerializer):
    class Meta:
        model = Skill
        fields = "__all__"


class GetSkillSerializer(serializers.ModelSerializer):
    created_by = UserSerializer()
    category_name = GetCategorySerializer()

    class Meta:
        model = Skill
        fields = "__all__"


class CountrySerializer(serializers.ModelSerializer):
    class Meta:
        model = Country
        fields = "__all__"


class GetCountrySerializer(serializers.ModelSerializer):
    created_by = UserSerializer()

    class Meta:
        model = Country
        fields = "__all__"


class StateSerializer(serializers.ModelSerializer):
    class Meta:
        model = State
        fields = "__all__"


class GetStateSerializer(serializers.ModelSerializer):
    created_by = UserSerializer()
    country_name = GetCountrySerializer()

    class Meta:
        model = State
        fields = "__all__"


class CitySerializer(serializers.ModelSerializer):
    class Meta:
        model = City
        fields = "__all__"


class GetCitySerializer(serializers.ModelSerializer):
    created_by = UserSerializer()
    state_name = GetStateSerializer()

    class Meta:
        model = City
        fields = "__all__"


class AddressSerializer(serializers.ModelSerializer):
    class Meta:
        model = Address
        fields = "__all__"


class GetAddressSerializer(serializers.ModelSerializer):
    created_by = UserSerializer()
    city_name = GetCitySerializer()

    class Meta:
        model = Address
        fields = "__all__"


class SocialMediaPlatformsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SocialMediaPlatforms
        fields = "__all__"


class GetSocialMediaPlatformsSerializer(serializers.ModelSerializer):
    created_by = UserSerializer()
    user = UserSerializer()

    class Meta:
        model = SocialMediaPlatforms
        fields = "__all__"


class UserLanguageSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserLanguage
        fields = "__all__"


class GetUserLanguageSerializer(serializers.ModelSerializer):
    created_by = UserSerializer()

    class Meta:
        model = UserLanguage
        fields = "__all__"


class GetPackageSerializer(serializers.ModelSerializer):
    services = serializers.SerializerMethodField()

    class Meta:
        model = Package
        fields = ["id", "name", "services", "price"]

    def get_services(self, obj):
        return [
            {
                "id": service.id,
                "name": service.name,
                "description": service.description,
            }
            for service in obj.pac_services.all()
        ]
