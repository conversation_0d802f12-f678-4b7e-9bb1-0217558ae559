from base.models import Category, Skill
from base.services.logging import LoggingService
from candidates.repositories.application_repository import RepositoryResponse
from base.repositories.skill import SkillRepository
from typing import Dict, Any
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

logging_service = LoggingService()


class SkillService:
    def __init__(self, repository: SkillRepository = None):
        self.repository = repository or SkillRepository()

    def _validate_skill_data(self, data: Dict[str, Any]):
        required_fields = ["name"]
        missing_fields = logging_service.check_required_fields(data, required_fields)
        if missing_fields:
            return RepositoryResponse(
                success=False,
                data=None,
                message=missing_fields,
            )
        if len(data["name"]) > 255:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Name cannot exceed 255 characters.",
            )

        if data.get("description") and len(data["description"]) > 500:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Description cannot exceed 500 characters.",
            )

    def create_skill(self, data: Dict[str, Any]) -> RepositoryResponse:
        try:
            self._validate_skill_data(data)
            response = self.repository.create_skill(data)

            if not response.success:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message=response.message,
                )

            return RepositoryResponse(
                success=True,
                data={
                    "id": response.data.id,
                    "name": response.data.name,
                    "description": response.data.description,
                    "date_created": response.data.date_created,
                },
                message=_("Skill created successfully."),
            )
        except ValidationError as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Error creating skill",
            )

    def get_skill_by_id(self, id: str) -> RepositoryResponse:
        try:
            response = self.repository.get_skill_by_id(id)
            if not response.success:
                return RepositoryResponse(
                    success=False, data=None, message=response.message
                )

            return RepositoryResponse(
                success=True,
                data={
                    "id": response.data.id,
                    "name": response.data.name,
                    "description": response.data.description,
                    "date_created": response.data.date_created,
                },
                message=_("Skill retrieved successfully."),
            )
        except ValidationError as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False, data=None, message="Error while retrieving skill"
            )

    def get_skill_by_name(self, name: str) -> RepositoryResponse:
        try:
            response = self.repository.get_skill_by_name(name)
            if not response.success:
                return RepositoryResponse(
                    success=False, data=None, message=response.message
                )

            return RepositoryResponse(
                success=True,
                data=response.data,
                message=_("Skill retrieved successfully."),
            )
        except ValidationError as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False, data=None, message="Error while retrieving skill"
            )

    def get_or_create_skill(sef, name, category_name) -> RepositoryResponse:
        try:
            category, _ = Category.objects.get_or_create(name=category_name)
            skill, _ = Skill.objects.get_or_create(name=name, category_name=category)
            return RepositoryResponse(
                success=True,
                data=skill,
                message=("Skill retrieved or created successfully."),
            )
        except ValidationError as ev:
            logging_service.log_error(ev)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Validation error while getting or creating skill",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Unknown error while getting or creating skill",
            )

    def update_skill(self, id: str, data: Dict[str, Any]) -> RepositoryResponse:
        try:
            self._validate_skill_data(data)
            response = self.repository.update_skill(id, data)

            if not response.success:
                return RepositoryResponse(
                    success=False, data=None, message=response.message
                )

            return RepositoryResponse(
                success=True,
                data={
                    "id": response.data.id,
                    "name": response.data.name,
                    "description": response.data.description,
                    "date_created": response.data.date_created,
                },
                message=_("Skill updated successfully."),
            )
        except ValidationError as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Unknown error during updating skill",
            )

    def get_skill_by_category(self, category):
        try:
            response = self.repository.get_skill_by_category(category)
            if not response.success:
                return RepositoryResponse(
                    success=False, data=None, message=response.message
                )

            return RepositoryResponse(
                success=True,
                data=response.data,
                message=_("Skills retrieved successfully."),
            )
        except ValidationError as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Validation error during updating skill",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Unknown error during updating skill",
            )

    def delete_skill(self, id: str) -> RepositoryResponse:
        try:
            response = self.repository.delete_skill(id)
            if not response.success:
                return RepositoryResponse(
                    success=False, data=None, message=response.message
                )

            return RepositoryResponse(
                success=True, data=None, message=_("Skill deleted successfully.")
            )
        except ValidationError as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Validation error during deleting",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Unknown error during deleting skill",
            )
