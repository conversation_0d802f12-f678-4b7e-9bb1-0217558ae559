"""
This service provides caching functionality for the application.
It uses Redis in production and Default django cache in development.
"""

from django.core.cache import cache

from base.services.logging import LoggingService


class CachingService:
    def __init__(self):
        self.logging_service = LoggingService()

    def setCache(self, key, object, timeout=60 * 60 * 24):
        """
        Set an object in the cache with a specified key and timeout.
        """
        try:
            cache.set(key, object, timeout)
            self.logging_service.log_info(f"Cache set for key: {key}")
            return True
        except Exception as e:
            self.logging_service.log_error(e)
            return False

    def getCache(self, key, default=None):
        """
        Retrieve an object from the cache by key.
        """
        try:
            return cache.get(key, default)
        except Exception as e:
            print(f"Error getting cache: {e}")
            return default

    def deleteCache(self, key):
        """
        Delete a key from the cache.
        """
        try:
            cache.delete(key)
            return True
        except Exception as e:
            print(f"Error deleting cache: {e}")
            return False

    def hasCache(self, key):
        """
        Check if a key exists in the cache.
        """
        try:
            return key in cache
        except Exception as e:
            print(f"Error checking cache existence: {e}")
            return False

    def clearCache(self):
        """
        Clear the entire cache (use with caution).
        """
        try:
            cache.clear()
            return True
        except Exception as e:
            print(f"Error clearing cache: {e}")
            return False

    def updateCache(self, key, object, timeout=60 * 60 * 24):
        """
        Update an existing cache entry. Returns True if updated, False if key does not exist.
        """
        try:
            if self.hasCache(key):
                cache.set(key, object, timeout)
                return True
            else:
                print(f"Cache key '{key}' does not exist. Cannot update.")
                return False
        except Exception as e:
            print(f"Error updating cache: {e}")
            return False

    def addToCacheList(self, key, object, timeout=None):
        """
        Add an object to a list in the cache. If the key does not exist, it creates a new list.
        """
        try:
            cached_list = self.getCache(key, [])
            if not isinstance(cached_list, list):
                cached_list = []
            cached_list.append(object)
            self.setCache(key, cached_list, timeout)
            return True
        except Exception as e:
            print(f"Error adding to cache list: {e}")
            return False

    def removeFromCacheList(self, key, object):
        """
        Remove an object from a list in the cache.
        """
        try:
            cached_list = self.getCache(key, [])
            if isinstance(cached_list, list) and object in cached_list:
                cached_list.remove(object)
                self.setCache(key, cached_list)
                return True
            return False
        except Exception as e:
            print(f"Error removing from cache list: {e}")
            return False

    def updateCacheItem(self, key, item_id, updated_item, timeout=60 * 60 * 24):
        """
        Update a specific item in a cached list by removing the old one and adding the updated one.
        
        Args:
            key (str): The cache key for the list
            item_id: The identifier value to match
            updated_item (dict): The updated item to store
            timeout (int, optional): Cache timeout in seconds
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            cached_list = self.getCache(key, [])
            if not isinstance(cached_list, list):
                return False
            
            filtered_list = [item for item in cached_list if item.get('id') != item_id]
            
            filtered_list.append(updated_item)
            
            self.setCache(key, filtered_list, timeout)
            return True
        except Exception as e:
            print(f"Error updating cache item: {e}")
            return False
        
    def removeCacheItem(self, key, item_id, id_field='id'):
        """
        Remove a specific item from a cached list.
        
        Args:
            key (str): The cache key for the list
            item_id: The value to match against the id_field
            id_field (str): The field to use as identifier
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            cached_list = self.getCache(key, [])
            if not isinstance(cached_list, list):
                return False
            
            filtered_list = [item for item in cached_list if item.get(id_field) != item_id]
            
            if len(filtered_list) != len(cached_list):
                self.setCache(key, filtered_list)
                return True
            return False
        except Exception as e:
            print(f"Error removing cache item: {e}")
            return False
