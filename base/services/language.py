from typing import Dict, Any, List
from base.repositories.language import UserLanguageRepository
from candidates.repositories.application_repository import RepositoryResponse

class LanguageService:
    def __init__(self):
        self.repository = UserLanguageRepository()

    def create_user_language(self, data: Dict[str, Any]) -> RepositoryResponse:
        """
        Create a new user language.
        """
        if "name" not in data or not data["name"]:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Language name is required."
            )

        return self.repository.create_user_language(data)

    def get_user_language_by_id(self, id: str) -> RepositoryResponse:
        """
        Retrieve a user language by its ID.
        """
        return self.repository.get_user_language_by_id(id)

    def update_user_language(self, id: str, data: Dict[str, Any]) -> RepositoryResponse:
        """
        Update an existing user language by its ID.
        """
        if "name" in data and not data["name"]:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Language name cannot be empty."
            )

        return self.repository.update_user_language(id, data)

    def delete_user_language(self, id: str) -> RepositoryResponse:
        """
        Delete a user language by its ID.
        """
        return self.repository.delete_user_language(id)

    def bulk_update_user_languages(self, user, language_list: List[Dict[str, Any]]) -> RepositoryResponse:
        """
        Update multiple user languages associated with a user profile.
        """
        try:
            language_ids = []

            for language_data in language_list:
                if "id" in language_data:
                    response = self.update_user_language(language_data["id"], language_data)
                else:
                    language_data["created_by"] = user
                    response = self.create_user_language(language_data)

                if not response.success:
                    return RepositoryResponse(
                        success=False,
                        data=None,
                        message=f"Failed to process language: {response.message}"
                    )

                language_ids.append(response.data.id)

            return RepositoryResponse(
                success=True,
                data=language_ids,
                message="Languages updated successfully."
            )

        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to update languages: {str(e)}"
            )
