from base.repositories.interviews import InterviewRepository
from base.services.logging import LoggingService
from base.utils.status_checker import APIResponse
from rest_framework import status

from businesses.services.business import BusinessService
from jobs.models import Job

from candidates.models import Interview


repository = InterviewRepository()
logging_service = LoggingService()
company_service = BusinessService()


class InterviewService:

    def create_error_response(self, message, status_code):
        return APIResponse(
            success=False, data=None, message=message, status=status_code
        )

    def get_interviews_user(self, user_id) -> APIResponse:
        try:
            response = repository.get_interviews_by_user(user_id)
            if not response.success:
                return APIResponse(
                    success=False,
                    data=None,
                    message=response.message,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return APIResponse(
                success=True,
                data=response.data,
                message=response.message,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Failed to retrieve interviews by user",
            )

    def get_interviews_by_company(self, company_slug) -> APIResponse:
        try:
            company = company_service.get_business_by_slug(slug=company_slug)
            if not company.success:
                return APIResponse(
                    success=False,
                    data=None,
                    message=company.message,
                    status=status.HTTP_404_NOT_FOUND,
                )

            response = repository.get_interviews_by_company(
                company_id=company.data.get("id")
            )

            if not response.success:
                return APIResponse(
                    success=False,
                    data=None,
                    message=response.message,
                    status=status.HTTP_404_NOT_FOUND,
                )

            return APIResponse(
                success=True,
                data=response.data,
                message=response.message,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Failed to retrieve company",
                status=status.HTTP_400_BAD_REQUEST,
            )

    def get_interview_by_id(self, interview_id) -> APIResponse:
        try:
            response = repository.get_interview_by_id(interview_id=interview_id)
            if not response.success:
                return APIResponse(
                    success=False,
                    data=None,
                    message=response.message,
                    status=status.HTTP_404_NOT_FOUND,
                )
            return APIResponse(
                success=True,
                data=response.data,
                message=response.message,
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Failed to interview by id",
                status=status.HTTP_400_BAD_REQUEST,
            )

    def create_interview(self, created_by, user, data):
        try:
            # Validate users
            if not user or not created_by:
                return self.create_error_response(
                    "User and created_by fields are required",
                    status.HTTP_400_BAD_REQUEST,
                )

            if user.id == created_by.id:
                return self.create_error_response(
                    "User cannot create their own interview",
                    status.HTTP_400_BAD_REQUEST,
                )

            # Validate required fields
            required_fields = ["interview_date", "job_id"]
            required_fields_response = logging_service.check_required_fields(
                data, required_fields
            )
            if required_fields_response:
                return self.create_error_response(
                    required_fields_response, status.HTTP_400_BAD_REQUEST
                )

            # Get job first to fail early if not found
            job_id = data.get("job_id")
            try:
                job = Job.objects.get(id=job_id)
            except Job.DoesNotExist:
                return self.create_error_response(
                    "Job does not exist", status.HTTP_404_NOT_FOUND
                )

            # Create interview
            interview_data = {
                "interview_date": data.get("interview_date"),
                "job_applied": job,
                "from_time": data.get("from_time"),
                "to_time": data.get("to_time"),
                "created_by": created_by,
                "user": user,
                "location": data.get("location", ""),
                "message": data.get("message", ""),
                "business": job.company_name,
            }
            response = repository.create_interview(**interview_data)

            if not response.success:
                return self.create_error_response(
                    response.message, status.HTTP_400_BAD_REQUEST
                )

            return APIResponse(
                success=True,
                data=response.data,
                message="Interview created successfully",
                status=status.HTTP_201_CREATED,
            )

        except Exception as e:
            logging_service.log_error(e)
            return self.create_error_response(
                "Failed to create an interview", status.HTTP_400_BAD_REQUEST
            )
        

    def close_interview(self, user_id, job_id) -> APIResponse:

        try:
            interview = Interview.objects.get(
                user_id=user_id,
                job_applied=job_id
            )   
            update_response = repository.update_interview(interview.id, status="Closed")
            if not update_response.success:
                return APIResponse(
                    success=False,
                    data=None,
                    message=update_response.message,
                    status=400,
                )
                
            return APIResponse(
                success=True,
                data=update_response.data,
                message="Interview closed successfully",
                status=200,
            )
        except Interview.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="Interview not found",
                status=404,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Error closing interview",
                status=500,
            )
