from accounts.models import Package
from base.serializers import GetPackageSerializer
from base.utils.status_checker import APIResponse


class PackagesService:
    """CRUD operations methods for PackageModel"""

    def create_package(self, logged_in_user, data):
        """Create a new Package"""

    def get_package(self, package_id):
        """Get a Package by id"""

    def update_package(self, package_id, logged_in_user, data):
        """Update a Package by id"""

    def delete_package(self, package_id, logged_in_user):
        """Delete a Package by id"""

    def get_all_packages(self) -> APIResponse:
        """Get all Packages"""
        packages = Package.objects.all()
        serializer = GetPackageSerializer(packages, many=True)
        return APIResponse(
            success=True,
            data=serializer.data,
            message="Packages retrieved successfully",
        )
