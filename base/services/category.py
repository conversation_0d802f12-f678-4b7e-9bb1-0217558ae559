from typing import Dict, Any, List
from base.repositories.category import CategoryRepository
from candidates.repositories.application_repository import RepositoryResponse


class CategoryService:
    def __init__(self):
        self.repository = CategoryRepository()
    
    def get_all_categories(self) -> RepositoryResponse:
        """
        Retrieve all categories.
        """
        try:
            categories = self.repository.list_categories()
            return RepositoryResponse(
                success=True,
                message="Categories retrieved successfully.",
                data=categories
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                message=str(e),
                data=None
            )
    
    def get_category_by_id(self, category_id) -> RepositoryResponse:
        """
            Retrieve a category by its ID.
        """
        try:
            category = self.repository.get_category_by_id(category_id)
            if not category.success:
                return RepositoryResponse(
                    success=False,
                    message="Category not found.",
                    data=None
                )
            return RepositoryResponse(
                success=True,
                message="Category retrieved successfully.",
                data=category.data
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                message=str(e),
                data=None
            )
    
    def get_category_by_name(self, category_name) -> RepositoryResponse:
        """
        Retrieve a category by its name.
        """
        try:
            category = self.repository.get_category_by_name(category_name)
            if not category.success:
                return RepositoryResponse(
                    success=False,
                    message="Category not found.",
                    data=None
                )
            return RepositoryResponse(
                success=True,
                message="Category retrieved successfully.",
                data=category.data
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                message=str(e),
                data=None
            )
    
    def create_category(self, data: Dict[str, Any]) -> RepositoryResponse:
        """
        Create a new category.
        """
        try:
            if not data["name"]:
                return RepositoryResponse(
                    success=False,
                    message="Category name is required.",
                    data=None
                )
            response = self.repository.create_category(data)
            if not response.success:
                return response
            return RepositoryResponse(
                success=True,
                message="Category created successfully.",
                data=response.data
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                message=str(e),
                data=None
            )
    
    def update_category(self, category_id: int, data: Dict[str, Any]) -> RepositoryResponse:
        """
        Update an existing category.
        """
        try:
            if data["name"] != '':
                response = self.repository.update_category(category_id, data)
            if not response.success:
                return response
            return RepositoryResponse(
                success=True,
                message="Category updated successfully.",
                data=response.data
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                message=str(e),
                data=None
            )
    
    def delete_category(self, category_id: int) -> RepositoryResponse:
        """
        Delete an existing category.
        """
        try:
            response = self.repository.delete_category(category_id)
            if not response.success:
                return response
            return RepositoryResponse(
                success=True,
                message="Category deleted successfully.",
                data=None
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                message=str(e),
                data=None
            )
        
