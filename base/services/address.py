from typing import Optional
from accounts.repositories.user_repository import RepositoryResponse
from base.models import Address, City, Country, State
from candidates.models import Candidate
from candidates.services import candidate
from base.services.logging import LoggingService


logging_service = LoggingService()


class AddressService:
    def get_or_create_address(self, data: dict) -> RepositoryResponse:
        """Creates or retrieves full address with location hierarchy"""
        country_name = data.get("country", "")
        state_name = data.get("state", "")
        city_name = data.get("city", "")
        street_address = data.get("address", "")

        if not street_address:
            return RepositoryResponse(
                success=False,
                message="Street address is required to create an address",
                data=None,
            )

        try:
            address = Address.objects.get(name=street_address)
            return RepositoryResponse(
                success=True,
                data=address,
                message="Address exists in country",
            )
        except:
            # Create location hierarchy
            country_response = self.get_or_create_country(country_name)
            if not country_response.success:
                return country_response

            state_response = self.get_or_create_state(state_name, country_response.data)
            if not state_response.success:
                return state_response

            city_response = self.get_or_create_city(city_name, state_response.data)
            if not city_response.success:
                return city_response

            try:
                address, created = Address.objects.get_or_create(
                    street_address=street_address, city_name=city_response.data
                )

                return RepositoryResponse(
                    success=True,
                    data=address,
                    message=(
                        "Address created successfully"
                        if created
                        else "Address retrieved successfully"
                    ),
                )

            except Exception as e:
                logging_service.log_error(e)
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="Failed to get or create address",
                )

    def get_or_create_country(self, name: str) -> RepositoryResponse:
        """Get or create a country by name"""
        try:
            country, created = Country.objects.get_or_create(name=name)
            return RepositoryResponse(
                success=True,
                data=country,
                message=(
                    "Country created successfully"
                    if created
                    else "Country retrieved successfully"
                ),
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to get or create country",
            )

    def get_or_create_state(self, name: str, country: Country) -> RepositoryResponse:
        """Get or create a state by name and country"""
        try:
            state, created = State.objects.get_or_create(
                name=name, country_name=country
            )
            return RepositoryResponse(
                success=True,
                data=state,
                message=(
                    "State created successfully"
                    if created
                    else "State retrieved successfully"
                ),
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to get or create state",
            )

    def get_or_create_city(self, name: str, state: State) -> RepositoryResponse:
        """Get or create a city by name and state"""
        try:
            city, created = City.objects.get_or_create(name=name, state_name=state)
            return RepositoryResponse(
                success=True,
                data=city,
                message=(
                    "City created successfully"
                    if created
                    else "City retrieved successfully"
                ),
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to get or create city",
            )

    def add_address_to_candidate(self, data, candidate):
        """Add address to candidate"""
        address_response = self.get_or_create_address(data)
        if not address_response.success:
            return address_response

        try:
            candidate.location = address_response.data
            candidate.save()
            return RepositoryResponse(
                success=True,
                data=candidate,
                message="Address added to candidate successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to add address to candidate",
            )
