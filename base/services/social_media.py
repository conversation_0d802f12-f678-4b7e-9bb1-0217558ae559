from typing import Dict, Any, List
from django.utils.translation import gettext_lazy as _
from base.repositories.social_media import SocialMediaPlatformsRepository
from candidates.repositories.application_repository import RepositoryResponse
from base.services.logging import LoggingService


logging_service = LoggingService()


class SocialMediaPlatformService:
    def __init__(self):
        self.repository = SocialMediaPlatformsRepository()

    def create_social_media_platform(self, data: Dict[str, Any]) -> RepositoryResponse:
        """
        Create a new social media platform.
        """
        missing_fields = logging_service.check_required_fields(data, ['handle', 'link'])
        if missing_fields:
            return RepositoryResponse(
                success=False,
                data=None,
                message=missing_fields
            )
        return self.repository.create_social_media_platform(data)

    def get_social_media_platform_by_id(self, id: str) -> RepositoryResponse:
        """
        Retrieve a social media platform by its ID.
        """
        return self.repository.get_social_media_platform_by_id(id)

    def update_social_media_platform(self, id: str, data: Dict[str, Any]) -> RepositoryResponse:
        """
        Update an existing social media platform by its ID.
        """
        missing_fields = logging_service.check_required_fields(data, ['handle', 'link'])
        if missing_fields:
            return RepositoryResponse(
                success=False,
                data=None,
                message=missing_fields
            )

        return self.repository.update_social_media_platform(id, data)

    def delete_social_media_platform(self, id: str) -> RepositoryResponse:
        """
        Delete a social media platform by its ID.
        """
        return self.repository.delete_social_media_platform(id)

    def bulk_update_social_media_platforms(self, user, platforms: List[Dict[str, Any]]) -> RepositoryResponse:
        """
        Update multiple social media platforms associated with a user profile.
        """
        try:
            platform_ids = []

            for platform_data in platforms:
                if "id" in platform_data:
                    response = self.update_social_media_platform(platform_data["id"], platform_data)
                else:
                    platform_data["user"] = user
                    response = self.create_social_media_platform(platform_data)

                if not response.success:
                    return RepositoryResponse(
                        success=False,
                        data=None,
                        message=f"Failed to process platform: {response.message}"
                    )

                platform_ids.append(response.data.id)

            return RepositoryResponse(
                success=True,
                data=platform_ids,
                message="Social media platforms updated successfully."
            )

        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to update platforms: {str(e)}"
            )