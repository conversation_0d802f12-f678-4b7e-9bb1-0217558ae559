from django.test import TransactionTestCase
from accounts.factory import PackageFactory
from rest_framework.test import APIClient


class TestGetPackages(TransactionTestCase):
    def setUp(self):
        self.packages_service = [PackageFactory() for _ in range(3)]
        self.client = APIClient()

    def test_get_packages(self):
        response = self.client.get("/api/base/packages/")

        if not response.status_code == 200:
            self.fail(
                f"Expected status code 200, got {response.status_code} with data: {response.data}"
            )
        self.assertEqual(response.status_code, 200)
        self.assertGreater(len(response.data), 2)
