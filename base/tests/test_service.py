import uuid
from django.test import TestCase
from django.contrib.auth.models import User
from unittest.mock import Mock, patch
from base.models import Category, UserLanguage
from base.models import Skill
from base.services.social_media import SocialMediaPlatformService
from base.services.language import LanguageService
from base.services.skill import SkillService
from base.services.category import CategoryService
from candidates.repositories.application_repository import RepositoryResponse


class TestSocialMediaPlatformService(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser', 
            email='<EMAIL>', 
            password='testpass'
        )
        
        self.service = SocialMediaPlatformService()
        self.test_data = {
            'user': self.user,
            'handle': 'testhandle',
            'link': 'https://example.com/testhandle',
            'name': 'Test Platform'
        }

    def test_create_social_media_platform_success(self):
        """
        Test successful creation of social media platform
        """
        # Create a mock repository response
        mock_repo_response = RepositoryResponse(
            success=True,
            data=Mock(id=uuid.uuid4(), handle='testhandle'),
            message="Social media platform created successfully"
        )

        # Patch the repository method
        with patch.object(self.service.repository, 'create_social_media_platform', return_value=mock_repo_response):
            response = self.service.create_social_media_platform(self.test_data)

        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

    def test_create_social_media_platform_missing_handle(self):
        """
        Test creation of social media platform without handle
        """
        invalid_data = self.test_data.copy()
        del invalid_data['handle']

        response = self.service.create_social_media_platform(invalid_data)

        self.assertFalse(response.success)

    def test_create_social_media_platform_empty_handle(self):
        """
        Test creation of social media platform with empty handle
        """
        invalid_data = self.test_data.copy()
        invalid_data['handle'] = ''

        response = self.service.create_social_media_platform(invalid_data)

        self.assertFalse(response.success)

    def test_create_social_media_platform_missing_link(self):
        """
        Test creation of social media platform without link
        """
        invalid_data = self.test_data.copy()
        del invalid_data['link']

        response = self.service.create_social_media_platform(invalid_data)

        self.assertFalse(response.success)

    def test_create_social_media_platform_empty_link(self):
        """
        Test creation of social media platform with empty link
        """
        invalid_data = self.test_data.copy()
        invalid_data['link'] = ''

        response = self.service.create_social_media_platform(invalid_data)

        self.assertFalse(response.success)

    def test_get_social_media_platform_by_id(self):
        """
        Test retrieving a social media platform by ID
        """
        mock_repo_response = RepositoryResponse(
            success=True,
            data=Mock(id=uuid.uuid4()),
            message="Social media platform retrieved successfully"
        )

        with patch.object(self.service.repository, 'get_social_media_platform_by_id', return_value=mock_repo_response):
            response = self.service.get_social_media_platform_by_id(uuid.uuid4())

        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

    def test_update_social_media_platform_success(self):
        """
        Test successful update of social media platform
        """
        mock_repo_response = RepositoryResponse(
            success=True,
            data=Mock(id=uuid.uuid4(), handle='updatedhandle'),
            message="Social media platform updated successfully"
        )

        update_data = {
            'handle': 'updatedhandle',
            'name': 'Updated Platform'
        }

        with patch.object(self.service.repository, 'update_social_media_platform', return_value=mock_repo_response):
            response = self.service.update_social_media_platform(uuid.uuid4(), update_data)

        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

    def test_update_social_media_platform_empty_handle(self):
        """
        Test update with empty handle
        """
        update_data = {'handle': ''}

        response = self.service.update_social_media_platform(uuid.uuid4(), update_data)

        self.assertFalse(response.success)
        self.assertEqual(response.message, "Handle cannot be empty.")

    def test_update_social_media_platform_empty_link(self):
        """
        Test update with empty link
        """
        update_data = {'link': ''}

        response = self.service.update_social_media_platform(uuid.uuid4(), update_data)

        self.assertFalse(response.success)
        self.assertEqual(response.message, "Link cannot be empty.")

    def test_delete_social_media_platform(self):
        """
        Test deleting a social media platform
        """
        mock_repo_response = RepositoryResponse(
            success=True,
            data=None,
            message="Social media platform deleted successfully"
        )

        with patch.object(self.service.repository, 'delete_social_media_platform', return_value=mock_repo_response):
            response = self.service.delete_social_media_platform(uuid.uuid4())

        self.assertTrue(response.success)

    def test_bulk_update_social_media_platforms_success(self):
        """
        Test bulk update of social media platforms
        """
        platforms = [
            {'handle': 'handle1', 'link': 'https://example1.com', 'name': 'Platform 1'},
            {'handle': 'handle2', 'link': 'https://example2.com', 'name': 'Platform 2'}
        ]

        # Mock repository responses for create/update
        mock_responses = [
            RepositoryResponse(success=True, data=Mock(id=uuid.uuid4()), message="Platform created/updated"),
            RepositoryResponse(success=True, data=Mock(id=uuid.uuid4()), message="Platform created/updated")
        ]

        with patch.object(self.service, 'update_social_media_platform', side_effect=mock_responses):
            with patch.object(self.service, 'create_social_media_platform', side_effect=mock_responses):
                response = self.service.bulk_update_social_media_platforms(self.user, platforms)

        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 2)


class TestLanguageService(TestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser', 
            password='12345'
        )
        
        # Initialize the service
        self.service = LanguageService()

    def test_create_user_language_success(self):
        """Test creating a user language successfully"""
        data = {
            'name': 'Mandarin',
            'created_by': self.user
        }
        
        response = self.service.create_user_language(data)
        
        # Check response
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.name, 'Mandarin')

    def test_create_user_language_missing_name(self):
        """Test creating a user language with missing name"""
        data = {
            'created_by': self.user
        }
        
        response = self.service.create_user_language(data)
        
        # Check response
        self.assertFalse(response.success)
        self.assertIsNone(response.data)

    def test_get_user_language_by_id(self):
        """Test retrieving a user language by ID"""
        # First create a language
        language = UserLanguage.objects.create(
            name='Arabic', 
            created_by=self.user
        )
        
        # Now retrieve it
        response = self.service.get_user_language_by_id(language.id)
        
        # Check response
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.name, 'Arabic')

    def test_update_user_language_success(self):
        """Test updating a user language successfully"""
        # First create a language
        language = UserLanguage.objects.create(
            name='Russian', 
            created_by=self.user
        )
        
        # Update data
        update_data = {'name': 'Updated Russian'}
        response = self.service.update_user_language(language.id, update_data)
        
        # Check response
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, 'Updated Russian')

    def test_update_user_language_empty_name(self):
        """Test updating a user language with empty name"""
        # First create a language
        language = UserLanguage.objects.create(
            name='Hindi', 
            created_by=self.user
        )
        
        # Update data
        update_data = {'name': ''}
        response = self.service.update_user_language(language.id, update_data)
        
        # Check response
        self.assertFalse(response.success)
        self.assertEqual(response.message, "Language name cannot be empty.")

    def test_delete_user_language(self):
        """Test deleting a user language"""
        # First create a language
        language = UserLanguage.objects.create(
            name='Korean', 
            created_by=self.user
        )
        
        # Delete the language
        response = self.service.delete_user_language(language.id)
        
        # Check response
        self.assertTrue(response.success)
        
        # Verify language is actually deleted
        with self.assertRaises(UserLanguage.DoesNotExist):
            UserLanguage.objects.get(id=language.id)

    def test_bulk_update_user_languages(self):
        """Test bulk updating user languages"""
        # Prepare language data for bulk update
        language_list = [
            {'name': 'Swedish'},
            {'name': 'Norwegian'},
            {'name': 'Danish'}
        ]
        
        # Perform bulk update
        response = self.service.bulk_update_user_languages(self.user, language_list)
        
        # Check response
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 3)  # 3 languages created
        
        # Verify each language was created
        for language_id in response.data:
            language = UserLanguage.objects.get(id=language_id)
            self.assertIsNotNone(language)


class TestSkillService(TestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser', 
            password='12345'
        )
        
        # Initialize the service
        self.service = SkillService()

    def test_create_user_skill_success(self):
        """Test creating a user skill successfully"""
        data = {
            'name': 'Python',
            'created_by': self.user
        }
        
        response = self.service.create_skill(data)
        
        # Check response
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data["name"], "Python")
    
    def test_create_user_skill_missing_name(self):
        """Test creating a user skill with missing name"""
        data = {
            'created_by': self.user
        }
        
        response = self.service.create_skill(data)
        
        # Check response
        self.assertFalse(response.success)
        self.assertIsNone(response.data)
    
    def test_get_user_skill_by_id(self):
        """Test retrieving a user skill by ID"""
        # First create a skill
        skill = Skill.objects.create(
            name='JavaScript', 
            created_by=self.user
        )
        
        # Now retrieve it
        response = self.service.get_skill_by_id(skill.id)
        
        # Check response
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data["name"], "JavaScript")
    
    def test_update_user_skill_success(self):
        """Test updating a user skill successfully"""
        # First create a skill
        skill = Skill.objects.create(
            name='Java', 
            created_by=self.user
        )
        
        # Update data
        update_data = {'name': 'Updated Java'}
        response = self.service.update_skill(skill.id, update_data)
        
        # Check response
        self.assertTrue(response.success)
        self.assertEqual(response.data["name"], 'Updated Java')
    
    def test_update_user_skill_empty_name(self):
        """Test updating a user skill with empty name"""
        # First create a skill
        skill = Skill.objects.create(
            name='C#', 
            created_by=self.user
        )
        
        # Update data
        update_data = {'name': ''}
        response = self.service.update_skill(skill.id, update_data)
        
        # Check response
        self.assertFalse(response.success)
    
    def test_delete_user_skill(self):
        """Test deleting a user skill"""
        # First create a skill
        skill = Skill.objects.create(
            name='Go', 
            created_by=self.user
        )
        
        # Delete the skill
        response = self.service.delete_skill(skill.id)
        
        # Check response
        self.assertTrue(response.success)
        
        # Verify skill is actually deleted
        with self.assertRaises(Skill.DoesNotExist):
            Skill.objects.get(id=skill.id)


class TestCategoryService(TestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser', 
            password='12345'
        )
        
        # Initialize the service
        self.service = CategoryService()
    
    def test_create_category_success(self):
        """Test creating a category successfully"""
        data = {
            'name': 'Programming',
            'created_by': self.user
        }
        
        response = self.service.create_category(data)
        
        # Check response
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.name, "Programming")
    
    def test_create_category_missing_name(self):
        """Test creating a category with missing name"""
        data = {
            'created_by': self.user
        }
        
        response = self.service.create_category(data)
        
        # Check response
        self.assertFalse(response.success)
        self.assertIsNone(response.data)
    
    def test_get_category_by_id(self):
        """Test retrieving a category by ID"""
        # First create a category
        category = Category.objects.create(
            name='Development', 
            created_by=self.user
        )
        
        # Now retrieve it
        response = self.service.get_category_by_id(category.id)
        
        # Check response
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(str(response.data), "Development")
    
    def test_get_category_by_id_fail(self):
        """Test retrieving a non-existent category by ID"""
        # Now retrieve it
        response = self.service.get_category_by_id(9999999999999999999)
        
        # Check response
        self.assertFalse(response.success)
        self.assertIsNone(response.data)
    
    def test_get_category_by_name(self):
        """Test retrieving a category by name"""
        # First create a category
        category = Category.objects.create(
            name='Design', 
            created_by=self.user
        )
        
        # Now retrieve it
        response = self.service.get_category_by_name(category.name)
        
        # Check response
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(str(response.data), "Design")
    
    def test_get_category_by_name_fail(self):
        """Test retrieving a non-existent category by name"""
        # Now retrieve it
        response = self.service.get_category_by_name("Non-existent Category")
        
        # Check response
        self.assertFalse(response.success)
        self.assertIsNone(response.data)
    
    def test_update_category_success(self):
        """Test updating a category successfully"""
        # First create a category
        category = Category.objects.create(
            name='Marketing', 
            created_by=self.user
        )
        
        # Update data
        update_data = {'name': 'Updated Marketing'}
        response = self.service.update_category(category.id, update_data)
        
        # Check response
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, 'Updated Marketing')
    
    def test_update_category_empty_name(self):
        """Test updating a category with empty name"""
        # First create a category
        category = Category.objects.create(
            name='Finance', 
            created_by=self.user
        )
        
        # Update data
        update_data = {'name': ''}
        response = self.service.update_category(category.id, update_data)
        
        # Check response
        self.assertFalse(response.success)
    
    def test_delete_category(self):
        """Test deleting a category"""
        # First create a category
        category = Category.objects.create(
            name='HR', 
            created_by=self.user
        )
        
        # Delete the category
        response = self.service.delete_category(category.id)
        
        # Check response
        self.assertTrue(response.success)
        
        # Verify category is actually deleted
        with self.assertRaises(Category.DoesNotExist):
            Category.objects.get(id=category.id)
