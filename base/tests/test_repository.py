import uuid
from django.test import TestCase
from django.contrib.auth.models import User
from base.models import Category, UserLanguage
from base.models import SocialMediaPlatforms
from base.repositories.social_media import SocialMediaPlatformsRepository
from unittest.mock import Mock, patch
from base.repositories.language import UserLanguageRepository
from base.models import Skill
from base.repositories.skill import SkillRepository
from base.repositories.category import CategoryRepository


class TestSocialMediaPlatformsRepository(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser', 
            email='<EMAIL>', 
            password='testpass'
        )
        
        self.repository = SocialMediaPlatformsRepository()
        self.test_data = {
            'user': self.user,
            'handle': 'testhandle',
            'link': 'https://example.com/testhandle',
            'name': 'Test Platform'
        }

    def test_create_social_media_platform_success(self):
        """
        Test successful creation of a social media platform
        """
        response = self.repository.create_social_media_platform(self.test_data)
        
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.handle, self.test_data['handle'])
        self.assertIsInstance(response.data.id, uuid.UUID)

    def test_create_social_media_platform_with_existing_data(self):
        """
        Test creating multiple social media platforms
        """
        # First creation
        first_response = self.repository.create_social_media_platform(self.test_data)
        self.assertTrue(first_response.success)

        # Second creation with same data
        second_data = self.test_data.copy()
        second_data['name'] = 'Another Test Platform'
        second_response = self.repository.create_social_media_platform(second_data)
        
        self.assertTrue(second_response.success)
        self.assertNotEqual(first_response.data.id, second_response.data.id)

    def test_get_social_media_platform_by_id_success(self):
        """
        Test successful retrieval of a social media platform by ID
        """
        # First, create a platform
        created_platform_response = self.repository.create_social_media_platform(self.test_data)
        self.assertTrue(created_platform_response.success)
        
        platform_id = created_platform_response.data.id

        # Now retrieve it
        response = self.repository.get_social_media_platform_by_id(platform_id)
        
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.id, platform_id)

    def test_get_social_media_platform_by_id_not_found(self):
        """
        Test retrieval of a non-existent social media platform
        """
        # Generate a random UUID that doesn't exist
        non_existent_id = uuid.uuid4()
        
        response = self.repository.get_social_media_platform_by_id(non_existent_id)
        
        self.assertFalse(response.success)
        self.assertIsNone(response.data)

    def test_update_social_media_platform_success(self):
        """
        Test successful update of a social media platform
        """
        # First, create a platform
        created_platform_response = self.repository.create_social_media_platform(self.test_data)
        self.assertTrue(created_platform_response.success)
        
        platform_id = created_platform_response.data.id

        # Update data
        update_data = {
            'handle': 'updatedhandle',
            'link': 'https://example.com/updatedhandle',
            'name': self.test_data['name']
        }

        response = self.repository.update_social_media_platform(platform_id, update_data)
        
        self.assertTrue(response.success)
        self.assertEqual(response.data.handle, update_data['handle'])
        self.assertEqual(response.data.link, update_data['link'])

    def test_update_social_media_platform_not_found(self):
        """
        Test update of a non-existent social media platform
        """
        non_existent_id = uuid.uuid4()
        update_data = {
            'handle': 'newhandle',
            'name': 'Updated Platform'
        }

        response = self.repository.update_social_media_platform(non_existent_id, update_data)
        
        self.assertFalse(response.success)
        self.assertIsNone(response.data)

    def test_delete_social_media_platform_success(self):
        """
        Test successful deletion of a social media platform
        """
        # First, create a platform
        created_platform_response = self.repository.create_social_media_platform(self.test_data)
        self.assertTrue(created_platform_response.success)
        
        platform_id = created_platform_response.data.id

        # Delete the platform
        response = self.repository.delete_social_media_platform(platform_id)
        
        self.assertTrue(response.success)
        self.assertIsNone(response.data)
        
        # Verify platform is actually deleted
        with self.assertRaises(SocialMediaPlatforms.DoesNotExist):
            SocialMediaPlatforms.objects.get(pk=platform_id)

    def test_delete_social_media_platform_not_found(self):
        """
        Test deletion of a non-existent social media platform
        """
        non_existent_id = uuid.uuid4()

        response = self.repository.delete_social_media_platform(non_existent_id)
        
        self.assertFalse(response.success)
        self.assertIsNone(response.data)


class UserLanguageRepositoryTest(TestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser', 
            password='12345'
        )
        
        # Initialize the repository
        self.repository = UserLanguageRepository()

    def test_create_user_language(self):
        """Test creating a user language via repository"""
        data = {
            'name': 'French',
            'created_by': self.user
        }
        
        response = self.repository.create_user_language(data)
        
        # Check response
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.name, 'French')

    def test_get_user_language_by_id(self):
        """Test retrieving a user language by ID"""
        # First create a language
        language = UserLanguage.objects.create(
            name='German', 
            created_by=self.user
        )
        
        # Now retrieve it
        response = self.repository.get_user_language_by_id(language.id)
        
        # Check response
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.name, 'German')

    def test_update_user_language(self):
        """Test updating a user language"""
        # First create a language
        language = UserLanguage.objects.create(
            name='Italian', 
            created_by=self.user
        )
        
        # Update data
        update_data = {'name': 'Updated Italian'}
        response = self.repository.update_user_language(language.id, update_data)
        
        # Check response
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, 'Updated Italian')

    def test_delete_user_language(self):
        """Test deleting a user language"""
        # First create a language
        language = UserLanguage.objects.create(
            name='Portuguese', 
            created_by=self.user
        )
        
        # Delete the language
        response = self.repository.delete_user_language(language.id)
        
        # Check response
        self.assertTrue(response.success)
        
        # Verify language is actually deleted
        with self.assertRaises(UserLanguage.DoesNotExist):
            UserLanguage.objects.get(id=language.id)

    def test_get_nonexistent_user_language(self):
        """Test retrieving a non-existent user language"""
        # Use a random UUID
        non_existent_id = '123e4567-e89b-12d3-a456-************'
        
        response = self.repository.get_user_language_by_id(non_existent_id)
        
        # Check response
        self.assertFalse(response.success)
        self.assertIsNone(response.data)


class TestSkillRepository(TestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser', 
            password='12345'
        )
        
        # Initialize the repository
        self.repository = SkillRepository()
    
    def test_create_skill(self):
        """Test creating a skill via repository"""
        data = {
            'name': 'Python',
            'created_by': self.user
        }
        
        response = self.repository.create_skill(data)
        
        # Check response
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.name, 'Python')
    
    def test_get_skill_by_id(self):
        """Test retrieving a skill by ID"""
        # First create a skill
        skill = Skill.objects.create(
            name='JavaScript', 
            created_by=self.user
        )
        
        # Now retrieve it
        response = self.repository.get_skill_by_id(skill.id)
        
        # Check response
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.name, 'JavaScript')
    
    def test_update_skill(self):
        """Test updating a skill"""
        # First create a skill
        skill = Skill.objects.create(
            name='Java', 
            created_by=self.user
        )
        
        # Update data
        update_data = {'name': 'Updated Java'}
        response = self.repository.update_skill(skill.id, update_data)
        
        # Check response
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, 'Updated Java')
    
    def test_delete_skill(self):
        """Test deleting a skill"""
        # First create a skill
        skill = Skill.objects.create(
            name='C#', 
            created_by=self.user
        )
        
        # Delete the skill
        response = self.repository.delete_skill(skill.id)
        
        # Check response
        self.assertTrue(response.success)
        
        # Verify skill is actually deleted
        with self.assertRaises(Skill.DoesNotExist):
            Skill.objects.get(id=skill.id)
        
    def test_get_nonexistent_skill(self):
        """Test retrieving a non-existent skill"""
        # Use a random UUID
        non_existent_id = '123e4567-e89b-12d3-a456-************'
        
        response = self.repository.get_skill_by_id(non_existent_id)
        
        # Check response
        self.assertFalse(response.success)
        self.assertIsNone(response.data)
    

class TestCategoryRepository(TestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser', 
            password='12345'
        )
        
        # Initialize the repository
        self.repository = CategoryRepository()
    
    def test_create_category(self):
        """Test creating a category via repository"""
        data = {
            'name': 'Technology',
            'created_by': self.user
        }
        
        response = self.repository.create_category(data)
        
        # Check response
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.name, 'Technology')
    
    def test_get_category_by_id(self):
        """Test retrieving a category by ID"""
        # First create a category
        category = Category.objects.create(
            name='Education', 
            created_by=self.user
        )
        
        # Now retrieve it
        response = self.repository.get_category_by_id(category.id)
        
        # Check response
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.name, 'Education')
    
    def test_get_nonexistent_category(self):
        """Test retrieving a non-existent category"""
        # Use a random UUID
        non_existent_id = '123e4567-e89b-12d3-a456-************'
        
        response = self.repository.get_category_by_id(non_existent_id)
        
        # Check response
        self.assertFalse(response.success)
        self.assertIsNone(response.data)
    
    def test_update_category(self):
        """Test updating a category"""
        # First create a category
        category = Category.objects.create(
            name='Health', 
            created_by=self.user
        )
        
        # Update data
        update_data = {'name': 'Updated Health'}
        response = self.repository.update_category(category.id, update_data)
        
        # Check response
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, 'Updated Health')
    
    def test_get_all_categories(self):
        """Test retrieving all categories"""
        # Create categories
        Category.objects.create(
            name='Sports', 
            created_by=self.user
        )
        Category.objects.create(
            name='Music', 
            created_by=self.user
        )
        Category.objects.create(
            name='Gaming', 
            created_by=self.user
        )
        # Retrieve all categories
        response = self.repository.list_categories()
        
        # Check response
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 3)
        self.assertEqual(response.data[0].name, 'Sports')
        self.assertEqual(response.data[1].name, 'Music')
        self.assertEqual(response.data[2].name, 'Gaming')
    
    def test_delete_category(self):
        """Test deleting a category"""
        # First create a category
        category = Category.objects.create(
            name='Finance', 
            created_by=self.user
        )
        
        # Delete the category
        response = self.repository.delete_category(category.id)
        
        # Check response
        self.assertTrue(response.success)
        
        # Verify category is actually deleted
        with self.assertRaises(Category.DoesNotExist):
            Category.objects.get(id=category.id)
    
    def test_get_category_by_name(self):
        """Test retrieving a category by name"""
        # First create a category
        Category.objects.create(
            name='Food', 
            created_by=self.user
        )
        
        # Retrieve it
        response = self.repository.get_category_by_name('Food')
        
        # Check response
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.name, 'Food')
    
    def test_get_nonexistent_category_by_name(self):
        """Test retrieving a non-existent category by name"""
        response = self.repository.get_category_by_name('Nonexistent Category')
        
        # Check response
        self.assertFalse(response.success)
        self.assertIsNone(response.data)
    
    