from django.test import TransactionTestCase

from accounts.factory import PackageFactory
from base.services.packagess import PackagesService


packages_service = PackagesService()


class TestPackagesCase(TransactionTestCase):
    def setUp(self):
        self.packages = [PackageFactory() for _ in range(5)]

    def test_get_packages(self):
        response = packages_service.get_all_packages()

        if not response.success:
            self.fail(f"Failed to get packages {response.message}")

        self.assertGreater(len(response.data), 4)
