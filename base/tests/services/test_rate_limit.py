from django.test import TestCase, RequestFactory
from django.contrib.auth.models import User, AnonymousUser
from base.services.rate_limit import RateLimit


class RateLimitTestCase(TestCase):
    def setUp(self):
        self.factory = RequestFactory()
        self.admin_user = User.objects.create_user(
            username="admin",
            email="<EMAIL>",
            password="adminpass",
            is_staff=True,
        )
        self.regular_user = User.objects.create_user(
            username="user",
            email="<EMAIL>",
            password="userpass",
            is_staff=False,
        )

    def test_skip_for_admins_with_admin_user(self):
        request = self.factory.get("/")
        request.user = self.admin_user
        self.assertTrue(RateLimit.skip_for_admins(request))

    def test_skip_for_admins_with_regular_user(self):
        request = self.factory.get("/")
        request.user = self.regular_user
        self.assertFalse(RateLimit.skip_for_admins(request))

    def test_skip_for_admins_with_anonymous_user(self):
        request = self.factory.get("/")
        request.user = AnonymousUser()
        self.assertFalse(RateLimit.skip_for_admins(request))
