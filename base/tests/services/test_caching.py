from django.test import TestCase, override_settings
from django.core.cache import cache

from base.services.caching import CachingService


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestCachingService(TestCase):
    def setUp(self):
        self.caching_service = CachingService()
        cache.clear()

    def tearDown(self):
        cache.clear()

    def test_set_and_get_cache(self):
        """Test setting and retrieving an object from cache"""
        result = self.caching_service.setCache("test_key", "test_value")
        self.assertTrue(result)
        
        value = self.caching_service.getCache("test_key")
        self.assertEqual(value, "test_value")
        
        test_dict = {"name": "Test User", "age": 30, "email": "<EMAIL>"}
        self.caching_service.setCache("test_dict", test_dict)
        retrieved_dict = self.caching_service.getCache("test_dict")
        self.assertEqual(retrieved_dict, test_dict)

    def test_get_cache_with_default(self):
        """Test getting a non-existent key with a default value"""
        value = self.caching_service.getCache("non_existent_key", "default_value")
        self.assertEqual(value, "default_value")

    def test_delete_cache(self):
        """Test deleting a key from the cache"""
        self.caching_service.setCache("delete_test", "value_to_delete")
        
        self.assertEqual(self.caching_service.getCache("delete_test"), "value_to_delete")
        
        result = self.caching_service.deleteCache("delete_test")
        self.assertTrue(result)
        
        self.assertIsNone(self.caching_service.getCache("delete_test"))

    def test_has_cache(self):
        """Test checking if a key exists in the cache"""
        self.assertFalse(self.caching_service.hasCache("has_test"))
        
        self.caching_service.setCache("has_test", "test_value")
        
        self.assertTrue(self.caching_service.hasCache("has_test"))

    def test_clear_cache(self):
        """Test clearing the entire cache"""
        self.caching_service.setCache("clear_test_1", "value1")
        self.caching_service.setCache("clear_test_2", "value2")
        
        self.assertEqual(self.caching_service.getCache("clear_test_1"), "value1")
        self.assertEqual(self.caching_service.getCache("clear_test_2"), "value2")
        
        result = self.caching_service.clearCache()
        self.assertTrue(result)
        
        self.assertIsNone(self.caching_service.getCache("clear_test_1"))
        self.assertIsNone(self.caching_service.getCache("clear_test_2"))

    def test_update_cache(self):
        """Test updating an existing cache entry"""
        self.caching_service.setCache("update_test", "initial_value")
        
        result = self.caching_service.updateCache("update_test", "updated_value")
        self.assertTrue(result)
        
        self.assertEqual(self.caching_service.getCache("update_test"), "updated_value")
        
        result = self.caching_service.updateCache("non_existent_key", "some_value")
        self.assertFalse(result)

    def test_add_to_cache_list(self):
        """Test adding an object to a list in the cache"""
        result = self.caching_service.addToCacheList("list_test", "item1")
        self.assertTrue(result)
        
        cached_list = self.caching_service.getCache("list_test")
        self.assertEqual(cached_list, ["item1"])
        
        self.caching_service.addToCacheList("list_test", "item2")
        cached_list = self.caching_service.getCache("list_test")
        self.assertEqual(cached_list, ["item1", "item2"])
        
        test_dict = {"id": 1, "name": "Test User"}
        self.caching_service.addToCacheList("list_test", test_dict)
        cached_list = self.caching_service.getCache("list_test")
        self.assertEqual(cached_list, ["item1", "item2", test_dict])

    def test_remove_from_cache_list(self):
        """Test removing an object from a list in the cache"""
        self.caching_service.setCache("remove_list_test", ["item1", "item2", "item3"])
        
        result = self.caching_service.removeFromCacheList("remove_list_test", "item2")
        self.assertTrue(result)
        
        cached_list = self.caching_service.getCache("remove_list_test")
        self.assertEqual(cached_list, ["item1", "item3"])
        
        result = self.caching_service.removeFromCacheList("remove_list_test", "non_existent_item")
        self.assertFalse(result)
        
        self.caching_service.setCache("not_a_list", "string_value")
        result = self.caching_service.removeFromCacheList("not_a_list", "item")
        self.assertFalse(result)

    def test_update_cache_item(self):
        """Test updating a specific item in a cached list"""
        items = [
            {"id": 1, "name": "Item 1"},
            {"id": 2, "name": "Item 2"},
            {"id": 3, "name": "Item 3"}
        ]
        self.caching_service.setCache("update_item_test", items)
        
        updated_item = {"id": 2, "name": "Updated Item 2"}
        result = self.caching_service.updateCacheItem("update_item_test", 2, updated_item)
        self.assertTrue(result)
        
        cached_list = self.caching_service.getCache("update_item_test")
        updated_item_in_cache = next((item for item in cached_list if item["id"] == 2), None)
        self.assertEqual(updated_item_in_cache["name"], "Updated Item 2")
        
        non_existent_item = {"id": 99, "name": "Non-existent"}
        result = self.caching_service.updateCacheItem("update_item_test", 99, non_existent_item)
        self.assertTrue(result)
        
        cached_list = self.caching_service.getCache("update_item_test")
        self.assertEqual(len(cached_list), 4)
        
        self.caching_service.setCache("not_a_list", "string_value")
        result = self.caching_service.updateCacheItem("not_a_list", 1, {"id": 1, "value": "test"})
        self.assertFalse(result)

    def test_remove_cache_item(self):
        """Test removing a specific item from a cached list"""
        items = [
            {"id": 1, "name": "Item 1"},
            {"id": 2, "name": "Item 2"},
            {"id": 3, "name": "Item 3"}
        ]
        self.caching_service.setCache("remove_item_test", items)
        
        result = self.caching_service.removeCacheItem("remove_item_test", 2)
        self.assertTrue(result)
        
        cached_list = self.caching_service.getCache("remove_item_test")
        self.assertEqual(len(cached_list), 2)
        self.assertFalse(any(item["id"] == 2 for item in cached_list))
        
        result = self.caching_service.removeCacheItem("remove_item_test", 99)
        self.assertFalse(result)
        
        items = [
            {"user_id": "a1", "name": "User A"},
            {"user_id": "b2", "name": "User B"},
            {"user_id": "c3", "name": "User C"}
        ]
        self.caching_service.setCache("custom_id_test", items)
        
        result = self.caching_service.removeCacheItem("custom_id_test", "b2", "user_id")
        self.assertTrue(result)
        
        cached_list = self.caching_service.getCache("custom_id_test")
        self.assertEqual(len(cached_list), 2)
        self.assertFalse(any(item["user_id"] == "b2" for item in cached_list))
        
        self.caching_service.setCache("not_a_list", "string_value")
        result = self.caching_service.removeCacheItem("not_a_list", 1)
        self.assertFalse(result)
