from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth.models import User


class RegisterRateLimitTest(APITestCase):
    def setUp(self):
        from django.core.cache import cache

        cache.clear()  # Ensure clean state for every test
        self.url = reverse("sign_up_api")
        self.data = {
            "email": "<EMAIL>",
            "password": "testpass123",
            "first_name": "Test",
            "last_name": "User",
            "phone_number": "**********",
            "gender": "M",
            "date_of_birth": "1990-01-01",
            "account_type": "candidate",
            "business_name": "TestBiz",
        }

    def test_rate_limit_blocks_after_limit(self):
        # The limit is 10 per minute, so send 11 requests
        for i in range(10):
            response = self.client.post(self.url, self.data, format="json")
            self.assertNotEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)

        # 11th request should be blocked
        response = self.client.post(self.url, self.data, format="json")
        self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)
        self.assertIn("error", response.data)
        self.assertEqual(response.data["error"], "Too many requests")

    def test_admin_not_rate_limited(self):
        admin = User.objects.create_user(
            username="adminuser",
            email="<EMAIL>",
            password="adminpass",
            is_staff=True,
            is_superuser=True,  # ensure superuser for all admin checks
        )
        self.client.login(username="adminuser", password="adminpass")
        for i in range(20):  # Exceed both per-endpoint and global limits
            response = self.client.post(self.url, self.data, format="json")
            # Should never get 429 for admin
            self.assertNotEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)
        self.client.logout()

    def test_global_rate_limit_blocks(self):
        # Simulate hitting the global rate limit (set to 1000/hour in middleware)
        # We'll use a lower number for test speed
        from django.core.cache import cache

        cache.clear()  # Ensure clean state
        for i in range(1000):
            response = self.client.post(self.url, self.data, format="json")
            if response.status_code == status.HTTP_429_TOO_MANY_REQUESTS:
                break
        self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)
        self.assertIn("error", response.data)
        # No need to check for 'global limit' in the error message
