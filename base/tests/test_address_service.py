import json
from django.test import TestCase
from base.models import Address, City, Country, State
from accounts.repositories.user_repository import RepositoryResponse
from base.services.address import AddressService


class AddressServiceTestCase(TestCase):
    def setUp(self):
        """Set up initial data for testing."""
        self.service = AddressService()
        self.country = Country.objects.create(name="United States")
        self.state = State.objects.create(name="New York", country_name=self.country)
        self.city = City.objects.create(name="New York City", state_name=self.state)
        self.address = Address.objects.create(name="Address", city_name=self.city)

    def test_get_or_create_country(self):
        """Test the creation and retrieval of a country."""
        # Test creation
        response = self.service.get_or_create_country(name="Canada")
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, "Canada")
        self.assertIsInstance(response.data, Country)

        # Test retrieval of existing
        second_response = self.service.get_or_create_country("Canada")
        self.assertTrue(second_response.success)
        self.assertEqual(second_response.data.name, "Canada")
        self.assertEqual(Country.objects.filter(name="Canada").count(), 1)

    def test_get_or_create_state(self):
        """Test the creation and retrieval of a state."""
        # Test creation
        country = Country.objects.create(name="State Country")
        response = self.service.get_or_create_state("New State", country)

        self.assertTrue(response.success)
        self.assertEqual(response.data.name, "New State")
        self.assertEqual(response.data.country_name, country)
        self.assertIsInstance(response.data, State)

    def test_get_or_create_city(self):
        """Test the creation and retrieval of a city."""
        # Test creation
        country = Country.objects.create(name="City Country")
        state = State.objects.create(name="City State", country_name=country)
        response = self.service.get_or_create_city("New City", state)

        self.assertTrue(response.success)

    def test_get_or_create_address(self):
        """Test the creation and retrieval of an address."""
        # Test creation with JSON data
        data = {
            "address": "123 Test Street",
            "city": "City 2",
            "state": "State 2",
            "country": "Country 2",
        }
        response = self.service.get_or_create_address(data)
        print(response.data)
        self.assertTrue(response.success)
