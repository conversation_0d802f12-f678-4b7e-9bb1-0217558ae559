import email
from django.test import TransactionTestCase
from rest_framework.test import APIClient
from datetime import datetime, timedelta
from core.settings import SECRET_KEY
import jwt
from accounts.factory import *
from base.factory import *
from businesses.factory import *


class BaseSetup(TransactionTestCase):
    def setUp(self):
        self.user = UserFactory()
        self.company = CompanyFactory(email="<EMAIL>")
        self.package = PackageFactory()
        self.free_package = PackageFactory(
            name="Free Package", has_limit=False, price=0
        )
        self.client = APIClient()

        self.admin = UserFactory()
        self.admin.is_admin = True
        self.admin.save()

        # endpoints
        self.endpoint = "/api"
        self.business_endpoint = f"{self.endpoint}/business"
        self.accounts_endpoint = f"{self.endpoint}/accounts"
        self.candidates_endpoint = f"{self.endpoint}/candidates"
        self.admin_endpoints = f"{self.endpoint}/admin"
        self.invoices_endpoints = f"{self.endpoint}/admin/invoices"

    def _authenticate_user(self, user):
        """Helper method to authenticate a user"""

        token_payload = {
            "user_id": user.id,
            "exp": datetime.now() + timedelta(days=1),
        }
        jwt_token = jwt.encode(token_payload, SECRET_KEY, algorithm="HS256")

        self.client = APIClient()
        self.client.force_authenticate(user=user)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {jwt_token}")
