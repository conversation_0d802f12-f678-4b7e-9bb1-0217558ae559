from django.test import TestCase

from base.factory import UserFactory
from base.services.interviews import InterviewService
from businesses.factory import CompanyFactory
from candidates.factory import InterviewFactory
from jobs.factory import JobFactory


class TestInterviewService(TestCase):
    def setUp(self):
        self.service = InterviewService()
        self.user = UserFactory()
        self.created_by = UserFactory()
        self.company = CompanyFactory()
        self.job = JobFactory()
        self.interview = InterviewFactory(
            job_applied=self.job,
            user=self.user,
            business=self.company,
            status="Upcoming",
            message="Test interview",
            interview_date="2024-12-20 10:00:00",
            from_time="10:00:00",
            to_time="11:00:00",
            location="Online",
        )

    def test_create_interview_success(self):
        interview_data = {
            "interview_date": "2024-12-20 10:00:00",
            "job_id": self.job.id,
            "from_time": "10:00:00",
            "to_time": "11:00:00",
            "location": "Online",
            "message": "Test interview",
        }
        response = self.service.create_interview(
            self.created_by,
            self.user,
            interview_data,
        )
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.job_applied, self.job)
        self.assertEqual(response.data.user, self.user)
        self.assertEqual(response.data.business, self.job.company_name)
        self.assertEqual(response.data.status, "Upcoming")
    
    def test_create_interview_user_self_creation_error(self):
        interview_data = {
            "interview_date": "2024-12-20 10:00:00",
            "job_id": self.job.id,
            "from_time": "10:00:00",
            "to_time": "11:00:00",
            "location": "Online",
            "message": "Test interview",
        }
        response = self.service.create_interview(self.user, self.user, interview_data)
        self.assertFalse(response.success)

    def test_create_interview_missing_required_fields(self):
        incomplete_data = {
            "interview_date": "2024-12-20 10:00:00",
            "from_time": "10:00:00",
            "to_time": "11:00:00",
            "location": "Online",
        }
        response = self.service.create_interview(
            self.created_by, self.user, incomplete_data
        )
        self.assertFalse(response.success)

    def test_create_interview_job_not_found(self):
        interview_data = {
            "interview_date": "2024-12-20 10:00:00",
            "job_id": 999,
            "from_time": "10:00:00",
            "to_time": "11:00:00",
            "location": "Online",
            "message": "Test interview",
        }
        response = self.service.create_interview(
            self.created_by, self.user, interview_data
        )
        self.assertFalse(response.success)

    def test_get_interviews_user_success(self):
        response = self.service.get_interviews_user(self.user.id)
        self.assertTrue(response.success)
        self.assertIn(self.interview, response.data)

    def test_get_interviews_user_no_interviews(self):
        other_user = UserFactory()
        response = self.service.get_interviews_user(other_user.id)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 0)

    def test_get_interviews_by_company_success(self):
        response = self.service.get_interviews_by_company(self.company.slug)
        self.assertTrue(response.success)
        self.assertIn(self.interview, response.data)

    def test_get_interviews_by_company_no_interviews(self):
        other_company = CompanyFactory()
        response = self.service.get_interviews_by_company(other_company.slug)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 0)

    def test_get_interview_by_id_success(self):
        response = self.service.get_interview_by_id(self.interview.id)
        self.assertTrue(response.success)
        self.assertEqual(response.data, self.interview)

    def test_get_interview_by_id_failure(self):
        response = self.service.get_interview_by_id(999)
        self.assertFalse(response.success)
    
    def test_close_interview_success(self):
        response = self.service.close_interview(self.user.id, self.job.id)
        self.assertTrue(response.success)
        self.assertEqual(response.data.status, "Closed")