from django.test import TestCase
from base.factory import UserFactory
from base.repositories.interviews import InterviewRepository
from businesses.factory import CompanyFactory
from candidates.factory import InterviewFactory
from jobs.factory import JobFactory


class TestInterviewRepository(TestCase):
    def setUp(self):
        self.interview_repository = InterviewRepository()
        self.job = JobFactory()
        self.user = UserFactory()
        self.company = CompanyFactory()
        self.interview = InterviewFactory(
            job_applied=self.job,
            user=self.user,
            business=self.company,
            status="Upcoming",
            message="Test interview",
            interview_date="2024-12-20 10:00:00",
            from_time="10:00:00",
            to_time="11:00:00",
            location="Online",
        )

    def test_create_interview_success(self):
        interview_data = {
            "job_applied": self.job,
            "user": self.user,
            "business": self.company,
            "status": "Upcoming",
            "message": "Test interview",
            "interview_date": "2024-12-20 10:00:00",
            "from_time": "10:00:00",
            "to_time": "11:00:00",
            "location": "Online",
        }
        response = self.interview_repository.create_interview(**interview_data)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.job_applied, self.job)
        self.assertEqual(response.data.user, self.user)
        self.assertEqual(response.data.business, self.company)
        self.assertEqual(response.data.status, "Upcoming")
    
    def test_get_interviews_by_user_success(self):
        response = self.interview_repository.get_interviews_by_user(self.user.id)
        self.assertTrue(response.success)
        self.assertIn(self.interview, response.data)

    def test_get_interviews_by_user_failure(self):
        response = self.interview_repository.get_interviews_by_user(999)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 0)

    def test_get_interviews_by_company_success(self):
        response = self.interview_repository.get_interviews_by_company(self.company.id)
        self.assertTrue(response.success)
        self.assertIn(self.interview, response.data)

    def test_get_interviews_by_company_failure(self):
        response = self.interview_repository.get_interviews_by_company(999)
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 0)

    def test_get_interview_by_id_success(self):
        response = self.interview_repository.get_interview_by_id(self.interview.id)
        self.assertTrue(response.success)
        self.assertEqual(response.data, self.interview)

    def test_get_interview_by_id_failure(self):
        response = self.interview_repository.get_interview_by_id(999)
        self.assertFalse(response.success)
        self.assertIsNone(response.data)

    def test_update_interview_success(self):
        updated_data = {"status": "Completed", "location": "In-person"}
        response = self.interview_repository.update_interview(self.interview.id, **updated_data)
        self.assertTrue(response.success)
        self.assertEqual(response.data.status, "Completed")
        self.assertEqual(response.data.location, "In-person")

    def test_update_interview_failure(self):
        updated_data = {"status": "Completed"}
        response = self.interview_repository.update_interview(999, **updated_data)
        self.assertFalse(response.success)
        self.assertIsNone(response.data)

    def test_delete_interview_success(self):
        response = self.interview_repository.delete_interview(self.interview.id)
        self.assertTrue(response.success)
        self.assertIsNone(response.data)

    def test_delete_interview_failure(self):
        response = self.interview_repository.delete_interview(999)
        self.assertFalse(response.success)
        self.assertIsNone(response.data)
