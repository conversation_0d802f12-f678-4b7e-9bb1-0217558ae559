from django.contrib import admin

from base.models import *


class BaseModelAdminMixin(admin.ModelAdmin):
    list_display = ("id", "name", "date_created", "date_updated", "created_by")
    prepopulated_fields = {"slug": ("name",)}


class AdminSkillCategory(BaseModelAdminMixin):
    pass


class AdminSkill(BaseModelAdminMixin):
    list_display = ("id", "name", "category_name")


class AdminCountry(BaseModelAdminMixin):
    pass


class StateAdmin(BaseModelAdminMixin):
    list_display = ("id", "name", "country")


class AdminCity(BaseModelAdminMixin):
    list_display = ("id", "name", "state")


class AdminUserLanguage(BaseModelAdminMixin):
    pass


class AdminSocialMediaPlatforms(BaseModelAdminMixin):
    pass


class AdminSubscriptionType(BaseModelAdminMixin):
    pass


class AdminSubscription(BaseModelAdminMixin):
    pass


class AdminPackageService(BaseModelAdminMixin):
    pass


class AdminPackage(BaseModelAdminMixin):
    list_display = ("name", "price", "date_created")


admin.site.register(Category, AdminSkillCategory)
admin.site.register(Skill, AdminSkill)
admin.site.register(Country, AdminCountry)
admin.site.register(State, StateAdmin)
admin.site.register(City, AdminCity)
admin.site.register(UserLanguage, AdminUserLanguage)
admin.site.register(SocialMediaPlatforms, AdminSocialMediaPlatforms)
admin.site.register(PackageService, AdminPackageService)
admin.site.register(Package, AdminPackage)
