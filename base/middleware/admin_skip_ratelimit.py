from django.utils.deprecation import MiddlewareMixin
from django.conf import settings


class AdminSkipRateLimitMiddleware(MiddlewareMixin):
    def process_request(self, request):
        # If user is authenticated and is staff, set a flag to skip rate limiting
        user = getattr(request, "user", None)
        if user and user.is_authenticated and (user.is_staff or user.is_superuser):
            setattr(request, "skip_rate_limit", True)
        else:
            setattr(request, "skip_rate_limit", False)
