from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from django.conf import settings
from django.core.cache import cache
from rest_framework.authentication import SessionAuthentication, BasicAuthentication
from rest_framework_simplejwt.authentication import JWTAuthentication
import time


class GlobalRateLimitMiddleware(MiddlewareMixin):
    RATE_LIMIT = 1000  # requests
    TIME_WINDOW = 60 * 60  # 1 hour in seconds

    def process_request(self, request):
        # Remove admin exclusion from middleware, only apply to anonymous/public users
        if request.path.startswith("/api/"):
            ip = self.get_client_ip(request)
            key = f"global_rate_limit:{ip}"
            data = cache.get(key, {"count": 0, "start": time.time()})
            now = time.time()
            if now - data["start"] > self.TIME_WINDOW:
                data = {"count": 1, "start": now}
            else:
                data["count"] += 1
            cache.set(key, data, timeout=self.TIME_WINDOW)
            if data["count"] > self.RATE_LIMIT:
                setattr(request, "global_rate_limited", True)
                return JsonResponse(
                    {"error": "Too many requests (global limit)"}, status=429
                )
        return None

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip
