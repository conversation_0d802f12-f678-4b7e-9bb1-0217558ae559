# Generated by Django 4.2.11 on 2024-04-15 17:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BaseModel',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('slug', models.SlugField(unique=True)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('date_updated', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
            ],
            options={
                'verbose_name_plural': 'Skills Categories',
            },
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='Country',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('country_code', models.CharField(max_length=4, null=True, unique=True)),
            ],
            options={
                'verbose_name_plural': 'Countries',
            },
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='State',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('country_name', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='base.country')),
            ],
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='Skill',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('description', models.CharField(max_length=500, null=True)),
                ('category_name', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='base.category')),
            ],
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='City',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('state_name', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='base.state')),
            ],
            options={
                'verbose_name_plural': 'Cities',
            },
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='Address',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('street_address', models.CharField(max_length=255)),
                ('city_name', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='base.city')),
            ],
            bases=('base.basemodel',),
        ),
    ]
