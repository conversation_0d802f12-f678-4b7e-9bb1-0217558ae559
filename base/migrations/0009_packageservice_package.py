# Generated by Django 4.2.17 on 2025-04-03 18:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('base', '0008_remove_subscriptiontype_basemodel_ptr_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PackageService',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('description', models.TextField(blank=True, max_length=500)),
                ('is_active', models.BooleanField(default=True)),
            ],
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='Package',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('description', models.TextField(max_length=1000)),
                ('is_active', models.BooleanField(default=False)),
                ('pac_services', models.ManyToManyField(blank=True, related_name='service_packages', to='base.packageservice')),
            ],
            bases=('base.basemodel',),
        ),
    ]
