# Generated by Django 4.2.11 on 2024-04-16 07:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('base', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SocialMediaPlatforms',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('handle', models.CharField(max_length=255)),
                ('link', models.URLField(max_length=255)),
            ],
            bases=('base.basemodel',),
        ),
    ]
