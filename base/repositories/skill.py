from base.models import Skill
from django.db import transaction
from candidates.repositories.application_repository import RepositoryResponse


class SkillRepository:
    """Create a new skill"""

    def create_skill(self, data) -> RepositoryResponse:
        """
        Create a new skill for a candidate.
        """
        try:
            skill = Skill.objects.create(**data)
            return RepositoryResponse(
                success=True,
                data=skill,
                message="Skill created successfully",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False, data=None, message=f"Failed to create skill: {str(e)}"
            )

    """ get skill by id"""

    def get_skill_by_id(self, id) -> RepositoryResponse:
        """
        Get a skill by id.
        """
        try:
            skill = Skill.objects.get(pk=id)
            return RepositoryResponse(
                success=True,
                data=skill,
                message="Skill retrieved successfully",
            )
        except Skill.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Skill not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False, data=None, message=f"Failed to retrieve skill: {str(e)}"
            )

    """ Get skill by name"""

    @transaction.atomic
    def get_skill_by_name(self, name) -> RepositoryResponse:
        """
        Get a skill by name.
        """
        try:
            skill = Skill.objects.get(name=name)
            return RepositoryResponse(
                success=True,
                data=skill,
                message="Skill retrieved successfully",
            )
        except Skill.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Skill not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False, data=None, message=f"Failed to retrieve skill: {str(e)}"
            )

    """Update an skill"""

    @transaction.atomic
    def update_skill(self, id, data) -> RepositoryResponse:
        """
        Update an existing skill.
        """
        try:
            skill = Skill.objects.get(pk=id)
            for key, value in data.items():
                setattr(skill, key, value)
            skill.save()
            return RepositoryResponse(
                success=True,
                data=skill,
                message="Skill updated successfully",
            )
        except Skill.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Skill not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False, data=None, message=f"Failed to update skill: {str(e)}"
            )

    """get skill by category"""

    @transaction.atomic
    def get_skills_by_category(self, category) -> RepositoryResponse:
        """
        Get skills by category.
        """
        try:
            skills = Skill.objects.filter(category_name=category)
            return RepositoryResponse(
                success=True,
                data=skills,
                message="Skills retrieved successfully",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to retrieve skills by category: {str(e)}",
            )

    """Delete a skill"""

    @transaction.atomic
    def delete_skill(self, id) -> RepositoryResponse:
        """
        Delete an existing skill.
        """
        try:
            skill = Skill.objects.get(pk=id)
            skill.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Skill deleted successfully",
            )
        except Skill.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Skill not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False, data=None, message=f"Failed to delete skill: {str(e)}"
            )
