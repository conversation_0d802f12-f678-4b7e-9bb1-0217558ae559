from base.models import Category
from base.models import Skill
from django.db import transaction
from candidates.repositories.application_repository import RepositoryResponse


class CategoryRepository:
    """Create a new category"""
    @transaction.atomic
    def create_category(self, data) -> RepositoryResponse:
        """
        Create a new category.
        """
        try:
            category = Category.objects.create(**data)
            return RepositoryResponse(
                success=True,
                message="Category created successfully",
                data=category
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                message=str(e)
            )
    
    """Get a category by its id"""
    def get_category_by_id(self, category_id) -> RepositoryResponse:
        """
        Get a category by its id.
        """
        try:
            category = Category.objects.get(pk=category_id)
            if category:
                return RepositoryResponse(
                    success=True,
                    data=category,
                    message="Category retrieved successfully"
                )
            else:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="Category not found"
                )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=str(e)
            )
    
    """Get category by its name"""
    def get_category_by_name(self, name) -> RepositoryResponse:
        """
        Get a category by its name.
        """
        try:
            category = Category.objects.get(name=name)
            if not category:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="Category not found"
                )
            return RepositoryResponse(
                success=True,
                data=category,
                message="Category retrieved successfully"
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=str(e)
            )
    
    """List all categories"""
    def list_categories(self) -> RepositoryResponse:
        """
        Get all categories.
        """
        try:
            categories = Category.objects.all()
            return RepositoryResponse(
                success=True,
                data=categories,
                message="All categories retrieved successfully"
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=str(e)
            )
    
    """Update an existing category"""
    @transaction.atomic
    def update_category(self, category_id, data) -> RepositoryResponse:
        """
        Update an existing category.
        """
        try:
            category = Category.objects.get(pk=category_id)
            for key, value in data.items():
                setattr(category, key, value)
            return RepositoryResponse(
                success=True,
                data=category,
                message="Category updated successfully"
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=str(e)
            )
    
    """Delete a category"""
    @transaction.atomic
    def delete_category(self, category_id) -> RepositoryResponse:
        """
        Delete an existing category.
        """
        try:
            category = Category.objects.get(pk=category_id)
            category.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Category deleted successfully"
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=str(e)
            )