from typing import List, Optional
from django.db.models import QuerySet

from base.services.logging import LoggingService
from candidates.models import Interview
from candidates.repositories.application_repository import RepositoryResponse

logging_service = LoggingService()


class InterviewRepository:

    def get_interviews_by_user(self, user_id) -> RepositoryResponse:
        try:
            interviews = Interview.objects.filter(user_id=user_id)
            return RepositoryResponse(
                success=True,
                data=interviews,
                message="Interviews successfully retrieved",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Failed to retrieve interviews by user",
                data=None,
            )

    def get_interviews_by_company(self, company_id) -> RepositoryResponse:
        try:
            interviews = Interview.objects.filter(business_id=company_id)
            return RepositoryResponse(
                success=True,
                data=interviews,
                message="Interviews successfully retrieved",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Failed to retrieve interviews by company",
                data=None,
            )

    def get_interview_by_id(self, interview_id: int) -> RepositoryResponse:
        try:
            interview = Interview.objects.filter(id=interview_id).first()
            return RepositoryResponse(
                success=interview is not None,
                data=interview,
                message="Interview successfully retrieved",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Failed to retrieve interview by id",
                data=None,
            )

    def create_interview(self, **kwargs) -> RepositoryResponse:
        try:
            interview = Interview.objects.create(**kwargs)
            return RepositoryResponse(
                success=True,
                data=interview,
                message="Interview successfully created",
            )
        except Interview.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Interview not found",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Failed to create interview",
                data=None,
            )

    def update_interview(self, interview_id: int, **kwargs) -> RepositoryResponse:
        try:
            interview = self.get_interview_by_id(interview_id)
            if not interview.success:
                return RepositoryResponse(
                    success=False,
                    message="Interview not found",
                    data=None,
                )
            for key, value in kwargs.items():
                setattr(interview.data, key, value)
            interview.data.save()
            return RepositoryResponse(
                success=True,
                data=interview.data,
                message="Interview successfully updated",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Failed to update interview",
                data=None,
            )

    def delete_interview(self, interview_id: int) -> RepositoryResponse:
        try:
            interview = self.get_interview_by_id(interview_id)
            if not interview.success:
                return RepositoryResponse(
                    success=False,
                    message="Interview not found",
                    data=None,
                )
            interview.data.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Interview successfully deleted",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Failed to delete interview",
                data=None,
            )
