from base.models import UserLanguage
from django.db import transaction
from candidates.repositories.application_repository import RepositoryResponse


class UserLanguageRepository:
    """Create a new user language"""
    @transaction.atomic
    def create_user_language(self, data) -> RepositoryResponse:
        """
        Create a new user language.
        """
        try:
            user_language = UserLanguage.objects.create(**data)
            return RepositoryResponse(
                success=True,
                data=user_language,
                message="User language created successfully",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to create user language: {str(e)}"
            )
    
    """ get user language by id"""
    def get_user_language_by_id(self, id) -> RepositoryResponse:
        """
        Get a user language by id.
        """
        try:
            user_language = UserLanguage.objects.get(pk=id)
            return RepositoryResponse(
                success=True,
                data=user_language,
                message="User language retrieved successfully",
            )
        except UserLanguage.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="User language not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to retrieve user language: {str(e)}"
            )
    
    """Update a user language"""
    @transaction.atomic
    def update_user_language(self, id, data) -> RepositoryResponse:
        """
        Update an existing user language.
        """
        try:
            user_language = UserLanguage.objects.get(pk=id)
            for key, value in data.items():
                setattr(user_language, key, value)
            user_language.save()
            return RepositoryResponse(
                success=True,
                data=user_language,
                message="User language updated successfully",
            )
        except UserLanguage.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="User language not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to update user language: {str(e)}"
            )
    
    """Delete a user language"""
    @transaction.atomic
    def delete_user_language(self, id) -> RepositoryResponse:
        """
        Delete an existing user language.
        """
        try:
            user_language = UserLanguage.objects.get(pk=id)
            user_language.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="User language deleted successfully",
            )
        except UserLanguage.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="User language not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to delete user language: {str(e)}"
            )