from base.models import SocialMediaPlatforms
from django.db import transaction
from candidates.repositories.application_repository import RepositoryResponse


class SocialMediaPlatformsRepository:
    """Create a new social media platform"""
    @transaction.atomic
    def create_social_media_platform(self, data) -> RepositoryResponse:
        """
        Create a new social media platform for a candidate.
        """
        try:
            social_media_platform = SocialMediaPlatforms.objects.create(**data)
            return RepositoryResponse(
                success=True,
                data=social_media_platform,
                message="Social media platform created successfully",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to create social media platform: {str(e)}"
            )
    
    """ get social media platform by id"""
    @transaction.atomic
    def get_social_media_platform_by_id(self, id) -> RepositoryResponse:
        """
        Get a social media platform by id.
        """
        try:
            social_media_platform = SocialMediaPlatforms.objects.get(pk=id)
            return RepositoryResponse(
                success=True,
                data=social_media_platform,
                message="Social media platform retrieved successfully",
            )
        except SocialMediaPlatforms.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Social media platform not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to retrieve social media platform: {str(e)}"
            )
    
    """Update a social media platform"""
    @transaction.atomic
    def update_social_media_platform(self, id, data) -> RepositoryResponse:
        """
        Update an existing social media platform.
        """
        try:
            social_media_platform = SocialMediaPlatforms.objects.get(pk=id)
            for key, value in data.items():
                setattr(social_media_platform, key, value)
            social_media_platform.save()
            return RepositoryResponse(
                success=True,
                data=social_media_platform,
                message="Social media platform updated successfully",
            )
        except SocialMediaPlatforms.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Social media platform not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to update social media platform: {str(e)}"
            )
    
    """Delete a social media platform"""
    @transaction.atomic
    def delete_social_media_platform(self, id) -> RepositoryResponse:
        """
        Delete an existing social media platform.
        """
        try:
            social_media_platform = SocialMediaPlatforms.objects.get(pk=id)
            social_media_platform.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Social media platform deleted successfully",
            )
        except SocialMediaPlatforms.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Social media platform not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to delete social media platform: {str(e)}"
            )