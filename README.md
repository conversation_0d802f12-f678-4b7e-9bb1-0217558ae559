# JobMatch Backend API

JobMatch is a robust platform designed to seamlessly connect businesses with job seekers. The backend API enables businesses to register, manage profiles, post jobs, and handle applications, while candidates can create profiles and apply for jobs. All endpoints are secured with JWT-based authentication and follow best practices for security and scalability.

---

## Table of Contents

- [Architecture](#architecture)
- [Tech Stack](#tech-stack)
- [Features](#features)
- [API Structure](#api-structure)
- [Getting Started](#getting-started)
- [Testing](#testing)
- [Deployment](#deployment)
- [Contributing](#contributing)
- [License](#license)

---

## Architecture

### API Gateway

This backend is built with Python and Django, following a singleton design and N-Tier application pattern. Responsibilities are separated as follows:

- **Repository Classes:** Handle database queries.
- **Service Classes:** Contain business logic.
- **API App:** Houses API views (primarily function-based, with some class-based views).

## Tech Stack

- **Django:** Web framework
- **Django REST Framework:** API development
- **JWT:** Authentication
- **Django User Roles & Permissions:** Authorization
- **Custom Permissions Model:** Fine-grained access control
- **Custom Rate Limiting:** API abuse protection
- **Cloudflare:** DDoS protection & bot detection
- **Postmark:** Email service
- **Render:** Hosting
- **Redis:** Caching & read database
- **PostgreSQL:** Primary database

## Features

- Unified Data Class returned by repositories and services
- Consistent DRF Response objects from endpoints
- Modular, maintainable codebase
- Secure authentication and authorization
- Rate limiting and abuse protection
- Scalable deployment architecture

## API Structure

- **POST, GET:** POST to add an instance to a collection; GET to list instances
- **POST, GET, DELETE, UPDATE, PUT:** Standard RESTful operations for resource management
- **Endpoints:** Organized by domain (accounts, businesses, candidates, jobs, etc.)

## Getting Started

### Prerequisites

- Python 3.10+
- pip
- PostgreSQL (or SQLite for development)
- Redis Insights

### Installation

1. Clone the repository:
      ```bash
      git clone <repo-url>
      cd csr-job-macth-backend
      ```
2. Install dependencies:
      ```bash
      pip install -r requirements.txt
      ```
3. Apply migrations:
      ```bash
      python manage.py migrate
      ```
4. Create a superuser:
      ```bash
      python manage.py createsuperuser
      ```
5. Run the development server:
      ```bash
      python manage.py runserver
      ```

## Testing

Run tests using:

```bash
python manage.py test
```

## Environment Variables

Create a `.env` file in the `core/` directory with the following variables:

```
SECRET_KEY=
# Database credentials
DB_NAME=
DB_USERNAME=
DB_PASSWORD=
DB_HOST=
DB_PORT=

# Storage (DigitalOcean Spaces or S3)
USE_STORAGE=
BUCKET_TOKEN=
BUCKET_SECRET_KEY=
BUCKET_ACCESS_KEY=
BUCKET_URL=

# Email server
EMAIL_SERVER_TOKEN=

# Domain names
LIVE_DOMAIN_NAME=
DEV_DOMAIN_NAME=
MAIN_EMAIL=

# Working environment: dev | test | production
ENVIRONMENT=
ALLOWED_HOSTS=

# CORS
CORS_ALLOWED_ORIGIN_REGEXES=
ORIGIN_LOCAL=
ORIGIN_TEST=
ORIGIN_PRODUCTION=
ORIGIN_BACKEND=

# Stripe API
STRIPE_PUBLIC_KEY=
STRIPE_SECRET_KEY=

# Redis configuration
REDIS_PUBLIC_ENDPOINT=
REDIS_PASSWORD=
REDIS_PORT=
REDIS_DATABASE_NAME=
```

> **Note:** Never commit your actual `.env` file to version control.

## Deployment

- Configure environment variables for production
- Use `gunicorn` or `uvicorn` for production WSGI/ASGI serving
- Set up Redis and PostgreSQL in production
- Deploy on Render or your preferred cloud provider

## Contributing

Contributions are welcome! Please reach out to CSR limited at csrlimited.com

## License

This project is the property of CSR Limited. All rights reserved.
