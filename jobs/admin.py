from django.contrib import admin
from .models import *
from base.admin import BaseModelAdminMixin


# Register your models here.
@admin.register(Benefit)
class AdminJobBenefit(BaseModelAdminMixin):
    pass


@admin.register(JobType)
class AdminJobType(BaseModelAdminMixin):
    pass


@admin.register(WorkType)
class AdminWorkType(BaseModelAdminMixin):
    pass


@admin.register(ExperienceLevel)
class AdminWorExperienceLevel(BaseModelAdminMixin):
    pass


@admin.register(Job)
class AdminJob(BaseModelAdminMixin):
    list_display = (
        "name",
        "company_name",
        "job_type",
        "work_type",
        "min_salary",
        "max_salary",
    )


class AdminJobSkillsCategory(BaseModelAdminMixin):
    pass


class AdminJobSkills(BaseModelAdminMixin):
    pass


admin.site.register(JobSkillsCategory, AdminJobSkillsCategory)
admin.site.register(<PERSON>S<PERSON>, AdminJobSkills)
