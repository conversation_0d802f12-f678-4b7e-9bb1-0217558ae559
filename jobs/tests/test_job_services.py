from django.test import TestCase
from django.core.files.uploadedfile import SimpleUploadedFile
from django.contrib.auth.models import User

from businesses.models import Company
from jobs.models import (
    Job,
    JobType,
    WorkType,
    JobSkill,
    JobSkillsCategory,
    Benefit,
    ExperienceLevel,
)
from jobs.services.job_service import JobService


class JobServiceTest(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create user
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )

        # Create company
        self.company = Company.objects.create(
            name="Test Company", email="<EMAIL>", created_by=self.user
        )

        # Create job types and work types
        self.job_type = JobType.objects.create(name="Full-time")
        self.work_type = WorkType.objects.create(name="Remote")

        # Create experience level
        self.experience_level = ExperienceLevel.objects.create(name="Senior")

        # Create skill category and skills
        self.skill_category = JobSkillsCategory.objects.create(name="Programming")
        self.skill1 = JobSkill.objects.create(
            name="Python", category_name=self.skill_category
        )
        self.skill2 = JobSkill.objects.create(
            name="Django", category_name=self.skill_category
        )

        # Create benefits
        self.benefit1 = Benefit.objects.create(
            name="Health Insurance",
            description="Full coverage",
            icon=SimpleUploadedFile(
                "test_icon.png", b"file_content", content_type="image/png"
            ),
        )
        self.benefit2 = Benefit.objects.create(
            name="401k", description="Company matching"
        )

        # Initialize service
        self.job_service = JobService()

        # Basic job data
        self.job_data = {
            "location": "New York",
            "job_type": "Full-time",
            "work_type": "Remote",
            "experience_level": "Senior",
            "min_salary": 80000,
            "max_salary": 120000,
            "job_description": "Test job description",
            "responsibilities": "Test responsibilities",
            "qualifications": "Test qualifications",
            "nice_to_have": "Test nice to have",
        }

    def test_create_job_success(self):
        """Test successful job creation"""
        response = self.job_service.create_job(
            user=self.user,
            company_slug=self.company.slug,
            data=self.job_data,
        )

        self.assertTrue(response.success)
        self.assertEqual(response.message, "Job created successfully")

        # Verify job was created with correct data
        job = response.data
        self.assertEqual(job.company_name, self.company)
        self.assertEqual(job.location, "New York")
        self.assertEqual(job.min_salary, 80000)
        self.assertEqual(job.max_salary, 120000)

        # Verify related objects
        self.assertEqual(job.job_type, "Full-time")
        self.assertEqual(job.work_type, "Remote")
        self.assertEqual(job.experience_level, "Senior")

    def test_create_job_missing_required_fields(self):
        """Test job creation with missing required fields"""
        invalid_data = self.job_data.copy()
        invalid_data.pop("location")
        invalid_data.pop("job_type")

        response = self.job_service.create_job(
            user=self.user,
            company_slug=self.company.slug,
            data=invalid_data,
        )
        self.assertFalse(response.success)

    def test_get_job(self):
        """Test retrieving a job"""
        # First create a job
        create_response = self.job_service.create_job(
            user=self.user, company_slug=self.company.slug, data=self.job_data
        )
        job_slug = create_response.data.slug

        # Then try to retrieve it
        response = self.job_service.get_job(job_slug)

        self.assertTrue(response.success)
        self.assertEqual(response.data.slug, job_slug)

    def test_update_job(self):
        """Test updating a job"""
        # First create a job
        create_response = self.job_service.create_job(
            user=self.user,
            company_slug=self.company.slug,
            data=self.job_data,
        )
        job_slug = create_response.data.slug

        # Update data
        update_data = {
            "location": "Remote",
            "min_salary": 90000,
            "max_salary": 130000,
            "required_skills": [
                {"name": "Python", "category": "Programming"},
                {"name": "Django", "category": "Frameworks"},
            ],
            "benefits": [
                {"name": "Gym Membership", "description": "Full access"},
                {"name": "Free Lunch", "description": "Daily allowance"},
            ],
        }

        response = self.job_service.update_job(
            user=self.user,
            job_slug=job_slug,
            data=update_data,
        )

        self.assertTrue(response.success)
        self.assertEqual(response.data.location, "Remote")
        self.assertEqual(response.data.min_salary, 90000)
        self.assertEqual(response.data.max_salary, 130000)
        self.assertEqual(response.data.required_skills.count(), 2)

    def test_delete_job(self):
        """Test deleting a job"""
        # First create a job
        create_response = self.job_service.create_job(
            user=self.user,
            company_slug=self.company.slug,
            data=self.job_data,
        )
        slug = create_response.data.slug

        # Then delete it
        response = self.job_service.delete_job(self.user, job_slug=slug)

        self.assertTrue(response.success)
        self.assertEqual(Job.objects.filter(slug=slug).count(), 0)

    def test_manage_applicants(self):
        """Test managing job applicants"""
        # Create a job
        create_response = self.job_service.create_job(self.user, self.company.slug, self.job_data)
        job = create_response.data

        # Initial applicants should be 0
        self.assertEqual(job.applicants, 0)

        # Increment applicants
        response = self.job_service._manage_applicants(job)

        self.assertTrue(response.success)
        self.assertEqual(response.data.applicants, 1)

    def test_manage_statuses(self):
        """Test managing job status"""
        # Create a job
        create_response = self.job_service.create_job(self.user, self.company.slug, self.job_data)
        job = create_response.data

        # Test valid status change
        response = self.job_service._manage_statuses(job, "closed")

        self.assertTrue(response.success)
        self.assertEqual(response.data.status, "closed")

        # Test invalid status
        response = self.job_service._manage_statuses(job, "invalid_status")

        self.assertFalse(response.success)
        self.assertEqual(response.message, "Invalid status value")

    def test_add_skills(self):
        """Test adding skills to a job"""
        # Create a job
        create_response = self.job_service.create_job(self.user, self.company.slug, self.job_data)
        job = create_response.data

        # Add new skills
        new_skills = [
            {"name": "JavaScript", "category": "Programming"},
            {"name": "React", "category": "Programming"},
        ]

        response = self.job_service._add_skills(job, new_skills)

        self.assertTrue(response.success)

    def test_update_benefits(self):
        """Test updating benefits for a job"""
        # Create a job
        create_response = self.job_service.create_job(self.user, self.company.slug, self.job_data)
        job = create_response.data

        # Update benefits
        new_benefits = [
            {"name": "Gym Membership", "description": "Full access"},
            {"name": "Free Lunch", "description": "Daily allowance"},
        ]

        response = self.job_service._update_benefits(job, new_benefits)

        self.assertTrue(response.success)
        self.assertEqual(response.data.benefits.count(), 2)
        self.assertTrue(response.data.benefits.filter(name="Gym Membership").exists())

    def test_get_jobs(self):
        # Test basic pagination
        response = self.job_service.get_jobs_list(page=1, page_size=3)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data["jobs"])
        self.assertEqual(response.data["total_pages"], 1)
    
    def test_get_job_types(self):
        response = self.job_service.get_job_types()
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)


    def test_get_work_types(self):
        response = self.job_service.get_work_types()
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

    def test_get_experience_levels(self):
        response = self.job_service.get_experience_levels()
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
    
    def test_get_locations(self):
        response = self.job_service.get_locations()
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
