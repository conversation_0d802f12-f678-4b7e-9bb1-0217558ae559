from base.tests.setup import BaseSetup
from jobs.factory import JobFactory
from jobs.services.admin import JobsAdminService
from businesses.factory import CompanyFactory
from rest_framework import status

service = JobsAdminService()

class TestAdminJobService(BaseSetup):
    def setUp(self):
        super().setUp()
        self.company = CompanyFactory(created_by=self.admin)
        self.job_data = {
            "name": "Software Engineer",
            "min_salary": 80000,
            "max_salary": 120000,
            "location": "Remote",
            "job_description": "Updated job description",
            "work_type": "Full-time",
            "job_type": "Full-time",
        }
        self.job = JobFactory(
            company_name=self.company,
            created_by=self.admin,
            **self.job_data
        )

    def test_get_all_jobs(self):
        """Test retrieving all jobs with pagination and filters"""
        [JobFactory() for _ in range(3)]
        
        response = service.get_all_jobs(
            user=self.admin,
            page=1,
            page_size=10,
            search_query="Software",
        )
        print(f"test get all jobs: {response}")
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertIn("jobs", response.data)

    def test_get_all_jobs_error(self):
        """Test error handling in get_all_jobs"""
        response = service.get_all_jobs(
            user=self.admin,
            page="invalid",
        )
        
        self.assertFalse(response.success)
        self.assertEqual(response.status, status.HTTP_400_BAD_REQUEST)

    def test_update_job(self):
        """Test updating a job"""
        update_data = {
            "name": "Senior Software Engineer",
            "min_salary": 100000,
            "max_salary": 150000,
        }
        
        response = service.update_job(self.admin, self.job.slug, update_data)
        
        self.assertTrue(response.success)
        self.assertEqual(response.data['name'], "Senior Software Engineer")
        self.assertEqual(response.data['min_salary'], '100000.00')

    def test_update_job_invalid_slug(self):
        """Test updating a job with invalid slug"""
        update_data = {"name": "Updated Job"}
        
        response = service.update_job(self.admin, "invalid-slug", update_data)
        
        self.assertFalse(response.success)

    def test_delete_job(self):
        """Test deleting a job"""
        response = service.delete_job(self.admin, self.job.slug)
        
        self.assertTrue(response.success)
        self.assertIsNone(response.data)

    def test_delete_job_invalid_slug(self):
        """Test deleting a job with invalid slug"""
        response = service.delete_job(self.admin, "invalid-slug")
        
        self.assertFalse(response.success)

    def test_create_job(self):
        """Test creating a new job"""
        response = service.create_job(
            user=self.admin,
            business_slug=self.company.slug,
            data=self.job_data
        )
        print(f"test create job: {response}")
        
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data["name"], "Software Engineer")

    def test_create_job_invalid_business(self):
        """Test creating a job with invalid business slug"""
        response = service.create_job(
            user=self.admin,
            business_slug="invalid-business",
            data=self.job_data
        )
        
        self.assertFalse(response.success)

    def test_get_job(self):
        """Test retrieving a specific job"""
        response = service.get_job(self.admin, self.job.slug)
        
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data['name'], "Software Engineer")

    def test_get_job_invalid_slug(self):
        """Test retrieving a job with invalid slug"""
        response = service.get_job(self.admin, "invalid-slug")
        
        self.assertFalse(response.success)

    def test_get_jobs_by_business(self):
        """Test retrieving jobs for a specific business"""
        [JobFactory(
            company_name=self.company,
            created_by=self.user
        ) for _ in range(2)]
        
        response = service.get_jobs_by_business(self.user, self.company.slug)
        
        self.assertTrue(response.success)
        self.assertEqual(response.message, "Jobs retrieved successfully")
        self.assertEqual(response.status, status.HTTP_200_OK)
        self.assertIsNotNone(response.data)
        # Should return 3 jobs (1 from setUp + 2 created here)
        self.assertEqual(len(response.data), 3)

    def test_get_jobs_by_business_invalid_slug(self):
        """Test retrieving jobs for an invalid business slug"""
        response = service.get_jobs_by_business(self.admin, "invalid-business-slug")
        
        self.assertFalse(response.success)
        self.assertEqual(response.status, status.HTTP_404_NOT_FOUND)
        self.assertIsNone(response.data)

    def test_get_jobs_by_business_no_jobs(self):
        """Test retrieving jobs for a business with no jobs"""
        # Create a new company without jobs
        empty_company = CompanyFactory(created_by=self.admin)
        
        response = service.get_jobs_by_business(self.admin, empty_company.slug)
        
        self.assertTrue(response.success)
        self.assertEqual(response.message, "Jobs retrieved successfully")
        self.assertEqual(response.status, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)
    
    def test_update_job_status(self):
        """Test updating a job status"""
        update_data = {"status": "draft"}
        
        response = service.update_job_status(self.admin, self.job.slug, update_data)
        
        self.assertTrue(response.success)
        self.assertEqual(response.data['status'], "draft")
