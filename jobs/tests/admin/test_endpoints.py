from django.urls import reverse
from rest_framework import status
from base.tests.setup import BaseSetup
from jobs.factory import JobFactory


class TestAdminJobEndpoints(BaseSetup):
    def setUp(self):
        super().setUp()
        self.job = JobFactory(
            company_name=self.company,
            created_by=self.admin
        )

        self.job_data = {
            "name": "Software Developer",
            "location": "Remote",
            "min_salary": 50000.00,
            "max_salary": 100000.00,
            "job_description": "Test job description",
            "work_type": "Full-time",
            "experience_level": "Senior",
            "job_type": "Full-time",
        }

    def test_admin_jobs_list(self):
        """Test getting list of jobs for a business"""
        self._authenticate_user(self.admin)

        url = reverse('admin_jobs_api_view')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_admin_create_job(self):
        """Test creating a new job"""
        self.job_data["business_slug"] = self.company.slug
        self._authenticate_user(self.admin)

        url = reverse('admin_jobs_api_view')
        response = self.client.post(url, self.job_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_admin_get_job_details(self):
        """Test getting details of a specific job"""
        self._authenticate_user(self.admin)

        url = reverse('admin_job_detail_api_view', kwargs={'job_slug': self.job.slug})
        response = self.client.get(url)
        print(f"response in test_admin_get_job_details: {response}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_admin_update_job(self):
        """Test updating a job"""
        self._authenticate_user(self.admin)

        update_data = {**self.job_data, "name": "Senior Software Developer"}
        url = reverse('admin_job_detail_api_view', kwargs={'job_slug': self.job.slug})
        response = self.client.put(url, update_data, format='json')
        print(f"response in test_admin_update_job: {response}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_admin_patch_job(self):
        """Test partially updating a job"""
        self._authenticate_user(self.admin)

        patch_data = {"status": "published"}
        url = reverse('admin_job_detail_api_view', kwargs={'job_slug': self.job.slug})
        response = self.client.patch(url, patch_data, format='json')
        print(f"response in test_admin_patch_job: {response}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_admin_delete_job(self):
        """Test deleting a job"""
        self._authenticate_user(self.admin)

        url = reverse('admin_job_detail_api_view', kwargs={'job_slug': self.job.slug})
        response = self.client.delete(url)
        print(f"response in test_admin_delete_job: {response}")

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

    def test_unauthorized_access(self):
        """Test unauthorized access to admin endpoints"""
        url = reverse('admin_jobs_api_view')
        response = self.client.get(url)
        print(f"response in test_unauthorized_access: {response}")

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_invalid_job_slug(self):
        """Test accessing endpoints with invalid job slug"""
        self._authenticate_user(self.admin)

        url = reverse('admin_job_detail_api_view', kwargs={'job_slug': 'invalid-job-slug'})
        response = self.client.get(url)
        print(f"response in test_invalid_job_slug: {response}")

        self.assertNotEqual(response.status_code, status.HTTP_200_OK)
