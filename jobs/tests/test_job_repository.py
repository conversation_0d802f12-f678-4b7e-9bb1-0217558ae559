from django.test import TestCase
from businesses.models import Company
from jobs.factory import JobFactory
from jobs.models import Job
from jobs.repositories.job_repository import JobRepository
from django.contrib.auth.models import User


class JobRepositoryTest(TestCase):
    def setUp(self):
        """ Testing data """
        self.job_repository = JobRepository()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )

        # Create company
        self.company = Company.objects.create(
            name="Test Company", email="<EMAIL>", created_by=self.user
        )
        self.job_data = {
            "name": "Software Developer",
            "location": "Remote",
            "min_salary": 50000,
            "max_salary": 100000,
            "job_description": "Develop software applications.",
            "responsibilities": "Work on projects, fix bugs, and implement new features.",
            "qualifications": "Bachelor's degree in Computer Science or Software Engineering, 5+ years of experience.",
            "nice_to_have": "Experience with version control systems and strong communication skills.",
        }
    
    def test_create_job(self):
        """ Create a new job """
        response = self.job_repository.create_job(self.user, self.company, self.job_data)
        
        if not response.success:
            print(f"Error message: {response.message}")
    
        self.assertIsNotNone(response.data)
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, "Software Developer")
        self.assertEqual(response.data.location, "Remote")
    
    def test_get_job(self):
        """ Get a job by id """
        created_job = JobFactory(name="Software Developer")
        response = self.job_repository.get_job(created_job.slug)
        
        if not response.success:
            print(f"Error message: {response.message}")
        
        self.assertIsNotNone(response.data)
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, "Software Developer")
        self.assertEqual(response.data.id, created_job.id)
    
    def test_get_nonexistent_job(self):
        """ Get a nonexistent job """
        response = self.job_repository.get_job(999)
        
        self.assertFalse(response.success)
        self.assertIsNone(response.data)
    
    def test_update_job(self):
        """ Update an existing job """
        created_job = JobFactory()
        updated_job_data = {
            "name": "Updated Software Developer",
            "min_salary": 60000,
            "max_salary": 110000,
        }
        print("\n :")
        print(created_job)
        print("\n")
        response = self.job_repository.update_job(created_job.slug, updated_job_data)
        
        if not response.success:
            print(f"Error message: {response.message}")
        
        self.assertIsNotNone(response.data)
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, "Updated Software Developer")
    
    def test_update_nonexistent_job(self):
        """ Update a nonexistent job """
        updated_job_data = {
            "name": "Updated Software Developer",
            "min_salary": 60000,
            "max_salary": 110000,
        }
        response = self.job_repository.update_job(999, updated_job_data)
        
        self.assertFalse(response.success)
        self.assertIsNone(response.data)
    
    def test_delete_job(self):
        """ Delete a job """
        created_job = JobFactory()
        response = self.job_repository.delete_job(created_job.slug)
        
        if not response.success:
            print(f"Error message: {response.message}")
        
        self.assertTrue(response.success)
    
    def test_delete_nonexistent_job(self):
        """ Delete a nonexistent job """
        response = self.job_repository.delete_job(999)
        
        self.assertFalse(response.success)
        self.assertIsNone(response.data)
        
    

        