from django.test import TestCase
from django.contrib.auth.models import User
from django.urls import reverse
from businesses.models import Company
from jobs.models import Job
from jobs.services.job_service import JobService
from datetime import datetime, timedelta
import jwt
from core.settings import SECRET_KEY
from rest_framework.test import APITestCase, APIClient


class JobEndpointTestCase(TestCase):
    def setUp(self):
        self.job_service = JobService()

        # test user
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="password123",
        )

        # test company
        self.company = Company.objects.create(
            name="XYZ Corp",
            created_by=self.user,
            email="<EMAIL>",
        )
        # complete data
        self.base_data = {
            "name": "Software Developer",
            "location": "Remote",
            "min_salary": 50000.00,
            "max_salary": 100000.00,
            "job_description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
            "work_type": "Full-time",
            "experience_level": "Senior",
            "job_type": "Full-time",
        }
        # incomplete data
        self.incomplete_data = {
            "location": "",
            "min_salary": "",
            "job_description": "",
            "work_type": "",
            "experience_level": "",
            "job_type": "",
        }
        self.related_fields = {
            "skills": ["Python", "Django"],
            "benefits": ["Health Insurance", "401k"],
        }

        # generate a JWT token

        token_payload = {
            "user_id": self.user.id,
            "exp": datetime.now() + timedelta(days=1),
        }
        jwt_token = jwt.encode(token_payload, SECRET_KEY, algorithm="HS256")

        # initialize client
        self.client = APIClient()

        # authenticate client
        self.client.force_authenticate(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {jwt_token}")

        # endpoint
        self.endpoint = "/api/jobs"

    # create a job successfully
    def test_create_job(self):
        data = {**self.base_data}
        response = self.client.post(
            f"{self.endpoint}/{self.company.slug}/new/",
            data,
            format="json",
        )
        self.assertEqual(response.status_code, 201)

    # create a job failed
    def test_create_job_failed(self):
        data = {**self.incomplete_data}
        response = self.client.post(
            f"{self.endpoint}/{self.company.slug}/new/",
            data,
            format="json",
        )
        self.assertEqual(response.status_code, 400)

    # get a job successfully
    def test_get_job(self):
        job = Job.objects.create(
            **self.base_data,
            company_name=self.company,
            created_by=self.user,
        )
        response = self.client.get(f"{self.endpoint}/{job.slug}/")
        self.assertEqual(response.status_code, 200)

    # get a job failed
    def test_get_job_failed(self):
        response = self.client.get(f"{self.endpoint}/non-existent-job-slug/")
        self.assertEqual(response.status_code, 404)

    # test get_jobs list successfully
    def test_get_jobs_list(self):
        # jobs = [
        #     Job.objects.create(
        #         **self.base_data,
        #         company_name=self.company,
        #         created_by=self.user,
        #     )
        #     for _ in range(3)
        # ]
        response = self.client.get(f"{self.endpoint}/")
        self.assertEqual(response.status_code, 200)
        # self.assertEqual(len(response.data), 3)

    # update a job successfully
    def test_update_job(self):
        job = Job.objects.create(
            **self.base_data,
            company_name=self.company,
            created_by=self.user,
        )
        data = {**self.base_data, "name": "Senior Software Developer"}
        response = self.client.patch(
            f"{self.endpoint}/{job.slug}/update/", data, format="json"
        )
        self.assertEqual(response.status_code, 200)

    def test_update_required_skills(self):
        job = Job.objects.create(
            **self.base_data,
            company_name=self.company,
            created_by=self.user,
        )
        data = {
            **self.related_fields,
            "required_skills": [
                {"name": "Python", "category": "Programming"},
                {"name": "Django", "category": "Frameworks"},
            ],
        }
        response = self.client.patch(
            f"{self.endpoint}/{job.slug}/update/", data, format="json"
        )
        self.assertEqual(response.status_code, 200)

    def update_benefits(self):
        job = Job.objects.create(
            **self.base_data,
            company_name=self.company,
            created_by=self.user,
        )
        data = {
            **self.related_fields,
            "benefits": [
                {"name": "Gym Membership", "description": "Full access"},
                {"name": "Free Lunch", "description": "Daily allowance"},
            ],
        }
        response = self.client.patch(
            f"{self.endpoint}/{job.slug}/update/", data, format="json"
        )
        self.assertEqual(response.status_code, 200)

    # delete job
    def test_delete_job(self):
        job = Job.objects.create(
            **self.base_data,
            company_name=self.company,
            created_by=self.user,
        )
        url = reverse("delete_job_api", kwargs={"business_slug": self.company.slug, "job_slug": job.slug})
        response = self.client.delete(url)
        print("Endpoint is :", url)
        print("Response: ", response)
        self.assertEqual(response.status_code, 204)
    
    def test_get_job_types_api(self):
        url = reverse("get_work_types_api")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_get_work_types_api(self):
        url = reverse("get_experience_levels_api")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_get_experience_levels_api(self):
        url = reverse("get_job_types_api")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
    
    def test_get_locations_api(self):
        url = reverse("get_locations_api")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
