import logging
from typing import Any, Dict
from base import serializers
from base.services.logging import LoggingService
from base.utils.status_checker import APIResponse
from businesses.services.business import BusinessService
from businesses.views.serializers import GetJobSerializer
from jobs.models import Benefit, Job, JobSkill
from jobs.services.job_service import JobService
from rest_framework import status
from django.contrib.auth.models import User
from django.core.paginator import Paginator, EmptyPage
from django.db.models import Q, Prefetch


job_service = JobService()
company_service = BusinessService()
logging_service = LoggingService()


class JobsAdminService:
    def get_all_jobs(
        self,
        user,
        page: int = 1,
        page_size: int = 10,
        filters: Dict[str, Any] = {},
        search_query: str = None,
        order_by: str = "-date_created",
    ) -> APIResponse:
        try:
            filter_conditions = Q()

            # Handle filters
            if filters:
                # Job Type
                if job_type := filters.get("job_type", []):  # Changed from job_types
                    filter_conditions &= Q(job_type__in=job_type)

                # Experience Level
                if experience_level := filters.get("experience_level", []):
                    filter_conditions &= Q(experience_level__in=experience_level)

                # Job Industry
                if job_industry := filters.get("job_industry", []):
                    filter_conditions &= Q(job_industry__in=job_industry)

                # Work Type filter
                if work_types := filters.get("work_types", []):
                    filter_conditions &= Q(work_type__in=work_types)
                # Salary Range
                if min_salary := filters.get("min_salary"):
                    filter_conditions &= Q(salary__gte=min_salary)
                if max_salary := filters.get("max_salary"):
                    filter_conditions &= Q(salary__lte=max_salary)

                # Location
                if location := filters.get("location"):
                    filter_conditions &= Q(Q(location__icontains=location))

            # Search functionality
            if search_query:
                search_conditions = Q()
                search_terms = search_query.split()

                for term in search_terms:
                    term_condition = (
                        Q(name__icontains=term)
                        | Q(job_description__icontains=term)
                        | Q(company_name__name__icontains=term)
                        | Q(location__icontains=term)
                        | Q(required_skills__name__icontains=term)
                    )
                    search_conditions &= term_condition

                filter_conditions &= search_conditions

            # Query with optimization
            queryset = (
                Job.objects.select_related("company_name", "created_by")
                .prefetch_related(
                    Prefetch("required_skills", queryset=JobSkill.objects.only("name")),
                    Prefetch("benefits", queryset=Benefit.objects.only("name")),
                )
                .filter(filter_conditions)
                .order_by(order_by)
                .distinct()
            )

            # Pagination
            paginator = Paginator(queryset, page_size)
            try:
                paginated_jobs = paginator.page(page)
            except EmptyPage:
                paginated_jobs = paginator.page(paginator.num_pages)

            # Prepare response
            jobs_data = [
                {
                    "id": job.id,
                    "slug": job.slug,
                    "title": job.name,
                    "company": {
                        "name": job.company_name.name,
                        "logo": (
                            job.company_name.logo if job.company_name.logo else None
                        ),
                    },
                    "location": job.location,
                    "job_type": job.job_type,
                    "work_type": job.work_type,
                    "experience_level": job.experience_level,
                    "min_salary": job.min_salary,
                    "max_salary": job.max_salary,
                    "description": job.job_description[:200] + "...",
                    "required_skills": [
                        skill.name for skill in job.required_skills.all()
                    ],
                    "date_posted": job.date_created,
                    "status": job.status,
                }
                for job in paginated_jobs
            ]

            return APIResponse(
                success=True,
                data={
                    "jobs": jobs_data,
                    "total_pages": paginator.num_pages,
                    "current_page": page,
                    "total_jobs": paginator.count,
                },
                message="Jobs retrieved successfully",
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message=f"Error retrieving jobs",
                status=status.HTTP_400_BAD_REQUEST,
            )

    def update_job(self, user, job_slug, data) -> APIResponse:
        try:
            job_response = job_service.get_job(job_slug)
            if not job_response.success:
                return job_response
            response = job_service.update_job(
                user=job_response.data.created_by, job_slug=job_slug, data=data
            )
            serialize_data = GetJobSerializer(response.data).data
            return APIResponse(
                success=True,
                message="Job updated successfully",
                data=serialize_data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to update job",
                data=None,
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    
    def update_job_status(self, user, job_slug, data) -> APIResponse:
        try:
            job_response = job_service.get_job(job_slug)
            if not job_response.success:
                return job_response
            response = job_service._manage_statuses(job_response.data, data['status'])
            serialize_data = GetJobSerializer(response.data).data
            return APIResponse(
                success=True,
                message="Job status updated successfully",
                data=serialize_data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to update job status",
                data=None,
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def delete_job(self, user, job_slug) -> APIResponse:
        try:
            job_response = job_service.get_job(job_slug)
            if not job_response.success:
                return job_response
            response = job_service.delete_job(job_response.data.created_by, job_slug)
            return response
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to delete job",
                data=None,
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def create_job(self, user, business_slug, data) -> APIResponse:
        try:
            business_response = company_service.get_business_by_slug(business_slug)
            if not business_response.success:
                return APIResponse(
                    success=False,
                    message=business_response.message,
                    data=None,
                    status=business_response.status,
                )
            
            created_by_user = User.objects.get(id=business_response.data['created_by']['id'])
            
            response = job_service.create_job(
                user=created_by_user,
                company_slug=business_slug,
                data=data,
            )
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    status=response.status,
                )
            serializer = GetJobSerializer(response.data)
            return APIResponse(
                success=True,
                message=response.message,
                data=serializer.data,
                status=response.status,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to retrieve business",
                data=None,
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def get_job(self, user, job_slug) -> APIResponse:
        try:
            response = job_service.get_job(job_slug)
            if not response.success:
                return response
            serialize_data = GetJobSerializer(response.data).data
            return APIResponse(
                success=True,
                message="Job retrieved successfully",
                data=serialize_data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to retrieve job",
                data=None,
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
    
    def get_jobs_by_business(self, user, business_slug):
        try:
            business_response = company_service.get_business_by_slug(business_slug)
            if not business_response.success:
                return business_response
            created_by_user = User.objects.get(id=business_response.data['created_by']['id'])
            response = job_service.get_jobs_by_business(created_by_user, business_slug)
            serialize_queryset = GetJobSerializer(response.data, many=True)
            return APIResponse(
                success=True,
                message="Jobs retrieved successfully",
                data=serialize_queryset.data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to retrieve jobs",
                data=None,
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
