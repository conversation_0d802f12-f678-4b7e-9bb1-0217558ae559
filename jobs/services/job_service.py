from typing import List, Optional, Dict, Any
from django.db import models
from accounts.services.permission_services import PermissionsService
from accounts.services.profile_service import APIResponse
from base.services.logging import LoggingService
from businesses.repositories.business_repository import CompanyRepository
from businesses.repositories.service_repository import BusinessServiceRepository
from businesses.services.business import BusinessService
from candidates.models import Application
from candidates.repositories.application_repository import RepositoryResponse
from jobs.models import *
from jobs.repositories.job_repository import JobRepository
from rest_framework import status
from django.core.paginator import Paginator, EmptyPage
from django.db.models import Q, Prefetch

job_repository = JobRepository()
logging_service = LoggingService()
company_repository = CompanyRepository()


class JobService:
    """
    This service handles CRUD and other operations on jobs models
    """

    def create_job(self, user, company_slug, data: dict) -> APIResponse:
        if not user:
            return APIResponse(
                success=False,
                message="Logged in user is required to create a job",
                data=None,
                status=status.HTTP_401_UNAUTHORIZED,
            )
        try:
            required_fields = [
                "location",
                "job_type",
                "work_type",
                "job_description",
            ]

            missing_fields = logging_service.check_required_fields(
                data, required_fields
            )
            if missing_fields:
                return APIResponse(
                    success=False,
                    message=missing_fields,
                    data=None,
                    status=status.HTTP_400_BAD_REQUEST,
                )
            # Copy data to avoid modifying the original
            job_data = data.copy()

            company = company_repository.get_business(company_slug)
            if not company.success:
                return APIResponse(
                    success=False,
                    message=company.message,
                    data=None,
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Remove related fields from job_data
            # TODO: Check business subscription and limitations

            can_post_job = PermissionsService().is_eligible_to_post_job(
                user, company.data
            )

            if not can_post_job.success:
                return APIResponse(
                    success=False,
                    message=can_post_job.message,
                    data=None,
                    status=status.HTTP_403_FORBIDDEN,
                )

            for field in [
                "required_skills",
                "benefits",
            ]:
                job_data.pop(field, None)

            # Create the job

            response = job_repository.create_job(user, company.data, job_data)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    status=status.HTTP_400_BAD_REQUEST,
                )

            job = response.data
            
            if "benefits" in data:
                ben_response = self._add_benefits(job, data["benefits"])
                if not ben_response.success:
                    return APIResponse(
                        success=False,
                        message=ben_response.message,
                        data=None,
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            if "required_skills" in data:
                skill_response = self._add_required_skills(job, data["required_skills"])
                if not skill_response.success:
                    return APIResponse(
                        success=False,
                        message=skill_response.message,
                        data=None,
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            return APIResponse(
                success=True,
                message="Job created successfully",
                data=job,
                status=status.HTTP_201_CREATED,
            )
        except User.DoesNotExist:
            return APIResponse(
                success=False,
                message="User does not exist",
                data=None,
                status=status.HTTP_401_UNAUTHORIZED,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error creating job",
                data=None,
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def get_job(self, job_slug: str) -> APIResponse:

        response = job_repository.get_job(job_slug)
        if not response.success:
            return APIResponse(
                success=False,
                message=response.message,
                data=None,
                status=status.HTTP_404_NOT_FOUND,
            )
        return APIResponse(
            success=True,
            message="Job retrieved successfully",
            data=response.data,
            status=status.HTTP_200_OK,
        )

    def get_jobs_list(
        self,
        page: int = 1,
        page_size: int = 10,
        filters: Dict[str, Any] = None,
        search_query: str = None,
        order_by: str = "-date_created",
        user=None,
    ):
        try:
            filter_conditions = Q()

            # Handle filters
            if filters:
                # Job Type
                if job_type := filters.get("job_type", []):  # Changed from job_types
                    filter_conditions &= Q(job_type__in=job_type)

                # Experience Level
                if experience_level := filters.get("experience_level", []):
                    filter_conditions &= Q(experience_level__in=experience_level)

                # Job Industry
                if job_industry := filters.get("job_industry", []):
                    filter_conditions &= Q(job_industry__in=job_industry)

                # Work Type filter
                if work_types := filters.get("work_types", []):
                    filter_conditions &= Q(work_type__in=work_types)
                # Salary Range
                if min_salary := filters.get("min_salary"):
                    filter_conditions &= Q(salary__gte=min_salary)
                if max_salary := filters.get("max_salary"):
                    filter_conditions &= Q(salary__lte=max_salary)

                # Location
                if location := filters.get("location"):
                    filter_conditions &= Q(Q(location__icontains=location))

            # Search functionality
            if search_query:
                search_conditions = Q()
                search_terms = search_query.split()

                for term in search_terms:
                    term_condition = (
                        Q(name__icontains=term)
                        | Q(job_description__icontains=term)
                        | Q(company_name__name__icontains=term)
                        | Q(location__icontains=term)
                        | Q(required_skills__name__icontains=term)
                    )
                    search_conditions &= term_condition

                filter_conditions &= search_conditions

            # Query with optimization
            queryset = (
                Job.objects.select_related("company_name", "created_by")
                .prefetch_related(
                    Prefetch("required_skills", queryset=JobSkill.objects.only("name")),
                    Prefetch("benefits", queryset=Benefit.objects.only("name")),
                )
                .filter(filter_conditions)
                .filter(status="published")
                .order_by(order_by)
                .distinct()
            )

            # Pagination
            paginator = Paginator(queryset, page_size)
            try:
                paginated_jobs = paginator.page(page)
            except EmptyPage:
                paginated_jobs = paginator.page(paginator.num_pages)

            # Prepare response
            jobs_data = [
                {
                    "id": job.id,
                    "slug": job.slug,
                    "title": job.name,
                    "company": {
                        "name": job.company_name.name,
                        "logo": (
                            job.company_name.logo if job.company_name.logo else None
                        ),
                    },
                    "location": job.location,
                    "job_type": job.job_type,
                    "work_type": job.work_type,
                    "experience_level": job.experience_level,
                    "min_salary": job.min_salary,
                    "max_salary": job.max_salary,
                    "description": job.job_description[:200] + "...",
                    "required_skills": [
                        skill.name for skill in job.required_skills.all()
                    ],
                    "date_posted": job.date_created,
                    "has_applied": self.has_user_applied_to_job(job.id, user),
                }
                for job in paginated_jobs
            ]

            return APIResponse(
                success=True,
                data={
                    "jobs": jobs_data,
                    "total_pages": paginator.num_pages,
                    "current_page": page,
                    "total_jobs": paginator.count,
                },
                message="Jobs retrieved successfully",
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message=f"Error retrieving jobs: {str(e)}",
                status=status.HTTP_400_BAD_REQUEST,
            )

    def get_jobs_by_business(self, user, business_slug):
        try:
            # Get business
            business = company_repository.get_business(business_slug)
            if not business.success:
                return business

            queryset = Job.objects.filter(company_name=business.data)

            if not business.data.created_by == user:
                queryset.filter(status="published")

            # Return the results
            return APIResponse(
                success=True,
                data=queryset,
                message="Jobs retrieved successfully",
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message=f"Error retrieving jobs by business: {str(e)}",
                status=status.HTTP_400_BAD_REQUEST,
            )

    def update_job(self, user, job_slug: int, data: dict) -> APIResponse:

        try:
            # Get existing job
            job_response = self.get_job(job_slug)
            if not job_response.success:
                return job_response
            job = job_response.data

            if not job.created_by:
                return APIResponse(
                    success=False,
                    message="This job has no owner, email our support to claim it",
                    data=None,
                    status=status.HTTP_403_FORBIDDEN,
                )

            if job.created_by and not job.created_by == user:
                return APIResponse(
                    success=False,
                    message="You don't have permission to update this job",
                    data=None,
                    status=status.HTTP_403_FORBIDDEN,
                )
            job_data = data.copy()

            if "benefits" in job_data:
                self._update_benefits(job, job_data.pop("benefits"))

            if "required_skills" in job_data:
                self._update_required_skills(job, job_data.pop("required_skills"))

            # Update core job fields
            response = job_repository.update_job(job_slug, job_data)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                    status=status.HTTP_400_BAD_REQUEST,
                )

            return APIResponse(
                success=True,
                message="Job updated successfully",
                data=response.data,
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error updating job",
                data=None,
                status=status.HTTP_400_BAD_REQUEST,
            )

    def delete_job(self, user, job_slug: int) -> APIResponse:
        job = job_repository.get_job(job_slug)

        if not job.success:
            return APIResponse(
                success=False,
                message="Job not found",
                data=None,
                status=status.HTTP_404_NOT_FOUND,
            )
        if not job.data.created_by == user:
            return APIResponse(
                success=False,
                message="You don't have permission to delete this job",
                data=None,
                status=status.HTTP_403_FORBIDDEN,
            )

        response = job_repository.delete_job(job_slug)
        if not response.success:
            return APIResponse(
                success=False,
                message=response.message,
                data=None,
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        return APIResponse(
            success=True,
            message="Job deleted successfully",
            data=None,
            status=status.HTTP_200_OK,
        )

    def get_job_types(self) -> APIResponse:
        try:
            result = [
                {"id": 1, "name": "Full-Time"},
                {"id": 2, "name": "Part-Time"},
                {"id": 3, "name": "Contract"},
                {"id": 4, "name": "Temporary"},
                {"id": 5, "name": "Freelance"},
                {"id": 6, "name": "Internship"},
                {"id": 7, "name": "Seasonal"},
                {"id": 8, "name": "Remote"},
                {"id": 9, "name": "Hybrid"},
                {"id": 10, "name": "Casual"},
            ]
            return APIResponse(
                success=True,
                data=result,
                message="Job types retrieved successfully",
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error retrieving job types",
                data=None,
                status=status.HTTP_400_BAD_REQUEST,
            )

    def get_work_types(self) -> APIResponse:
        try:
            result = [
                {"id": 1, "name": "Salaried"},
                {"id": 2, "name": "Hourly"},
                {"id": 3, "name": "Commission-Based"},
                {"id": 4, "name": "Shift Work"},
                {"id": 5, "name": "On-Call"},
                {"id": 6, "name": "Project-Based"},
                {"id": 7, "name": "Piecework"},
                {"id": 8, "name": "Volunteer"},
                {"id": 9, "name": "Gig Work"},
                {"id": 10, "name": "Overtime-Eligible"},
            ]
            return APIResponse(
                success=True,
                data=result,
                message="Work types retrieved successfully",
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error retrieving work types",
                data=None,
                status=status.HTTP_400_BAD_REQUEST,
            )

    def get_experience_levels(self) -> APIResponse:
        try:
            result = [
                {"id": 1, "name": "Entry-Level"},
                {"id": 2, "name": "Junior"},
                {"id": 3, "name": "Mid-Level"},
                {"id": 4, "name": "Senior"},
                {"id": 5, "name": "Lead"},
                {"id": 6, "name": "Manager"},
                {"id": 7, "name": "Director"},
                {"id": 8, "name": "Executive"},
                {"id": 9, "name": "Specialist"},
                {"id": 10, "name": "Intern"},
            ]
            return APIResponse(
                success=True,
                data=result,
                message="Experience levels retrieved successfully",
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error retrieving experience levels",
                data=None,
                status=status.HTTP_400_BAD_REQUEST,
            )

    def get_locations(self) -> APIResponse:
        """Get list of locations which exists in the database"""
        try:
            locations = (
                Job.objects.filter(~Q(location=None))
                .values("location")
                .distinct()
                .order_by("location")
            )

            # Transform into list of dictionaries with id and name
            result = [
                {"id": idx + 1, "name": item["location"]}
                for idx, item in enumerate(locations)
            ]
            return APIResponse(
                success=True,
                data=result,
                message=(
                    "Locations retrieved successfully"
                    if result
                    else "No locations found"
                ),
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error retrieving locations",
                data=None,
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _add_skills(self, job: Job, skills_data: List[dict]) -> RepositoryResponse:

        try:
            for skill_data in skills_data:
                category = None
                if "category" in skill_data:
                    category, _ = JobSkillsCategory.objects.get_or_create(
                        name=skill_data["category"]
                    )

                skill, _ = JobSkill.objects.get_or_create(
                    name=skill_data["name"], category_name=category
                )
                job.required_skills.add(skill)

            return RepositoryResponse(
                success=True,
                message="Skills added successfully",
                data=job,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error adding skills",
                data=None,
            )

    def _remove_skills(self, job: Job, skills_data: List[str]) -> RepositoryResponse:

        try:
            skills_to_remove = JobSkill.objects.filter(name__in=skills_data)
            job.required_skills.remove(*skills_to_remove)
            return RepositoryResponse(
                success=True,
                message="Skills removed successfully",
                data=job,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error removing skills",
                data=None,
            )

    def _update_skills(self, job: Job, skills_data: List[dict]) -> RepositoryResponse:

        try:
            job.required_skills.clear()
            return self._add_skills(job, skills_data)
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error updating skills",
                data=None,
            )

    def _add_benefits(self, job: Job, benefits_data: List[dict]) -> RepositoryResponse:

        try:
            for benefit_data in benefits_data:
                benefit, _ = Benefit.objects.get_or_create(
                    name=benefit_data["name"],
                    defaults={
                        "description": benefit_data.get("description"),
                        "icon": benefit_data.get("icon"),
                    },
                )
                job.benefits.add(benefit)

            return RepositoryResponse(
                success=True,
                message="Benefits added successfully",
                data=job,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error adding benefits",
                data=None,
            )

    def _remove_benefits(
        self, job: Job, benefits_data: List[str]
    ) -> RepositoryResponse:

        try:
            benefits_to_remove = Benefit.objects.filter(name__in=benefits_data)
            job.benefits.remove(*benefits_to_remove)
            return RepositoryResponse(
                success=True,
                message="Benefits removed successfully",
                data=job,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error removing benefits",
                data=None,
            )

    def _update_benefits(
        self, job: Job, benefits_data: List[dict]
    ) -> RepositoryResponse:

        try:
            job.benefits.clear()
            response = self._add_benefits(job, benefits_data)
            if not response.success:
                return RepositoryResponse(
                    success=False,
                    message=response.message,
                    data=None,
                )
            return RepositoryResponse(
                success=True,
                message="Benefits updated successfully",
                data=job,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error updating benefits",
                data=None,
            )

    def _add_required_skills(
        self, job: Job, skills_data: List[dict]
    ) -> RepositoryResponse:
        """Add required skills to a job"""
        return self._add_skills(job, skills_data)

    def _remove_required_skills(
        self, job: Job, skills_data: List[str]
    ) -> RepositoryResponse:
        """Remove required skills from a job"""
        return self._remove_skills(job, skills_data)

    def _update_required_skills(
        self, job: Job, skills_data: List[dict]
    ) -> RepositoryResponse:
        """Update required skills for a job"""
        return self._update_skills(job, skills_data)

    def _manage_applicants(self, job: Job) -> RepositoryResponse:

        try:
            job.applicants = models.F("applicants") + 1
            job.save()
            job.refresh_from_db()

            return RepositoryResponse(
                success=True, message="Applicant count updated successfully", data=job
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False, message="Error managing applicants", data=None
            )

    def _manage_statuses(self, job: Job, status: str) -> RepositoryResponse:

        try:
            if status not in dict(Job.status.field.choices):
                return RepositoryResponse(
                    success=False, message="Invalid status value", data=None
                )

            job.status = status
            job.save()

            return RepositoryResponse(
                success=True,
                message="Job status updated successfully",
                data=job,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error managing status",
                data=None,
            )

    def has_user_applied_to_job(self, job_id, user):
        """Check if user has applied to a specific job"""
        if not isinstance(user, User):
            return False

        if not job_id:
            return False

        return Application.objects.filter(
            applicant__user=user, job_applied_id=job_id
        ).exists()
