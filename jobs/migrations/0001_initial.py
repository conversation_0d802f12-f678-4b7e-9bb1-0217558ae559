# Generated by Django 4.2.11 on 2024-04-15 17:28

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('base', '0001_initial'),
        ('businesses', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Benefit',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
            ],
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='ExperienceLevel',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
            ],
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='JobSkillsCategory',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
            ],
            options={
                'verbose_name_plural': 'Job Skills Categories',
            },
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='JobType',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
            ],
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='WorkType',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
            ],
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='JobSkill',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('category_name', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='jobs.jobskillscategory')),
            ],
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='Job',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('applicants', models.IntegerField(blank=True, null=True)),
                ('location', models.CharField(max_length=255, null=True)),
                ('min_salary', models.DecimalField(decimal_places=2, max_digits=10)),
                ('max_salary', models.DecimalField(decimal_places=2, max_digits=10)),
                ('job_description', models.TextField(max_length=500)),
                ('benefits', models.ManyToManyField(to='jobs.benefit')),
                ('company_name', models.ForeignKey(max_length=255, on_delete=django.db.models.deletion.CASCADE, to='businesses.company')),
                ('experience_level', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='jobs.experiencelevel')),
                ('job_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='jobs.jobtype')),
                ('required_skills', models.ManyToManyField(to='jobs.jobskill')),
                ('work_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='jobs.worktype')),
            ],
            bases=('base.basemodel',),
        ),
    ]
