# Generated by Django 5.1.4 on 2024-12-10 08:39

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('businesses', '0006_alter_company_business_address_and_more'),
        ('jobs', '0007_job_status'),
    ]

    operations = [
        migrations.AlterField(
            model_name='job',
            name='company_name',
            field=models.ForeignKey(blank=True, max_length=255, null=True, on_delete=django.db.models.deletion.CASCADE, to='businesses.company'),
        ),
        migrations.AlterField(
            model_name='job',
            name='experience_level',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='jobs.experiencelevel'),
        ),
        migrations.AlterField(
            model_name='job',
            name='job_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='jobs.jobtype'),
        ),
        migrations.AlterField(
            model_name='job',
            name='work_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='jobs.worktype'),
        ),
    ]
