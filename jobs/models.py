from django.contrib.auth.models import User
from base.models import BaseModel
import uuid
from django.db import models
from businesses.models import Company
from ckeditor.fields import RichTextField


# Create your models here.
class Benefit(BaseModel):
    icon = models.ImageField(upload_to="protected/", null=True, blank=True)
    description = models.CharField(max_length=255, null=True, blank=True)


class JobType(BaseModel):
    pass


class WorkType(BaseModel):
    pass


class JobSkillsCategory(BaseModel):
    class Meta:
        verbose_name_plural = "Job Skills Categories"


class JobSkill(BaseModel):
    category_name = models.ForeignKey(
        JobSkillsCategory, on_delete=models.CASCADE, null=True
    )


class ExperienceLevel(BaseModel):
    pass


class Job(BaseModel):
    applicants = models.IntegerField(null=True, blank=True, default=0)
    company_name = models.ForeignKey(
        Company, on_delete=models.CASCADE, max_length=255, null=True, blank=True
    )
    location = models.CharField(max_length=255, null=True)
    job_type = models.CharField(max_length=255, null=True)
    work_type = models.CharField(max_length=255, null=True)
    experience_level = models.CharField(max_length=255, null=True)
    min_salary = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    max_salary = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    job_description = models.TextField(max_length=500)
    responsibilities = models.TextField(max_length=500, null=True, blank=True)
    qualifications = models.TextField(max_length=500, null=True, blank=True)
    nice_to_have = models.TextField(max_length=500, null=True, blank=True)
    benefits = models.ManyToManyField(Benefit)
    required_skills = models.ManyToManyField(JobSkill)
    status = models.CharField(
        max_length=50,
        choices=[
            ("closed", "Closed"),
            ("published", "Published"),
            ("draft", "Draft"),
        ],
        default="draft",
        null=True,
        blank=True,
    )
