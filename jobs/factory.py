import factory
from factory.django import DjangoModelFactory
from factory import fuzzy
from decimal import Decimal

from businesses.factory import CompanyFactory
from .models import (
    Benefit,
    JobType,
    WorkType,
    JobSkillsCategory,
    JobSkill,
    ExperienceLevel,
    Job,
)


class BenefitFactory(DjangoModelFactory):
    class Meta:
        model = Benefit

    name = factory.Sequence(lambda n: f"Benefit {n}")
    description = factory.Faker("sentence")
    created_by = factory.SubFactory("base.factory.UserFactory")


class JobTypeFactory(DjangoModelFactory):
    class Meta:
        model = JobType

    name = factory.Sequence(lambda n: f"Job Type {n}")
    created_by = factory.SubFactory("base.factory.UserFactory")


class WorkTypeFactory(DjangoModelFactory):
    class Meta:
        model = WorkType

    name = factory.Sequence(lambda n: f"Work Type {n}")
    created_by = factory.SubFactory("base.factory.UserFactory")


class JobSkillsCategoryFactory(DjangoModelFactory):
    class Meta:
        model = JobSkillsCategory

    name = factory.Sequence(lambda n: f"Skill Category {n}")
    created_by = factory.SubFactory("base.factory.UserFactory")


class JobSkillFactory(DjangoModelFactory):
    class Meta:
        model = JobSkill

    name = factory.Sequence(lambda n: f"Skill {n}")
    category_name = factory.SubFactory(JobSkillsCategoryFactory)
    created_by = factory.SubFactory("base.factory.UserFactory")


class ExperienceLevelFactory(DjangoModelFactory):
    class Meta:
        model = ExperienceLevel

    name = factory.Sequence(lambda n: f"Experience Level {n}")
    created_by = factory.SubFactory("base.factory.UserFactory")


class JobFactory(DjangoModelFactory):
    class Meta:
        model = Job

    name = factory.Sequence(lambda n: f"Job Position {n}")
    applicants = factory.Faker("random_int", min=0, max=1000)
    company_name = factory.SubFactory(CompanyFactory)
    location = factory.Faker("city")
    job_type = factory.Faker(
        "random_element", elements=["Full-time", "Part-time", "Contract"]
    )
    work_type = factory.Faker(
        "random_element", elements=["Remote", "Hybrid", "On-site"]
    )
    experience_level = factory.Faker(
        "random_element", elements=["Entry", "Mid", "Senior"]
    )
    min_salary = factory.LazyFunction(
        lambda: Decimal(str(fuzzy.FuzzyDecimal(30000, 50000).fuzz()))
    )
    max_salary = factory.LazyFunction(
        lambda: Decimal(str(fuzzy.FuzzyDecimal(51000, 150000).fuzz()))
    )
    job_description = factory.Faker("paragraph")
    responsibilities = factory.Faker("paragraph")
    qualifications = factory.Faker("paragraph")
    nice_to_have = factory.Faker("paragraph")
    status = factory.Faker("random_element", elements=["active", "closed"])
    created_by = factory.SubFactory("base.factory.UserFactory")

    @factory.post_generation
    def benefits(self, create, extracted, **kwargs):
        if not create:
            return

        if extracted:
            for benefit in extracted:
                self.benefits.add(benefit)
        else:
            self.benefits.add(BenefitFactory())

    @factory.post_generation
    def required_skills(self, create, extracted, **kwargs):
        if not create:
            return

        if extracted:
            for skill in extracted:
                self.required_skills.add(skill)
        else:
            self.required_skills.add(JobSkillFactory())
