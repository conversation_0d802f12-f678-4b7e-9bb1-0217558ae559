from ..models import JobType
from django.db import transaction
from candidates.repositories.application_repository import RepositoryResponse


class JobTypeRepository:
    """Create a new job type"""
    @transaction.atomic
    def create_job_type(self, data):
        try:
            job_type = JobType.objects.create(**data)
            return RepositoryResponse(
                success=True,
                data=job_type,
                message="Job type created successfully",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to create job type: {str(e)}",
            )
    
    """Get a job type by id"""
    def get_job_type(self, job_type_id):
        try:
            job_type = JobType.objects.get(pk=job_type_id)
            return RepositoryResponse(
                success=True,
                data=job_type,
                message="Job type retrieved successfully",
            )
        except JobType.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Job type not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to retrieve job type: {str(e)}",
            )
    
    """Update an existing job type"""
    @transaction.atomic
    def update_job_type(self, job_type_id, data):
        try:
            job_type = JobType.objects.get(pk=job_type_id)
            for key, value in data.items():
                setattr(job_type, key, value)
            job_type.save()
            return RepositoryResponse(
                success=True,
                data=job_type,
                message="Job type updated successfully",
            )
        except JobType.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Job type not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to update job type: {str(e)}",
            )
    
    """Delete a job type"""
    @transaction.atomic
    def delete_job_type(self, job_type_id):
        try:
            job_type = JobType.objects.get(pk=job_type_id)
            job_type.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Job type deleted successfully",
            )
        except JobType.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Job type not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to delete job type: {str(e)}",
            )