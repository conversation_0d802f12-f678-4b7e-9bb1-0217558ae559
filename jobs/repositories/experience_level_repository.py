from ..models import ExperienceLevel
from django.db import transaction
from candidates.repositories.application_repository import RepositoryResponse


class ExperienceLevelRepository:
    """Create a new experience level"""
    @transaction.atomic
    def create_experience_level(self, data):
        try:
            experience_level = ExperienceLevel.objects.create(**data)
            return RepositoryResponse(
                success=True,
                data=experience_level,
                message="Experience level created successfully",)
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to create experience level: {str(e)}",
            )
    
    """Get an experience level by id"""
    def get_experience_level(self, experience_level_id):
        try:
            experience_level = ExperienceLevel.objects.get(pk=experience_level_id)
            return RepositoryResponse(
                success=True,
                data=experience_level,
                message="Experience level retrieved successfully",
            )
        except ExperienceLevel.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Experience level not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to retrieve experience level: {str(e)}",
            )
    
    """Update an existing experience level"""
    @transaction.atomic
    def update_experience_level(self, experience_level_id, data):
        try:
            experience_level = ExperienceLevel.objects.get(pk=experience_level_id)
            for key, value in data.items():
                setattr(experience_level, key, value)
            experience_level.save()
            return RepositoryResponse(
                success=True,
                data=experience_level,
                message="Experience level updated successfully",
            )
        except ExperienceLevel.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Experience level not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to update experience level: {str(e)}",
            )
    
    """Delete an experience level"""
    @transaction.atomic
    def delete_experience_level(self, experience_level_id):
        try:
            experience_level = ExperienceLevel.objects.get(pk=experience_level_id)
            experience_level.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Experience level deleted successfully",
            )
        except ExperienceLevel.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Experience level not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to delete experience level: {str(e)}",
            )