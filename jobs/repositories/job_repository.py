from base.services.logging import LoggingService
from ..models import Job
from dataclasses import dataclass
from typing import Union, Optional, Dict, List
from django.db.models import Model
from django.db import transaction
from candidates.repositories.application_repository import RepositoryResponse

logging_service = LoggingService()


class JobRepository:
    """Create a job"""

    @transaction.atomic
    def create_job(self, user, company, data):

        try:
            job = Job.objects.create(**data)
            job.created_by = user
            job.company_name = company
            job.save()
            return RepositoryResponse(
                success=True,
                data=job,
                message="Job created successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to create job",
            )

    def get_job(self, job_slug):
        """Get a job by id"""
        try:
            job = Job.objects.get(slug=job_slug)
            return RepositoryResponse(
                success=True,
                data=job,
                message="Job retrieved successfully",
            )
        except Job.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Job not found",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to retrieve job",
            )

    def get_job_by_id(self, id):
        """Get a job by id"""
        try:
            job = Job.objects.get(id=id)
            return RepositoryResponse(
                success=True,
                data=job,
                message="Job retrieved successfully",
            )
        except Job.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Job not found",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to retrieve job",
            )

    def get_all_jobs(self, company_name):
        """Get all jobs by company"""
        try:
            jobs = Job.objects.filter(company_name=company_name)
            return RepositoryResponse(
                success=True,
                data=jobs,
                message="Jobs retrieved successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to retrieve jobs",
            )

    @transaction.atomic
    def update_job(self, job_slug, data):
        """Update existing job"""
        try:
            job = Job.objects.get(slug=job_slug)

            for key, value in data.items():
                setattr(job, key, value)

            job.save()
            return RepositoryResponse(
                success=True,
                data=job,
                message="Job updated successfully",
            )
        except Job.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Job not found",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to update job",
            )

    @transaction.atomic
    def delete_job(self, job_slug):
        """Delete a job"""
        try:
            job = Job.objects.get(slug=job_slug)
            job.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Job deleted successfully",
            )
        except Job.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Job not found",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to delete job",
            )
