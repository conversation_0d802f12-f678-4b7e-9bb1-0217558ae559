from ..models import Benefit
from django.db import transaction
from candidates.repositories.application_repository import RepositoryResponse


class BenefitRepository:
    """Create a new benefit"""
    @transaction.atomic
    def create_benefit(self, data):
        try:
            benefit = Benefit.objects.create(**data)
            return RepositoryResponse(
                success=True,
                data=benefit,
                message="Benefit created successfully",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to create benefit: {str(e)}",
            )
    
    """Get a benefit by id"""
    def get_benefit(self, benefit_id):
        try:
            benefit = Benefit.objects.get(pk=benefit_id)
            return RepositoryResponse(
                success=True,
                data=benefit,
                message="Benefit retrieved successfully",
            )
        except Benefit.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Benefit not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to retrieve benefit: {str(e)}",
            )
    
    """Update an existing benefit"""
    @transaction.atomic
    def update_benefit(self, benefit_id, data):
        try:
            benefit = Benefit.objects.get(pk=benefit_id)
            for key, value in data.items():
                setattr(benefit, key, value)
            benefit.save()
            return RepositoryResponse(
                success=True,
                data=benefit,
                message="Benefit updated successfully",
            )
        except Benefit.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Benefit not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to update benefit: {str(e)}",
            )
    
    """Delete a benefit"""
    @transaction.atomic
    def delete_benefit(self, benefit_id):
        try:
            benefit = Benefit.objects.get(pk=benefit_id)
            benefit.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Benefit deleted successfully",
            )
        except Benefit.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Benefit not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to delete benefit: {str(e)}",
            )