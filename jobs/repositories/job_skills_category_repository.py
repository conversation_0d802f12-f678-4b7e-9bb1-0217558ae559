from ..models import JobSkillsCategory
from django.db import transaction
from candidates.repositories.application_repository import RepositoryResponse


class JobSkillsCategoryRepository:
    """Create a new job skills category"""
    @transaction.atomic
    def create_job_skills_category(self, data):
        try:
            job_skills_category = JobSkillsCategory.objects.create(**data)
            return RepositoryResponse(
                success=True,
                data=job_skills_category,
                message="Job skills category created successfully",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to create job skills category: {str(e)}",
            )
    
    """Get a job skills category by id"""
    def get_job_skills_category(self, job_skills_category_id):
        try:
            job_skills_category = JobSkillsCategory.objects.get(pk=job_skills_category_id)
            return RepositoryResponse(
                success=True,
                data=job_skills_category,
                message="Job skills category retrieved successfully",
            )
        except JobSkillsCategory.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Job skills category not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to retrieve job skills category: {str(e)}",
            )
    
    """Update an existing job skills category"""
    @transaction.atomic
    def update_job_skills_category(self, job_skills_category_id, data):
        try:
            job_skills_category = JobSkillsCategory.objects.get(pk=job_skills_category_id)
            for key, value in data.items():
                setattr(job_skills_category, key, value)
            job_skills_category.save()
            return RepositoryResponse(
                success=True,
                data=job_skills_category,
                message="Job skills category updated successfully",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to update job skills category: {str(e)}",
            )
    
    """Delete a job skills category"""
    @transaction.atomic
    def delete_job_skills_category(self, job_skills_category_id):
        try:
            job_skills_category = JobSkillsCategory.objects.get(pk=job_skills_category_id)
            job_skills_category.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Job skills category deleted successfully",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to delete job skills category: {str(e)}",
            )