from ..models import JobSkill
from django.db import transaction
from candidates.repositories.application_repository import RepositoryResponse


class JobSkillRepository:
    """Create a new job skill"""
    @transaction.atomic
    def create_job_skill(self, data):
        try:
            job_skill = JobSkill.objects.create(**data)
            return RepositoryResponse(
                success=True,
                data=job_skill,
                message="Job skill created successfully",)
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to create job skill: {str(e)}",
            )
    
    """Get a job skill by id"""
    def get_job_skill(self, job_skill_id):
        try:
            job_skill = JobSkill.objects.get(pk=job_skill_id)
            return RepositoryResponse(
                success=True,
                data=job_skill,
                message="Job skill retrieved successfully",
            )
        except JobSkill.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Job skill not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to retrieve job skill: {str(e)}",
            )
    
    """Update an existing job skill"""
    @transaction.atomic
    def update_job_skill(self, job_skill_id, data):
        try:
            job_skill = JobSkill.objects.get(pk=job_skill_id)
            for key, value in data.items():
                setattr(job_skill, key, value)
            job_skill.save()
            return RepositoryResponse(
                success=True,
                data=job_skill,
                message="Job skill updated successfully",
            )
        except JobSkill.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Job skill not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to update job skill: {str(e)}",
            )
    
    """Delete a job skill"""
    @transaction.atomic
    def delete_job_skill(self, job_skill_id):
        try:
            job_skill = JobSkill.objects.get(pk=job_skill_id)
            job_skill.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Job skill deleted successfully",
            )
        except JobSkill.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Job skill not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to delete job skill: {str(e)}",
            )
    