from ..models import WorkType
from django.db import transaction
from candidates.repositories.application_repository import RepositoryResponse


class WorkTypeRepository:
    """Create a new work type"""
    @transaction.atomic
    def create_work_type(self, data):
        try:
            work_type = WorkType.objects.create(**data)
            return RepositoryResponse(
                success=True,
                data=work_type,
                message="Work type created successfully",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to create work type: {str(e)}",
            )
    
    """Get a work type by id"""
    def get_work_type(self, work_type_id):
        try:
            work_type = WorkType.objects.get(pk=work_type_id)
            return RepositoryResponse(
                success=True,
                data=work_type,
                message="Work type retrieved successfully",
            )
        except WorkType.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Work type not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to retrieve work type: {str(e)}",
            )
    
    """Update an existing work type"""
    @transaction.atomic
    def update_work_type(self, work_type_id, data):
        try:
            work_type = WorkType.objects.get(pk=work_type_id)
            for key, value in data.items():
                setattr(work_type, key, value)
            work_type.save()
            return RepositoryResponse(
                success=True,
                data=work_type,
                message="Work type updated successfully",
            )
        except WorkType.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Work type not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to update work type: {str(e)}",
            )
    
    """Delete a work type"""
    @transaction.atomic
    def delete_work_type(self, work_type_id):
        try:
            work_type = WorkType.objects.get(pk=work_type_id)
            work_type.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Work type deleted successfully",
            )
        except WorkType.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Work type not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=f"Failed to delete work type: {str(e)}",
            )