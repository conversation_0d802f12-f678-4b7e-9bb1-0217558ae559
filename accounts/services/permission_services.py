# a service to check permissions
from accounts.models import Subscription
from accounts.services.profile_service import APIResponse
from base.services.logging import LoggingService
from typing import Type

from businesses.models import Company


class PermissionsService:
    def __init__(self):
        self.logger = LoggingService()

    user_user_groups = ["Super user"]
    admin_user_groups = ["Admin", "Super user"]
    manager_user_groups = ["Manager", "Admin", "Super user"]

    # super user permissions
    def is_super_user(self, user):
        if user is None:
            return False
        return any(group.name in self.user_user_groups for group in user.groups.all())

    # admin permissions
    def is_admin(self, user):
        if user is None:
            return False
        return any(group.name in self.admin_user_groups for group in user.groups.all())

    # manager permissions
    def is_manager(self, user):
        if user is None:
            return False
        return any(
            group.name in self.manager_user_groups for group in user.groups.all()
        )

    # user owns item permissions
    def owns_item(self, user, item):
        if user is None:
            return False
        if not item or not hasattr(item, "created_by"):
            return False  # Return False if item is invalid or lacks a 'created_by' attribute

        return user.id == item.created_by.id

    def owns_company(self, user, company: Type[Company]) -> bool:
        """
        Checks if the given user is the owner of the specified company.
        Args:
            user: The user object to check ownership against.
            company (Type[Company]): The company instance to verify ownership.
        Returns:
            bool: True if the user is the owner of the company, False otherwise.
        Logs:
            Logs an error if the user is not the owner of the company.
        """

        if not company.created_by or not company.created_by.id == user.id:
            self.logger.log_error("User is not the owner of the company.")
            return False
        return True

    def has_active_subscription(self, subscription: Type[Subscription]) -> bool:
        """
        Checks if the provided subscription is active.

        Args:
            subscription (object): The subscription object to check.

        Returns:
            bool: True if the subscription exists (is active), False otherwise.

        Logs:
            An error message if the subscription is not active.
        """
        if not subscription or not subscription.is_active:
            self.logger.log_error("Company does not have an active subscription.")
            return False
        return True

    def has_reached_job_posting_limit(self, subscription: Type[Subscription]) -> bool:
        """
        Checks if the company has reached its job posting limit based on the provided subscription.

        Args:
            subscription: An object representing the company's subscription, expected to have a 'sub_package'
                          attribute with 'has_limit', 'current_job_postings', and 'limit' properties.

        Returns:
            bool: False if the job posting limit has been reached, True otherwise.

        Logs:
            Logs an error message if the job posting limit has been reached.
        """
        if (
            subscription.sub_package.has_limit
            and subscription.sub_package.current_job_postings
            >= subscription.sub_package.limit
        ):
            self.logger.log_error("Company has reached its job posting limit.")
            return False
        return True

    def is_eligible_to_post_job(self, user, company: Type[Company]) -> APIResponse:
        """
        Determines if a user is eligible to post a job for a given company.

        This method checks the following conditions:
            1. The user owns the specified company.
            2. The company has an active subscription.
            3. The company has not reached its job posting limit.

        Args:
            user: The user object attempting to post a job.
            company (Type[Company]): The company for which the job is being posted.

        Returns:
            APIResponse: An object indicating whether the user is eligible to post a job,
                         including a success flag, a message, and optional data.
        """

        try:
            subscription = Subscription.objects.filter(
                sub_company=company, is_active=True
            ).first()

            if not self.owns_company(user, company):
                return APIResponse(
                    success=False,
                    message="You are not the owner of the company.",
                    data=None,
                )

            if not self.has_active_subscription(subscription):
                return APIResponse(
                    success=False,
                    message="Company does not have an active subscription.",
                    data=None,
                )

            if not self.has_reached_job_posting_limit(subscription):
                return APIResponse(
                    success=False,
                    message="Company has reached its job posting limit.",
                    data=None,
                )
            return APIResponse(
                success=True,
                message="User is eligible to post a job.",
                data=None,
            )

        except Exception as e:
            self.logger.log_error(e)
            return APIResponse(
                success=False,
                message=f"Error checking subscription: {str(e)}",
                data=None,
            )
