import profile
from accounts.repositories.user_repository import RepositoryResponse, UserRepository
from accounts.services.permission_services import PermissionsService
from django.contrib.auth.models import Group
from base.services.caching import CachingService
from base.services.logging import LoggingService
from businesses.repositories.business_repository import CompanyRepository
from businesses.views.serializers import BusinessSerializer
from candidates.models import AcademicEducation, Candidate, WorkExperience
from candidates.repositories.candidate_repository import CandidateRepository
from candidates.views.serializers import CandidateSerializer
from django.forms.models import model_to_dict


# from django.contrib.auth.hashers import make_password


user_repository = UserRepository()
permissions = PermissionsService()


# user services to create, update, get, and delete users


class UserService:
    def __init__(self):
        self.caching = CachingService()

    def create_user(self, user_data, logged_in_user):
        if isinstance(user_data, dict):
            full_request_data = user_data.copy()

            roles = full_request_data.get("roles", [])
            if "roles" in user_data:
                del user_data["roles"]
            response = user_repository.create_user(user_data)

            if not response.success or not response.data:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message=f"Error creating user: {response.message}",
                )

            if response.success:
                response.data.set_password(user_data["password"])
                if roles:
                    if logged_in_user is None or permissions.is_admin(logged_in_user):
                        for role in roles:
                            role_obj, _ = Group.objects.get_or_create(name=role)
                            response.data.groups.add(role_obj)
                        response.data.save()
                    else:
                        return RepositoryResponse(
                            success=False,
                            data=None,
                            message="Only admins can assign permissions",
                        )
            
            user_dict = model_to_dict(response.data, exclude=['password'])
            """ Add to users list """
            self.caching.addToCacheList("users", user_dict)
            """ Cache individual user """
            self.caching.setCache(f"user_{response.data.id}", user_dict)
            
            return response
        else:
            return RepositoryResponse(success=False, data=None, message="Invalid Data")

    def update_user_by_id(self, user_id, updated_user_data, logged_in_user):
        # if not permissions.is_admin(logged_in_user) or not permissions.owns_item(
        #     logged_in_user, updated_user_data
        # ):
        #     return (
        #         False,
        #         None,
        #         "User does not have admin permissions or does not own the item.",
        #     )
        response = user_repository.update_user(user_id, updated_user_data)
        
        if response.success:
            user_dict = model_to_dict(response.data, exclude=['password'])
            """ Update in users list - use id as the identifier """
            self.caching.updateCacheItem("users", user_id, user_dict)
            """ Update individual user cache """
            self.caching.updateCache(f"user_{user_id}", user_dict)
        
        return response

    def get_user_by_id(self, user_id):
        cache_key = f"user_{user_id}"
        cached_user = self.caching.getCache(cache_key)
        
        if cached_user:
            return RepositoryResponse(
                success=True,
                data=cached_user,
                message="User retrieved from cache"
            )
        
        response = user_repository.get_user(user_id)
        
        if response.success:
            user_dict = model_to_dict(response.data, exclude=['password'])
            self.caching.setCache(cache_key, user_dict)
            
            if self.caching.hasCache("users"):
                self.caching.updateCacheItem("users", user_id, user_dict)
        
        return response

    def delete_user_by_id(self, user_id, logged_in_user):
        user_response = user_repository.get_user(user_id)

        if not user_response.success:
            return user_response

        if logged_in_user.id == user_id:
            return RepositoryResponse(
                False,
                None,
                "A superuser cannot delete their own account.",
            )

        if not permissions.is_super_user(logged_in_user):
            return RepositoryResponse(
                False,
                None,
                "User does not have super user permissions.",
            )
        
        response = user_repository.delete_user(user_id)
        
        if response.success:
            self.caching.removeCacheItem("users", user_id)
            self.caching.deleteCache(f"user_{user_id}")
        
        return response

    def get_all_users(self):
        cached_users = self.caching.getCache("users")
        
        if cached_users:
            return RepositoryResponse(
                success=True,
                data=cached_users,
                message="Users retrieved from cache"
            )
        
        response = user_repository.get_all_users()
        
        if response.success:
            """ Cache all users for future requests """
            users_dict = [model_to_dict(user, exclude=['password']) for user in response.data]
            self.caching.setCache("users", users_dict)
            
            """cache individual users """
            for user in users_dict:
                self.caching.setCache(f"user_{user['id']}", user)
        
        return response

    def sign_up(self, signup_data):
        self.candidate_repository = CandidateRepository()
        self.business_repository = CompanyRepository()
        """Handle user signup"""
        required_fields = [
            "password",
            "email",
            "first_name",
            "last_name",
            "account_type",
            "phone_number",
            "gender",
        ]

        for field in required_fields:
            if not signup_data.get(field):
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message=f"Missing required field: {field}",
                )

        user_data = {
            "username": signup_data["email"],
            "password": signup_data["password"],
            "email": signup_data["email"],
            "first_name": signup_data["first_name"],
            "last_name": signup_data["last_name"],
            "roles": (
                signup_data["account_type"]
                if signup_data["account_type"] == "candidate"
                else []
            ),
        }

        try:
            response = self.create_user(user_data, None)

            if response.success:
                user = response.data
                if signup_data["account_type"] == "candidate":
                    response = self.candidate_repository.create_candidate(
                        {
                            "user": user,
                            "phone_number": signup_data["phone_number"],
                            "gender": signup_data["gender"],
                            "date_of_birth": signup_data["date_of_birth"],
                            "created_by": user,
                        }
                    )
                    if not response.success:
                        return RepositoryResponse(
                            success=False,
                            data=None,
                            message=f"Error creating candidate: {response.message}",
                        )
                    """ Cache the candidate object """
                    try:
                        candidate_object = CandidateSerializer(response.data).data
                        self.caching.addToCacheList(
                            "candidates",
                            candidate_object,
                        )
                        """ Also cache individual candidate """
                        self.caching.setCache(f"candidate_{candidate_object['id']}", candidate_object)
                    except Exception as e:
                        return RepositoryResponse(
                            success=False,
                            data=None,
                            message="Error caching candidate data",
                        )

                elif signup_data["account_type"] == "business":
                    try:
                        if not "business_name" in signup_data:
                            return RepositoryResponse(
                                success=False,
                                data=None,
                                message="Business name is required",
                            )
                        data = {
                            "name": signup_data["business_name"],
                            "email": signup_data["email"],
                            "phone_number": signup_data["phone_number"],
                        }
                        response = self.business_repository.create_business(user, data)
                        if not response.success:
                            return RepositoryResponse(
                                success=False,
                                data=None,
                                message=f"Error creating business: {response.message}",
                            )

                        """ cache the business object """
                        business_object = BusinessSerializer(response.data).data
                        self.caching.addToCacheList(
                            "businesses",
                            business_object,
                        )
                        """ Also cache individual business """
                        self.caching.setCache(f"business_{business_object['id']}", business_object)

                    except Exception as e:
                        logging_service = LoggingService()
                        logging_service.log_error(e)

                        return RepositoryResponse(
                            success=False,
                            data=None,
                            message="Error creating business",
                        )
                return RepositoryResponse(
                    success=True,
                    data=user,
                    message="Account created successfully",
                )
            else:
                return response
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=str(e),
            )
