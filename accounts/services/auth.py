from functools import wraps
from django.core.exceptions import ObjectDoesNotExist
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate

from accounts.services.profile_service import APIResponse
from base.services.interviews import InterviewService
from base.services.logging import LoggingService
from businesses.models import Company
from businesses.services.business import BusinessService
from candidates.models import Application, Candidate
from candidates.repositories.candidate_repository import CandidateRepository
from candidates.services import candidate
from jobs.models import Job

logging_service = LoggingService()
candidate_service = candidate.CandidateService()
business_service = BusinessService()
candidate_repository = CandidateRepository()
interview_service = InterviewService()


class AuthService:

    def get_token(self, username, password) -> APIResponse:
        try:
            if not username or not password:
                return APIResponse(
                    success=False,
                    data=None,
                    message="Username and password are required",
                    status=status.HTTP_400_BAD_REQUEST,
                )
            user = authenticate(username=username, password=password)

            if user is None:
                return APIResponse(
                    success=False,
                    data=None,
                    message="Invalid credentials",
                    status=status.HTTP_401_UNAUTHORIZED,
                )

            refresh = RefreshToken.for_user(user)
            account_type = None
            account_id = None
            candidate = candidate_repository.get_candidate_by_user_id(user.id)
            business = business_service.get_business_by_created_by(user=user)

            if candidate.success:
                account_type = "candidate"
                account_id = candidate.data.id

            if business.success:
                account_type = "business"
                account_id = business.data.get("slug")

            if not business.success and not candidate.success and not user.is_staff:
                print("Errors: ", candidate.message, business.message)
                return APIResponse(
                    success=False,
                    data=None,
                    message="The account is neither business nor candidate. Contact admin",
                    status=status.HTTP_401_UNAUTHORIZED,
                )

            token_claims = {
                "user_id": user.id,
                "username": user.username,
                "is_staff": user.is_staff,
                "is_active": user.is_active,
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "account_type": account_type,
                "account_id": str(account_id),
            }

            for claim, value in token_claims.items():
                refresh[claim] = value

            for claim, value in token_claims.items():
                refresh.access_token[claim] = value

            return APIResponse(
                success=True,
                data={"refresh": str(refresh), "access": str(refresh.access_token)},
                message="Token successfully generated",
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Failed to generate token",
                status=status.HTTP_400_BAD_REQUEST,
            )

    def get_refresh_token(request):
        refresh_token = request.data.get("refresh")

        if not refresh_token:
            return APIResponse(
                success=False,
                data=None,
                message="Please provide a refresh token",
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            refresh = RefreshToken(refresh_token)

            return APIResponse(
                success=True,
                data={"access": str(refresh.access_token)},
                message="Token successfully generated",
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Invalid refresh token",
                status=status.HTTP_401_UNAUTHORIZED,
            )


class PermissionDecorators:
    @staticmethod
    def is_creator(
        model_class, lookup_field="id", url_param_names=None, fallback_to_user=False
    ):
        """
        Decorator to check if the request user is the creator of a given object.
        """
        if url_param_names is None:
            url_param_names = [lookup_field]

        def decorator(view_func):
            @wraps(view_func)
            def _wrapped_view(request, *args, **kwargs):
                lookup_value = next(
                    (
                        kwargs.get(param)
                        for param in url_param_names
                        if kwargs.get(param) is not None
                    ),
                    None,
                )

                if lookup_value is None:
                    lookup_value = request.data.get(lookup_field)

                if lookup_value is None and fallback_to_user:
                    try:
                        obj = model_class.objects.get(user=request.user)
                        lookup_value = getattr(obj, lookup_field)
                    except ObjectDoesNotExist:
                        return Response(
                            {
                                "success": False,
                                "data": None,
                                "message": f"{model_class.__name__} associated with this user not found.",
                            },
                            status=status.HTTP_404_NOT_FOUND,
                        )

                if not lookup_value:
                    return Response(
                        {
                            "success": False,
                            "data": None,
                            "message": f"Missing parameter for {lookup_field}.",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                try:
                    obj = model_class.objects.get(**{lookup_field: lookup_value})
                    if getattr(obj, "created_by") == request.user:
                        return view_func(request, *args, **kwargs)
                    return Response(
                        {
                            "success": False,
                            "data": None,
                            "message": "Permission denied. You are not the owner of this resource.",
                        },
                        status=status.HTTP_403_FORBIDDEN,
                    )
                except ObjectDoesNotExist:
                    return Response(
                        {
                            "success": False,
                            "data": None,
                            "message": f"{model_class.__name__} not found.",
                        },
                        status=status.HTTP_404_NOT_FOUND,
                    )

            return _wrapped_view

        return decorator


class PermissionValidators:
    @staticmethod
    def has_active_records(model_class, filter_conditions) -> bool:
        """
        Check if a model has records that meet a certain condition.
        Returns True if such records exist, False otherwise.
        """
        return model_class.objects.filter(**filter_conditions).exists()

    @staticmethod
    def can_delete_profile(model_class):
        """
        Decorator to validate if a Candidate or Business profile can be deleted.
        - Candidate: Cannot delete if they have scheduled interviews.
        - Business: Cannot delete if it has active jobs or upcoming interviews.
        """

        def decorator(view_func):
            @wraps(view_func)
            def _wrapped_view(request, *args, **kwargs):
                user = request.user

                if model_class.__name__ == "Candidate":
                    candidate_id = kwargs.get("candidate_id") or None

                    if not candidate_id:
                        try:
                            candidate = model_class.objects.get(user_id=user.id)
                        except ObjectDoesNotExist:
                            return Response(
                                {"success": False, "message": "Candidate not found."},
                                status=status.HTTP_404_NOT_FOUND,
                            )
                    else:
                        try:
                            candidate = model_class.objects.get(id=candidate_id)
                        except ObjectDoesNotExist:
                            return Response(
                                {"success": False, "message": "Candidate not found."},
                                status=status.HTTP_404_NOT_FOUND,
                            )

                    if PermissionValidators.has_active_records(
                        Application, {"applicant": candidate, "status": "Interview"}
                    ):
                        return Response(
                            {
                                "success": False,
                                "message": "Cannot delete profile. You have scheduled interviews.",
                            },
                            status=status.HTTP_403_FORBIDDEN,
                        )

                elif model_class.__name__ == "Company":
                    company_slug = kwargs.get("business_slug")

                    if not company_slug:
                        return Response(
                            {"success": False, "message": "Missing company slug."},
                            status=status.HTTP_400_BAD_REQUEST,
                        )

                    try:
                        company = model_class.objects.get(
                            slug=company_slug, created_by=user
                        )

                        if PermissionValidators.has_active_records(
                            Job, {"company_name": company, "status": "active"}
                        ):
                            return Response(
                                {
                                    "success": False,
                                    "message": "Cannot delete business. There are active jobs.",
                                },
                                status=status.HTTP_403_FORBIDDEN,
                            )

                        interview_response = (
                            interview_service.get_interviews_by_company(company.slug)
                        )
                        if interview_response.success and interview_response.data:
                            return Response(
                                {
                                    "success": False,
                                    "message": "Cannot delete business. There are upcoming interviews.",
                                },
                                status=status.HTTP_403_FORBIDDEN,
                            )

                    except ObjectDoesNotExist:
                        return Response(
                            {"success": False, "message": "Business not found."},
                            status=status.HTTP_404_NOT_FOUND,
                        )

                return view_func(request, *args, **kwargs)

            return _wrapped_view

        return decorator
