from decimal import Decimal
from django.db import models
from django.contrib.auth.models import User
from datetime import datetime, timedelta

from base.models import BaseModel, Package
from businesses.models import Company
from django.db import models
from datetime import timedelta
from django.utils import timezone


class PasswordResetCode(models.Model):
    verification_code = models.CharField(max_length=6)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    date_created = models.DateTimeField(auto_now_add=True)
    expiration_cate = models.DateTimeField(auto_now_add=False)

    def save(self, *args, **kwargs):
        if not self.expiration_cate:
            self.expiration_cate = datetime.now() + timedelta(minutes=1)

        super().save(*args, **kwargs)


class Subscription(BaseModel):
    user = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name="user_subscriptions",
    )
    sub_company = models.ForeignKey(
        Company,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
    )
    sub_package = models.ForeignKey(
        Package,
        on_delete=models.PROTECT,
        related_name="package_subscriptions",
    )
    start_date = models.DateTimeField(auto_now_add=True)
    end_date = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=False)

    def save(self, *args, **kwargs):
        # Ensure end_date is a datetime object if it's a string
        if self.end_date and isinstance(self.end_date, str):
            from django.utils.dateparse import parse_datetime, parse_date
            from django.utils import timezone

            dt = parse_datetime(self.end_date)
            if not dt:
                dt = parse_date(self.end_date)
                if dt:
                    from datetime import datetime

                    dt = datetime.combine(dt, datetime.min.time())
            if dt:
                if timezone.is_naive(dt):
                    dt = timezone.make_aware(dt)
                self.end_date = dt
        if not self.end_date and self.sub_package.is_active:
            self.end_date = self.start_date + timedelta(days=30)
        # Ensure start_date is always aware
        if (
            self.start_date
            and hasattr(self.start_date, "tzinfo")
            and self.start_date.tzinfo is None
        ):
            from django.utils import timezone

            self.start_date = timezone.make_aware(self.start_date)
        super().save(*args, **kwargs)

    def is_expired(self):
        return self.end_date and self.end_date < timezone.now()

    def get_total_months(self):
        if self.end_date:
            return (self.end_date - self.start_date).days // 30
        return 0


class Invoice(BaseModel):
    INVOICE_STATUS_CHOICES = (
        ("pending", "Pending"),
        ("paid", "Paid"),
        ("cancelled", "Cancelled"),
        ("overdue", "Overdue"),
        ("partial", "Partial"),
        ("refunded", "Refunded"),
    )
    inv_subscription = models.ForeignKey(
        Subscription,
        on_delete=models.PROTECT,
        related_name="invoice_subscriptions",
    )
    invoice_number = models.CharField(max_length=100, unique=True)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    due_date = models.DateTimeField(null=True, blank=True)
    date_paid = models.DateTimeField(null=True, blank=True)
    status = models.CharField(
        max_length=20,
        choices=INVOICE_STATUS_CHOICES,
        default="pending",
    )
    payment_method = models.CharField(max_length=50, blank=True, null=True)
    subtotal = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    tax = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)

    def save(self, *args, **kwargs):
        if not self.invoice_number:
            today = timezone.now().strftime("%Y%m%d")
            count = Invoice.objects.filter(
                invoice_number__startswith=f"INV-{today}"
            ).count()
            self.invoice_number = f"INV-{today}-{count + 1:03d}"
        self.due_date = timezone.now() + timedelta(days=15)
        self.tax = self.subtotal * Decimal("0.18")  # 18% tax rate
        self.total_amount = self.subtotal + self.tax

        if self.status == "paid":
            self.date_paid = timezone.now()
        elif self.status in ["cancelled", "refunded"]:
            self.date_paid = None
        super().save(*args, **kwargs)
