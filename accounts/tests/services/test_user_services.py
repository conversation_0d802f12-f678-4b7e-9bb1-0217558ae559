from django.test import TestCase, override_settings
from django.contrib.auth.models import User, Group
from django.core.cache import cache

from accounts.repositories.user_repository import UserRepository
from accounts.services.permission_services import PermissionsService
from accounts.services.user_services import UserService
from candidates.models import Candidate
from businesses.models import Company

@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class UserServiceCachingTestCase(TestCase):
    def setUp(self):
        cache.clear()
        
        self.user_service = UserService()
        self.user_repository = UserRepository()
        
        self.group = Group.objects.create(name="Super user")

        self.admin = User.objects.create_user(
            username="admin_user",
            email="<EMAIL>",
            password="admin_password",
        )
        self.admin.groups.add(self.group)
        self.admin.is_admin = True
        self.admin.save()
        self.normal_user = User.objects.create_user(
            username="normal_user",
            email="<EMAIL>",
            password="normal_password",
        )
        
        self.user_data = {
            "username": "test_user",
            "email": "<EMAIL>",
            "password": "test_password",
            "first_name": "Test",
            "last_name": "User",
        }
    
    def tearDown(self):
        cache.clear()
    
    def test_create_user_with_caching(self):
        """Test that creating a user also updates the cache"""
        response = self.user_service.create_user(self.user_data, self.admin)
        
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        
        cached_users = cache.get("users")
        self.assertIsNotNone(cached_users)
        self.assertTrue(any(u['username'] == self.user_data['username'] for u in cached_users))
        
        user_id = response.data.id
        cached_user = cache.get(f"user_{user_id}")
        self.assertIsNotNone(cached_user)
        self.assertEqual(cached_user['username'], self.user_data['username'])
    
    def test_get_user_by_id_from_cache(self):
        """Test retrieving a user from cache"""
        create_response = self.user_service.create_user(self.user_data, self.admin)
        user_id = create_response.data.id
        
        first_response = self.user_service.get_user_by_id(user_id)
        self.assertTrue(first_response.success)
        
        User.objects.filter(id=user_id).delete()
        
        second_response = self.user_service.get_user_by_id(user_id)
        
        self.assertTrue(second_response.success)
        self.assertEqual(second_response.data['username'], self.user_data['username'])
    
    def test_update_user_updates_cache(self):
        """Test that updating a user also updates the cache"""
        create_response = self.user_service.create_user(self.user_data, self.admin)
        user_id = create_response.data.id
        
        updated_data = {
            "first_name": "Updated",
            "last_name": "Name"
        }
        
        update_response = self.user_service.update_user_by_id(user_id, updated_data, create_response)

        if isinstance(update_response, tuple):
            success = update_response[0]
        else:
            success = update_response.success
        
        print("Update response: ", update_response)
        self.assertTrue(success)
        
        get_response = self.user_service.get_user_by_id(user_id)

        cached_user = cache.get(f"user_{user_id}")
        if cached_user:
            self.assertEqual(cached_user['first_name'], "Updated")
            self.assertEqual(cached_user['last_name'], "Name")
        
        cached_users = cache.get("users")
        if cached_users:
            updated_user = next((u for u in cached_users if u['id'] == user_id), None)
            if updated_user:
                self.assertEqual(updated_user['first_name'], "Updated")
    
    def test_delete_user_removes_from_cache(self):
        """Test that deleting a user removes it from cache"""
        create_response = self.user_service.create_user(self.user_data, self.admin)
        user_id = create_response.data.id
        
        self.user_service.get_user_by_id(user_id)
        
        self.assertIsNotNone(cache.get(f"user_{user_id}"))
        
        delete_response = self.user_service.delete_user_by_id(user_id, self.admin)
        self.assertTrue(delete_response.success)
        
        self.assertIsNone(cache.get(f"user_{user_id}"))
        
        cached_users = cache.get("users")
        if cached_users:
            self.assertFalse(any(u['id'] == user_id for u in cached_users))
    
    def test_get_all_users_from_cache(self):
        """Test retrieving all users from cache"""
        self.user_service.create_user(self.user_data, self.admin)
        self.user_service.create_user({
            "username": "another_user",
            "email": "<EMAIL>",
            "password": "password123",
            "first_name": "Another",
            "last_name": "User"
        }, self.admin)
        
        first_response = self.user_service.get_all_users()
        self.assertTrue(first_response.success)
        
        User.objects.all().delete()
        
        second_response = self.user_service.get_all_users()
        
        self.assertTrue(second_response.success)
        self.assertTrue(len(second_response.data) >= 2)
    
    def test_sign_up_candidate_with_caching(self):
        """Test that signing up a candidate also updates the cache"""
        signup_data = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "first_name": "Candidate",
            "last_name": "User",
            "account_type": "candidate",
            "phone_number": "**********",
            "gender": "male",
            "date_of_birth": "2000-01-01",
        }
        
        response = self.user_service.sign_up(signup_data)
        self.assertTrue(response.success)
        
        cached_users = cache.get("users")
        if cached_users:
            self.assertTrue(any(u['email'] == signup_data['email'] for u in cached_users))
        
        cached_candidates = cache.get("candidates")
        if cached_candidates:
            self.assertTrue(any(c['phone_number'] == signup_data['phone_number'] for c in cached_candidates))
    
    def test_sign_up_business_with_caching(self):
        """Test that signing up a business also updates the cache"""
        signup_data = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "first_name": "Business",
            "last_name": "Owner",
            "account_type": "business",
            "phone_number": "**********",
            "gender": "female",
            "date_of_birth": "1990-01-01",
            "business_name": "Test Business"
        }
        
        response = self.user_service.sign_up(signup_data)
        self.assertTrue(response.success)
        
        cached_users = cache.get("users")
        if cached_users:
            self.assertTrue(any(u['email'] == signup_data['email'] for u in cached_users))
        
        cached_businesses = cache.get("businesses")
        if cached_businesses:
            self.assertTrue(any(b['name'] == signup_data['business_name'] for b in cached_businesses))

# testing the accounts repositories


# user repository


class UserRepositoryTestCase(TestCase):
    def setUp(self):
        # creating test users
        self.user_repository = UserRepository()
        self.user_data = {
            "username": "test_user",
            "email": "<EMAIL>",
            "password": "test_password",
            "first_name": "Test",
            "last_name": "User",
        }

    def test_create_user(self):
        response = self.user_repository.create_user(self.user_data)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

    def test_create_user_with_existing_email(self):
        # creating a user first
        self.user_repository.create_user(self.user_data)

        # trying to create a user with the same email
        response = self.user_repository.create_user(self.user_data)
        self.assertFalse(response.success)

    # update user
    def test_update_user(self):
        # creating a user first
        user = self.user_repository.create_user(self.user_data).data
        updated_user_data = {
            "username": "updated_user",
            "email": "<EMAIL>",
            "password": "updated_password",
        }

        response = self.user_repository.update_user(user.id, updated_user_data)
        self.assertTrue(response.success)
        self.assertEqual(response.data.username, updated_user_data["username"])

    def test_update_user_with_non_existing_id(self):
        # trying to update a user with a non-existing id
        response = self.user_repository.update_user(999, {})
        self.assertFalse(response.success)

    # delete user
    def test_delete_user(self):
        # creating a user first
        user = self.user_repository.create_user(self.user_data).data

        response = self.user_repository.delete_user(user.id)
        self.assertTrue(response.success)

        # trying to delete a user with a non-existing id
        response = self.user_repository.delete_user(999)
        self.assertFalse(response.success)

    # get user
    def test_get_user(self):
        # creating a user first
        user = self.user_repository.create_user(self.user_data).data

        response = self.user_repository.get_user(user.id)
        self.assertTrue(response.success)
        self.assertEqual(response.data.username, self.user_data["username"])

    def test_get_user_with_non_existing_id(self):
        # trying to get a user with a non-existing id
        response = self.user_repository.get_user(999)
        self.assertFalse(response.success)
        self.assertIsNone(response.data)

    # deactivate user
    def test_deactivate_user(self):
        # creating a user first
        user = self.user_repository.create_user(self.user_data).data

        response = self.user_repository.deactivate_user(user.id)
        self.assertTrue(response.success)
        self.assertFalse(response.data.is_active)

        # trying to deactivate a user with a non-existing id
        response = self.user_repository.deactivate_user(999)
        self.assertFalse(response.success)

    # activate user
    def test_activate_user(self):
        # creating a user first
        self.user_data["is_active"] = False
        user = self.user_repository.create_user(self.user_data).data
        self.assertFalse(user.is_active)

        response = self.user_repository.activate_user(user.id)
        self.assertTrue(response.success)
        self.assertTrue(response.data.is_active)

        # trying to activate a user with a non-existing id
        response = self.user_repository.activate_user(999)
        self.assertFalse(response.success)

    # get all users
    def test_get_all_users(self):
        user = self.user_repository.create_user(self.user_data).data
        response = self.user_repository.get_all_users()
        self.assertTrue(response.success)
        self.assertTrue(len(response.data) > 0)


# permissions


class UserServiceTestCase(TestCase):
    def setUp(self):
        # create a group
        self.group = Group.objects.create(name="Super user")

        # create an admin user
        self.user = User.objects.create_user(
            username="test_user",
            email="<EMAIL>",
            password="test_password",
        )

        # add the user to the group
        self.user.groups.add(self.group)

        # create a normal user
        self.normal_user = User.objects.create_user(
            username="normal_user",
            email="<EMAIL>",
            password="normal_password",
        )

        self.permissions_service = PermissionsService()
        self.user_services = UserService()

    # test creating user as admin with roles in data
    def test_create_user_as_admin(self):
        user_data = {
            "username": "test_user1",
            "email": "<EMAIL>",
            "password": "test_password",
            "roles": ["Super user"],
        }
        response = self.user_services.create_user(user_data, self.user)
        # Assert that the user was created successfully
        self.assertTrue(response.success)
        self.assertTrue(self.group in response.data.groups.all())

    # test creating a user without admin role

    def test_user_without_admin_role(self):
        user_data = {
            "username": "test_user1",
            "email": "<EMAIL>",
            "password": "test_password",
            "roles": ["Super user"],
        }

        response = self.user_services.create_user(user_data, self.normal_user)
        self.assertFalse(response.success)

    def test_sign_up_candidate(self):
        """Test the sign_up method for candidate account type."""
        signup_data = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "first_name": "Candidate",
            "last_name": "User",
            "account_type": "candidate",
            "phone_number": "**********",
            "gender": "male",
            "date_of_birth": "2000-01-01",
        }

        response = self.user_services.sign_up(signup_data)

        # Assert the response was successful
        self.assertTrue(response.success)

        # Assert the user was created
        created_user = User.objects.filter(email=signup_data["email"]).first()
        self.assertIsNotNone(created_user)
        self.assertEqual(created_user.first_name, signup_data["first_name"])
        self.assertEqual(created_user.last_name, signup_data["last_name"])

        # Assert the Candidate profile was created
        candidate_profile = Candidate.objects.filter(user=created_user).first()
        self.assertIsNotNone(candidate_profile)
        self.assertEqual(candidate_profile.phone_number, signup_data["phone_number"])
        self.assertEqual(candidate_profile.gender, signup_data["gender"])

    def test_sign_up_missing_field(self):
        """Test the sign_up method with missing required fields."""
        signup_data = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            # Missing first_name, last_name, and other required fields
        }

        response = self.user_services.sign_up(signup_data)

        # Assert the response was unsuccessful
        self.assertFalse(response.success)
        self.assertIn("Missing required field:", response.message)

    def test_sign_up_existing_user(self):
        """Test the sign_up method when a user already exists."""
        # Pre-create a user with the same email
        User.objects.create_user(
            username="<EMAIL>",
            email="<EMAIL>",
            password="securepassword123",
        )

        signup_data = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "first_name": "Candidate",
            "last_name": "User",
            "account_type": "candidate",
            "phone_number": "**********",
            "gender": "male",
            "date_of_birth": "2000-01-01",
        }

        response = self.user_services.sign_up(signup_data)

        # Assert the response was unsuccessful
        self.assertFalse(response.success)

    # test get user by id
    def test_get_user_by_id(self):

        response = self.user_services.get_user_by_id(self.normal_user.id)
        self.assertTrue(response.success)

    # test get user by id with non-existing id
    def test_get_user_by_id_non_existing(self):
        response = self.user_services.get_user_by_id(999)
        self.assertFalse(response.success)

    # delete user
    def test_delete_user(self):
        response = self.user_services.delete_user_by_id(self.normal_user.id, self.user)
        self.assertTrue(response.success)
