from django.test import TestCase
from django.contrib.auth.models import User
from accounts.models import Subscription, Package
from businesses.models import Company
from accounts.services.permission_services import PermissionsService


class TestIsEligibleToPostJob(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(username="owner", password="testpass")
        self.other_user = User.objects.create_user(
            username="other", password="testpass"
        )
        self.company = Company.objects.create(name="<PERSON>Co", created_by=self.user)
        self.package = Package.objects.create(name="Basic", has_limit=False, price=100)
        self.subscription = Subscription.objects.create(
            user=self.user,
            sub_company=self.company,
            sub_package=self.package,
            is_active=True,
        )
        self.permission_service = PermissionsService()

    def test_user_is_owner_and_has_active_subscription_and_no_limit(self):
        response = self.permission_service.is_eligible_to_post_job(
            self.user, self.company
        )
        self.assertTrue(response.success)

    def test_user_is_not_owner(self):
        response = self.permission_service.is_eligible_to_post_job(
            self.other_user, self.company
        )
        self.assertFalse(response.success)

    def test_company_has_no_active_subscription(self):
        self.subscription.is_active = False
        self.subscription.save()
        response = self.permission_service.is_eligible_to_post_job(
            self.user, self.company
        )
        self.assertFalse(response.success)

    def test_company_has_reached_job_posting_limit(self):
        self.package.has_limit = True
        self.package.limit = 1
        self.package.save()
        self.subscription.sub_package = self.package
        self.subscription.save()
        # Simulate current_job_postings at limit
        self.package.current_job_postings = 1
        self.package.save()  # Ensure the value is saved to the DB
        response = self.permission_service.is_eligible_to_post_job(
            self.user, self.company
        )
        self.assertFalse(response.success)
