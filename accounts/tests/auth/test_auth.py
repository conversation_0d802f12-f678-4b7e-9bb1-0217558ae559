from django.test import TestCase

from accounts.services.auth import AuthService
from accounts.services.user_services import UserService
from base.services.caching import CachingService
from businesses.models import Company
from candidates.models import Candidate


class ResisterTestCase(TestCase):
    def setUp(self):
        self.service = UserService()
        self.valid_data = {
            "first_name": "Test",
            "last_name": "User",
            "email": "<EMAIL>",
            "password": "password123",
            "gender": "Male",
            "date_of_birth": "1990-01-01",
            "phone_number": "**********",
        }

    # test register candidate
    def test_register_candidate(self):
        data = self.valid_data.copy()
        data["account_type"] = "candidate"

        response = self.service.sign_up(data)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        # check if candidate is in the database

        candidate = Candidate.objects.get(user__email=self.valid_data["email"])
        self.assertIsNotNone(candidate)
        self.assertEqual(candidate.user.first_name, self.valid_data["first_name"])
        self.assertEqual(candidate.user.email, self.valid_data["email"])

        # check if user is cached
        cached_users = CachingService().getCache("candidates")
        print("Cached Users:", cached_users)

    def test_register_business(self):
        data = self.valid_data.copy()
        data["account_type"] = "business"
        data["business_name"] = "Test Business"

        response = self.service.sign_up(data)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

        # check if business is in the database
        business = Company.objects.get(name=data["business_name"])
        self.assertIsNotNone(business)

    def test_register_business_no_name(self):
        data = self.valid_data.copy()
        data["account_type"] = "business"

        response = self.service.sign_up(data)
        self.assertFalse(response.success)
