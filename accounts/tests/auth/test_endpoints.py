from os import name
from rest_framework.test import APIClient
from django.test import TestCase
from django.contrib.auth.models import User
from businesses.models import Company
from candidates.models import Candidate
from django.contrib.auth.hashers import make_password
from django.urls import reverse
import jwt
from django.conf import settings


class TestRegisterAuthEndpointsTestCase(TestCase):
    def setUp(self):

        self.client = APIClient()
        self.endpoint = "/api/accounts"

        self.valid_data = {
            "first_name": "Test",
            "last_name": "User",
            "email": "<EMAIL>",
            "password": "password123",
            "gender": "Male",
            "date_of_birth": "1990-01-01",
            "phone_number": "**********",
        }

    def test_register_as_candidate(self):
        data = self.valid_data.copy()
        data["account_type"] = "candidate"

        response = self.client.post(f"{self.endpoint}/register/", data)
        self.assertEqual(response.status_code, 201)

    def test_register_as_business(self):
        data = self.valid_data.copy()
        data["account_type"] = "business"
        data["business_name"] = "Test business"

        print("Data before posting: ", data)

        response = self.client.post(f"{self.endpoint}/register/", data, format="json")
        self.assertEqual(response.status_code, 201)

    def test_register_as_business_no_name(self):
        data = self.valid_data.copy()
        data["account_type"] = "business"

        print("Data before posting: ", data)

        response = self.client.post(f"{self.endpoint}/register/", data, format="json")
        self.assertNotEqual(response.status_code, 201)


class CandidateLoginTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.login_url = reverse("login_view")

        # Test user data
        self.user_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "first_name": "Test",
            "last_name": "User",
            "gender": "Male",
            "date_of_birth": "1990-01-01",
            "phone_number": "**********",
        }

        # Create user with properly hashed password
        self.user = User.objects.create(
            username=self.user_data["email"],
            email=self.user_data["email"],
            password=make_password(self.user_data["password"]),
            first_name=self.user_data["first_name"],
            last_name=self.user_data["last_name"],
        )

        # Create associated candidate profile
        self.candidate = Candidate.objects.create(
            user=self.user,
            created_by=self.user,
            name="Candidate Profile",
        )

    def test_successful_login(self):
        """Test successful login with correct credentials"""
        credentials = {
            "username": self.user_data["email"],
            "password": self.user_data["password"],
        }
        response = self.client.post(self.login_url, credentials)
        print(response.data)
        self.assertEqual(response.status_code, 200)
        self.assertIn("access", response.data)

    def test_login_wrong_password(self):
        """Test login failure with incorrect password"""
        credentials = {"username": self.user_data["email"], "password": "wrongpassword"}
        response = self.client.post(self.login_url, credentials)

        self.assertEqual(response.status_code, 401)
        self.assertIn("error", response.data)

    def test_token_claims(self):
        """Test JWT token claims for candidate login"""
        credentials = {
            "username": self.user_data["email"],
            "password": self.user_data["password"],
        }
        response = self.client.post(self.login_url, credentials)

        self.assertEqual(response.status_code, 200)

        # Decode access token
        access_token = response.data["access"]
        decoded_token = jwt.decode(
            access_token, settings.SECRET_KEY, algorithms=["HS256"]
        )

        # Verify candidate claims
        expected_claims = {
            "user_id": self.user.id,
            "username": self.user.username,
            "is_staff": self.user.is_staff,
            "is_active": self.user.is_active,
            "email": self.user.email,
            "first_name": self.user.first_name,
            "last_name": self.user.last_name,
            "account_type": "candidate",
            "account_id": str(self.candidate.id),
        }

        for claim, expected_value in expected_claims.items():
            self.assertIn(claim, decoded_token)
            self.assertEqual(decoded_token[claim], expected_value)


class BusinessLoginTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.login_url = reverse("login_view")

        # Test user data
        self.user_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "first_name": "Test",
            "last_name": "User",
            "gender": "Male",
            "date_of_birth": "1990-01-01",
            "phone_number": "**********",
        }

        # Create user with properly hashed password
        self.user = User.objects.create(
            username=self.user_data["email"],
            email=self.user_data["email"],
            password=make_password(self.user_data["password"]),
            first_name=self.user_data["first_name"],
            last_name=self.user_data["last_name"],
        )

        # Create associated business profile
        self.business = Company.objects.create(
            created_by=self.user, name="Business Profile"
        )

    def test_successful_login(self):
        """Test successful login with correct credentials"""
        credentials = {
            "username": self.user_data["email"],
            "password": self.user_data["password"],
        }
        response = self.client.post(self.login_url, credentials)

        self.assertEqual(response.status_code, 200)
        self.assertIn("access", response.data)

    def test_login_wrong_password(self):
        """Test login failure with incorrect password"""
        credentials = {"username": self.user_data["email"], "password": "wrongpassword"}
        response = self.client.post(self.login_url, credentials)

        self.assertEqual(response.status_code, 401)
        self.assertIn("error", response.data)

    def test_token_claims(self):
        """Test JWT token claims for business login"""
        # Update user's company association
        self.user.company = self.business
        self.user.save()

        credentials = {
            "username": self.user_data["email"],
            "password": self.user_data["password"],
        }
        response = self.client.post(self.login_url, credentials)

        self.assertEqual(response.status_code, 200)

        # Decode access token
        access_token = response.data["access"]
        decoded_token = jwt.decode(
            access_token, settings.SECRET_KEY, algorithms=["HS256"]
        )

        # Verify business claims
        expected_claims = {
            "user_id": self.user.id,
            "username": self.user.username,
            "is_staff": self.user.is_staff,
            "is_active": self.user.is_active,
            "email": self.user.email,
            "first_name": self.user.first_name,
            "last_name": self.user.last_name,
            "account_type": "business",
            "account_id": str(self.business.id),
        }

        for claim, expected_value in expected_claims.items():
            self.assertIn(claim, decoded_token)
            self.assertEqual(decoded_token[claim], expected_value)
