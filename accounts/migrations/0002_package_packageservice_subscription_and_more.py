# Generated by Django 4.2.17 on 2025-04-03 10:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('base', '0008_remove_subscriptiontype_basemodel_ptr_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('businesses', '0006_alter_company_business_address_and_more'),
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Package',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('description', models.TextField(max_length=1000)),
                ('is_active', models.BooleanField(default=False)),
            ],
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='PackageService',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('description', models.TextField(blank=True, max_length=500)),
                ('is_active', models.BooleanField(default=True)),
            ],
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='Subscription',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('start_date', models.DateTimeField(auto_now_add=True)),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('sub_company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='businesses.company')),
                ('sub_package', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='package_subscriptions', to='accounts.package')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='user_subscriptions', to=settings.AUTH_USER_MODEL)),
            ],
            bases=('base.basemodel',),
        ),
        migrations.AddField(
            model_name='package',
            name='pac_services',
            field=models.ManyToManyField(blank=True, related_name='service_packages', to='accounts.packageservice'),
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('invoice_number', models.CharField(max_length=100, unique=True)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('date_paid', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('paid', 'Paid'), ('cancelled', 'Cancelled'), ('overdue', 'Overdue'), ('partial', 'Partial'), ('refunded', 'Refunded')], default='pending', max_length=20)),
                ('payment_method', models.CharField(blank=True, max_length=50, null=True)),
                ('inv_subscription', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='invoice_subscriptions', to='accounts.subscription')),
            ],
            bases=('base.basemodel',),
        ),
    ]
