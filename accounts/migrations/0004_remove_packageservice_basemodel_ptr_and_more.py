# Generated by Django 4.2.17 on 2025-04-03 18:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('base', '0009_packageservice_package'),
        ('accounts', '0003_alter_subscription_is_active'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='packageservice',
            name='basemodel_ptr',
        ),
        migrations.AlterField(
            model_name='subscription',
            name='sub_package',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='package_subscriptions', to='base.package'),
        ),
        migrations.DeleteModel(
            name='Package',
        ),
        migrations.DeleteModel(
            name='PackageService',
        ),
    ]
