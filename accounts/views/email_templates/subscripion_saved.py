# Define the HTML content as a variable with placeholders
from base._views.emails import send_email


subscription_saved_email_content = """
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<title>Welcome to The KPI Tool</title>
</head>
<body style="font-family: Arial, sans-serif;">

<table cellpadding="0" cellspacing="0" border="0" width="100%">
<tr>
    <td style="padding: 20px;">
        <p>Dear {first_name},</p>
        <p>Welcome to CSR jobMatch! We are thrilled to have you on board.</p>
        <p>Your subscription have been saved. Once the platform is ready, we will inform you. </p>
        
        <p>If you have any questions or need further assistance, feel free to contact our support <NAME_EMAIL></p>
        <p>Best regards,<br/> CSR jobMatch team</p>
    </td>
</tr>
</table>

</body>
</html>
"""


def send_subscription_saved_email(recipient_email, first_name):
    subject = "Welcome to CSR jobMatch!"
    html_body = subscription_saved_email_content.format(
        first_name=first_name,
    )
    data = {
        "first_name_Value": first_name,
        "email_Value": recipient_email,
    }
    send_email(
        recipient_email=recipient_email, data=data, html_body=html_body, subject=subject
    )
