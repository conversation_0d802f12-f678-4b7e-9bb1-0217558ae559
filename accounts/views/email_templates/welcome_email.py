# Define the HTML content as a variable with placeholders
welcome_email_content = """
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<title>Welcome to The KPI Tool</title>
</head>
<body style="font-family: Arial, sans-serif;">

<table cellpadding="0" cellspacing="0" border="0" width="100%">
<tr>
    <td style="padding: 20px;">
        <p>Dear {first_name},</p>
        <p>Welcome to CSR jobMatch! We are thrilled to have you on board.</p>
        <p>Your information have been saved, and an account has been created for you. Once the platform is ready, you will login with: </p>
        <ul>
            <li><strong>Username:</strong> {email}</li>
            <li><strong>Password:</strong> [The password you used registering]</li>
        </ul>
        <p>You will access your account via the following link:</p>
        <p><a href="{login_link}">Login Now</a></p>
        <p>If you have any questions or need further assistance, feel free to contact our support <NAME_EMAIL></p>
        <p>Best regards,<br/> CSR jobMatch team</p>
    </td>
</tr>
</table>

</body>
</html>
"""
