from django.contrib.auth.models import User
from django.contrib.auth.models import User
from candidates.models import (
    Skill,
    Candidate,
)


import jwt
from django.conf import settings
from core.settings import SECRET_KEY
import jwt

from rest_framework import serializers


class SkillSerializer(serializers.ModelSerializer):
    class Meta:
        model = Skill
        fields = "__all__"


from candidates.models import (
    Candidate,
    Skill,
)

from candidates.views.serializers import CandidateDataSerializer
from django.conf import settings
from core.settings import SECRET_KEY
from accounts.views.email_templates.welcome_email import welcome_email_content
from accounts.views.email_templates.profile_complete import profile_complete
from base.services.caching import CachingService

from base._views.emails import send_email


def verify_user(access_token_with_bearer):

    if not isinstance(access_token_with_bearer, str):
        user = None
        message = "Invalid token"
        return (user, message)
    try:
        access_token = access_token_with_bearer.split()[1]
        decoded_token = jwt.decode(access_token, SECRET_KEY, algorithms=["HS256"])
        user_id = decoded_token.get("user_id")
        user = User.objects.get(pk=user_id)
        message = "User is authenticated"
        return (user, message)
    except jwt.ExpiredSignatureError:
        user = None
        message = "Token has expired"
        return (user, message)
    except jwt.InvalidTokenError:
        user = None
        message = "Invalid token"
        return (user, message)
    except User.DoesNotExist:
        user = None
        message = "User not found"
        return (user, message)


# get_profile_data


def get_profile_data(user):
    caching = CachingService()
    cache_key = f"profile_data_user:{user.id}"

    cached_data = caching.getCache(cache_key)
    if cached_data:
        return cached_data

    candidate_data = Candidate.objects.get(user=user)
    candidate = CandidateDataSerializer(candidate_data)
    serialized_data = candidate.data

    caching.setCache(cache_key, serialized_data)

    return serialized_data


def send_welcome_email(recipient_email, first_name, login_link):
    subject = "Welcome to CSR jobMatch"
    html_body = welcome_email_content.format(
        first_name=first_name,
        email=recipient_email,
        login_link=login_link,
    )
    data = {
        "first_name_Value": first_name,
        "email_Value": recipient_email,
    }
    send_email(
        recipient_email=recipient_email, data=data, html_body=html_body, subject=subject
    )


def send_profile_complete_email(recipient_email, first_name, profile_link):
    subject = "Profile complete"

    html_body = profile_complete.format(
        first_name=first_name, profile_link=profile_link
    )

    data = {"first_name_value": first_name, "profile_link": profile_link}
    send_email(
        recipient_email=recipient_email, data=data, html_body=html_body, subject=subject
    )
