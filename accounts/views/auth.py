from django.contrib.auth.models import User
from django.http import JsonR<PERSON>ponse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.models import User
from accounts import serializers
from accounts.services.auth import PermissionDecorators
from accounts.views.services import verify_user
from businesses.models import Company
from businesses.services.business import BusinessService
from candidates.models import Candidate
from candidates.services import candidate
from candidates.views.serializers import (
    CandidateDataSerializer,
    GetCandidateSerializer,
)
from django.contrib.auth import login, authenticate
from django.contrib.auth.hashers import make_password
from rest_framework_simplejwt.tokens import RefreshToken
import secrets
import string
from accounts.models import PasswordResetCode
from base.utils.email_service import EmailService
from base.services.logging import LoggingService

from base.email_template.send_verification_code import send_verification_code_email
from base.email_template.send_reset_password_code import send_reset_password_code_email
from base.utils.generate_verification_code import generate_verification_code
from django_ratelimit.decorators import ratelimit
from base.services.caching import CachingService


candidate_service = candidate.CandidateService()
business_service = BusinessService()
email_service = EmailService()
logging_service = LoggingService()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def user_info(request):
    if request.method == "GET":

        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        print("User id: ", user.id)
        if user is not None:
            refresh = RefreshToken.for_user(user)

            user_data = {
                "username": user.username,
                "first_name": user.first_name,
                "last_name": user.last_name,
            }
            account_type = None
            response_data = None
            if Candidate.objects.filter(user=user).exists():
                account_type = "candidate"
                candidate = candidate_service.get_candidate(user_id=user.id)
                if not candidate.success:
                    return Response({"message": candidate.message}, status=400)

                response_data = GetCandidateSerializer(candidate.data).data
            if Company.objects.filter(created_by=user).exists():
                account_type = "business"
                """
                Get the company using company service and set the value of response_data to the results"""

            return Response(
                {"user": response_data, "account_type": account_type},
                status=status.HTTP_200_OK,
            )
        else:
            return Response({"message": "Invalid credentials"}, status=401)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def candidate_profile_data(request, candidate_id):
    if request.method == "GET":
        caching = CachingService()
        cache_key = f"candidate_profile:{candidate_id}"

        cached_data = caching.getCache(cache_key)
        if cached_data:
            return Response(cached_data, status=status.HTTP_200_OK)

        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        try:
            if user is not None:
                refresh = RefreshToken.for_user(user)

                user_data = {
                    "username": user.username,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                }
                account_type = None
                response_data = None
                if Candidate.objects.filter(id=candidate_id).exists():
                    account_type = "candidate"
                    candidate = Candidate.objects.get(id=candidate_id)

                    response_data = GetCandidateSerializer(candidate).data
                if Company.objects.filter(created_by=user).exists():
                    account_type = "business"
                    """
                    Get the company using company service and set the value of response_data to the results"""

                result = {"user": response_data, "account_type": account_type}

                caching.setCache(cache_key, result)

                return Response(result, status=status.HTTP_200_OK)
            else:
                return Response({"message": "Invalid credentials"}, status=401)
        except Candidate.DoesNotExist:
            return Response(
                {"message": "No candidate found"}, status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logging_service.log_error(e)
            return Response(
                {"message": "Failed to retrieve candidate profile data"},
                status=status.HTTP_400_BAD_REQUEST,
            )


@api_view(["POST"])
@permission_classes([AllowAny])
@ratelimit(key="ip", rate="10/m", method="POST", block=False)
def verify_email(request):

    if request.method == "POST":
        email = request.data.get("email")
        if email is not None:
            try:
                user = User.objects.get(email=email)
                verification_code = generate_verification_code(user)
                first_name = user.first_name
                try:
                    send_verification_code_email(email, first_name, verification_code)
                    return Response(
                        {
                            "status": "success",
                            "message": "Email is verified",
                            "email": email,
                        },
                        status=200,
                    )
                except Exception as e:
                    logging_service.log_error(e)
                    return Response(
                        {
                            "status": "fail",
                            "message": "We could not send you an email",
                        },
                        status=400,
                    )

            except User.DoesNotExist:
                return Response(
                    {
                        "status": "fail",
                        "message": "We could not find a user with that email",
                    },
                    status=400,
                )
        else:
            return Response(
                {"status": "fail", "message": "Email is not verified"}, status=400
            )


@api_view(["POST"])
@permission_classes([AllowAny])
@ratelimit(key="ip", rate="10/m", method="POST", block=False)
def verify_code(request):
    if request.method == "POST":
        verification_code = request.data.get("code")
        email = request.data.get("email")
        password = request.data.get("password")
        if all([verification_code, email, password]):
            try:
                user = User.objects.get(email=email)
                saved_code_obj = PasswordResetCode.objects.filter(
                    verification_code=verification_code, user=user
                ).first()
                saved_code = saved_code_obj.verification_code
                if str(verification_code) == saved_code:
                    new_password = make_password(password)
                    user.password = new_password
                    user.save()
                    saved_code_obj.delete()
                    return Response(
                        {"status": "success", "message": "Code is verified"}, status=200
                    )
                else:
                    return Response(
                        {"status": "fail", "message": "invalid or expired code"},
                        status=400,
                    )
            except Exception as e:
                logging_service.log_error(e)
                return Response({"status": "fail"}, status=400)

        else:
            return Response(
                {"status": "fail", "message": "All fields are quired"}, status=400
            )


@api_view(["POST"])
@permission_classes([AllowAny])
@ratelimit(key="ip", rate="10/m", method="POST", block=False)
def reset_password(request):
    if request.method == "POST":
        email = request.data.get("email")
        if email is not None:
            try:
                user = User.objects.get(email=email)
                first_name = user.first_name
                verification_code = generate_verification_code(user)
                try:
                    send_reset_password_code_email(email, first_name, verification_code)
                    # print("Verification code is: ", verification_code)
                    return Response(
                        {
                            "status": "success",
                            "message": "Password reset code is sent to your email",
                            "email": email,
                        },
                        status=200,
                    )
                except Exception as e:
                    logging_service.log_error(e)
                    return Response(
                        {
                            "status": "fail",
                            "message": "We could not send you an email",
                        },
                        status=400,
                    )
            except User.DoesNotExist:
                return Response(
                    {
                        "status": "fail",
                        "message": "We could not find a user with that email",
                    },
                    status=400,
                )
