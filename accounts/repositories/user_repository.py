"""
    Repository layer - focuses purely on data access patterns
    No business logic, no caching, just database operations
    """

from django.contrib.auth.models import User
from dataclasses import dataclass
from typing import Union, Optional, Dict, List
from django.db.models import Model
from django.db import transaction


@dataclass
class RepositoryResponse:
    success: bool
    message: str
    data: Optional[Union[User, Dict, List, Model]]


class UserRepository:
    """Create a new user"""

    @transaction.atomic
    def create_user(self, data):

        # Check if a user with the same email already exists
        if User.objects.filter(email=data.get("email")).exists():
            return RepositoryResponse(
                success=False,
                data=None,
                message="Email already exists",
            )

        try:
            user = User.objects.create_user(**data)
            return RepositoryResponse(
                success=True,
                data=user,
                message="User created successfully",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=str(e),
            )

    """Update a user"""

    @transaction.atomic
    def update_user(self, user_id, data):
        try:
            user = User.objects.get(pk=user_id)
            for key, value in data.items():
                setattr(user, key, value)
            user.save()
            return RepositoryResponse(
                success=True,
                data=user,
                message="User updated successfully",
            )
        except User.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="User not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=str(e),
            )

    """Delete a user"""

    @transaction.atomic
    def delete_user(self, user_id):
        try:
            user = User.objects.get(pk=user_id)
            user.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="User deleted successfully",
            )
        except User.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="User not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=str(e),
            )

    """Deactivate user"""

    @transaction.atomic
    def deactivate_user(self, user_id):
        try:
            user = User.objects.get(pk=user_id)
            user.is_active = False
            user.save()
            return RepositoryResponse(
                success=True,
                data=user,
                message="User deactivated successfully",
            )
        except User.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="User not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=str(e),
            )

    """Activate user"""

    @transaction.atomic
    def activate_user(self, user_id):
        try:
            user = User.objects.get(pk=user_id)
            user.is_active = True
            user.save()
            return RepositoryResponse(
                success=True,
                data=user,
                message="User activated successfully",
            )
        except User.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="User not found",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=str(e),
            )

    """Get a user by id"""

    def get_user(self, user_id):
        try:
            user = User.objects.get(pk=user_id)
            return RepositoryResponse(
                success=True,
                data=user,
                message="User retrieved successfully",
            )
        except User.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="User does not exist",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=str(e),
            )

    """Get all users"""

    def get_all_users(self):
        try:
            users = User.objects.all()
            return RepositoryResponse(
                success=True,
                data=users,
                message="All users retrieved successfully",
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                data=None,
                message=str(e),
            )
