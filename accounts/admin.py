from django.contrib import admin

from accounts.models import Invoice, Subscription


@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    list_display = ("sub_company", "package", "end_date", "is_active")


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = (
        "inv_subscription",
        "invoice_number",
        "status",
        "total_amount",
        "date_created",
    )
    search_fields = ("invoice_number", "inv_subscription__user__username")
    list_filter = ("status", "date_created")
    date_hierarchy = "date_created"
    ordering = ("-date_created",)
