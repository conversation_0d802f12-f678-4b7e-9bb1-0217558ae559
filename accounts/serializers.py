from rest_framework import serializers
from django.contrib.auth.models import User

from accounts.models import Invoice, Package, Subscription

# from base.serializers import HumanReadableDateField


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["id", "first_name", "last_name", "email"]


class GetSubscriptionSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    company = serializers.SerializerMethodField()
    package = serializers.SerializerMethodField()
    end_date = serializers.SerializerMethodField()

    class Meta:
        model = Subscription
        fields = "__all__"

    def get_company(self, obj):
        if obj.sub_company:
            return {"id": obj.sub_company.id, "name": obj.sub_company.name}
        return None

    def get_package(self, obj):
        if obj.sub_package:
            return {"id": obj.sub_package.id, "name": obj.sub_package.name}
        return None

    def get_end_date(self, obj):
        if obj.end_date:
            return obj.end_date.date().isoformat()
        return None


class GetInvoicesSerializer(serializers.ModelSerializer):
    subscription = serializers.SerializerMethodField()
    package = serializers.SerializerMethodField()
    qty = serializers.SerializerMethodField()

    class Meta:
        model = Invoice
        fields = "__all__"

    def get_package(self, obj):
        if obj.inv_subscription.sub_package:
            return {
                "id": obj.inv_subscription.sub_package.id,
                "name": obj.inv_subscription.sub_package.name,
                "price": obj.inv_subscription.sub_package.price,
            }
        return None

    def get_subscription(self, obj):
        if obj.inv_subscription:
            return {
                "id": obj.inv_subscription.id,
                "start_date": obj.inv_subscription.start_date,
                "end_date": obj.inv_subscription.end_date,
            }

    def get_qty(self, obj):
        return obj.inv_subscription.get_total_months()
