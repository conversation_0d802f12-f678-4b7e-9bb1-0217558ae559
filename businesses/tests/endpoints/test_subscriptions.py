from django.test import TransactionTestCase, override_settings
from django.core.cache import cache

from accounts.factory import PackageFactory, PackageServiceFactory, SubscriptionFactory
from base.tests.setup import BaseSetup
from businesses.models import BillingAddress
from businesses.services.subscriptions import SubscriptionService
from base.factory import UserFactory
from businesses.factory import CompanyFactory


sub_service = SubscriptionService()


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestCreateSubscription(BaseSetup):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        super().setUp()

    def test_create_subscription_success(self):
        data = {
            "package_id": self.package.id,
            "end_date": "2022-12-31",
            "billing_address": "123 Main St",
            "billing_city": "Test City",
            "billing_country": "Test Country",
            "email": "<EMAIL>",
            "phone_number": "+1234567890",
            "postal_code": "12345",
        }

        self._authenticate_user(self.company.created_by)

        response = self.client.post(
            f"{self.business_endpoint}/{self.company.slug}/subscriptions/",
            data,
            format="json",
        )

        if not response.status_code == 201:
            self.fail(f"Failed to create a subscription {response.data}")

        self.assertEqual(response.data["user"]["id"], self.company.created_by.id)
        self.assertEqual(response.data["company"]["id"], self.company.id)
        self.assertEqual(response.data["package"]["id"], self.package.id)
        self.assertEqual(response.data["end_date"], "2022-12-31")
        self.assertFalse(response.data["is_active"])

        # check if billing details are saved
        billing_address = BillingAddress.objects.filter(
            billing_company=self.company
        ).first()
        self.assertIsNotNone(billing_address)
        self.assertEqual(billing_address.billing_address, "123 Main St")
        self.assertEqual(billing_address.billing_city, "Test City")
        self.assertEqual(billing_address.billing_country, "Test Country")
        self.assertEqual(billing_address.email, "<EMAIL>")
        self.assertEqual(billing_address.phone_number, "+1234567890")
        self.assertEqual(billing_address.postal_code, "12345")


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestGetAllSubscriptions(BaseSetup):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        super().setUp()

    def test_get_all_subscriptions_success(self):
        subscriptions = [
            SubscriptionFactory(sub_company=self.company) for _ in range(4)
        ]
        SubscriptionFactory()

        self._authenticate_user(self.company.created_by)

        response = self.client.get(
            f"{self.business_endpoint}/{self.company.slug}/subscriptions/"
        )
        self.assertTrue(response.status_code == 200)
        self.assertEqual(len(response.data), 4)


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestGetTotals(BaseSetup):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        super().setUp()
        self.service = SubscriptionService()

    def test_get_totals_success(self):
        data = {
            "totals": True,
            "package_id": self.package.id,
            "end_date": "2025-12-31",
        }
        self._authenticate_user(self.company.created_by)
        response = self.client.get(
            f"{self.business_endpoint}/{self.company.slug}/subscriptions/",
            data=data,
        )
        if not response.status_code == 200:
            self.fail(f"Failed to get totals {response.data}")
        print(response.data)
