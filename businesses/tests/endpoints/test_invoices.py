from accounts.factory import InvoiceFactory, SubscriptionFactory
from base.factory import UserFactory
from base.tests.setup import BaseSetup
from businesses.factory import CompanyFactory
from accounts.models import Invoice
from rest_framework import status
from django.test import override_settings
from django.core.cache import cache
import json
import uuid


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestGetInvoices(BaseSetup):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        super().setUp()
        self.company = CompanyFactory()
        self.client_1 = UserFactory()
        self.client_2 = UserFactory()
        self.subscription_1 = SubscriptionFactory(user=self.client_1)
        self.subscription_2 = SubscriptionFactory(user=self.client_2)

    def test_get_invoices(self):
        subscriptions = [
            SubscriptionFactory(sub_company=self.company) for _ in range(3)
        ]
        SubscriptionFactory()

        for i in subscriptions:
            InvoiceFactory(inv_subscription=i)
        self._authenticate_user(self.company.created_by)
        response = self.client.get(
            f"{self.business_endpoint}/{self.company.slug}/billing/invoices/",
        )

        if not response.status_code == 200:
            self.fail(f"Failed to create a subscription {response.data}")

        self.assertEqual(len(response.data), 3)

    def test_get_all_invoices(self):
        """Test getting all invoices for a company"""
        invoice_1 = InvoiceFactory(inv_subscription=self.subscription_1)
        invoice_2 = InvoiceFactory(inv_subscription=self.subscription_2)
        self._authenticate_user(self.admin)
        response = self.client.get(
            f"{self.invoices_endpoints}/",
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["total_count"], 2)

    def test_get_invoices_with_params(self):
        """Test getting invoices with filters and search query"""
        invoice_1 = InvoiceFactory(
            inv_subscription=self.subscription_1, invoice_number="INV-************"
        )
        invoice_2 = InvoiceFactory(inv_subscription=self.subscription_2)
        self._authenticate_user(self.admin)
        response = self.client.get(
            f"{self.invoices_endpoints}/?filters={{'status': 'pending'}}&search=INV-************".replace(
                "'", '"'
            ),
        )

    def test_get_invoices_unauthenticated(self):
        """Test getting invoices without authentication"""
        self.client.logout()
        response = self.client.get(
            f"{self.invoices_endpoints}/",
        )
        self.assertEqual(response.status_code, 403)

    def test_create_invoice(self):
        """Test creating an invoice"""
        self._authenticate_user(self.admin)
        response = self.client.post(
            f"{self.invoices_endpoints}/",
            data=json.dumps({"subscription_id": str(self.subscription_1.id)}),
            content_type="application/json",
        )
        self.assertEqual(response.status_code, 201)

    def test_create_invoice_unauthenticated(self):
        """Test creating an invoice without authentication"""
        self.client.logout()
        response = self.client.post(
            f"{self.invoices_endpoints}/",
            data=json.dumps({"subscription_id": str(self.subscription_1.id)}),
            content_type="application/json",
        )
        self.assertEqual(response.status_code, 403)


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestAdminInvoiceEndpoint(BaseSetup):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        super().setUp()
        unique_id1 = uuid.uuid4()
        unique_id2 = uuid.uuid4()
        self.subscription = SubscriptionFactory(name=f"Test-Subscription-{unique_id1}")
        self.invoice = InvoiceFactory(
            inv_subscription=self.subscription, name=f"Test-Invoice-{unique_id2}"
        )
        self.valid_status = Invoice.INVOICE_STATUS_CHOICES[1][0]
        self.endpoint = f"{self.admin_endpoints}/invoices/{self.invoice.id}/"
        self.non_existent_uuid = "00000000-0000-4000-a000-000000000000"

    def test_get_invoice_success(self):
        """Test successful retrieval of an invoice by admin"""
        self._authenticate_user(self.admin)
        response = self.client.get(self.endpoint)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])
        self.assertEqual(response.data["message"], "Invoice retrieved successfully")
        self.assertEqual(
            response.data["data"]["invoice_number"], self.invoice.invoice_number
        )

    def test_get_invoice_not_found(self):
        """Test retrieving a non-existent invoice"""
        self._authenticate_user(self.admin)
        response = self.client.get(
            f"{self.admin_endpoints}/invoices/{self.non_existent_uuid}/"
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertFalse(response.data["success"])
        self.assertEqual(response.data["message"], "Invoice not found")

    def test_get_invoice_unauthorized(self):
        """Test retrieving an invoice without authentication"""
        response = self.client.get(self.endpoint)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_get_invoice_by_regular_user(self):
        """Test retrieving an invoice by a regular user (not admin)"""
        self._authenticate_user(self.user)
        response = self.client.get(self.endpoint)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])

    def test_update_invoice_status_success(self):
        """Test successful update of invoice status by admin"""
        self._authenticate_user(self.admin)
        data = {"status": self.valid_status}
        response = self.client.patch(self.endpoint, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])
        self.assertEqual(
            response.data["message"], "Invoice status updated successfully"
        )
        self.assertEqual(response.data["data"]["status"], self.valid_status)

        # Verify invoice status is updated in the database
        self.invoice.refresh_from_db()
        self.assertEqual(self.invoice.status, self.valid_status)

    def test_update_invoice_by_regular_user(self):
        """Test updating invoice status by a regular user"""
        self._authenticate_user(self.user)
        data = {"status": self.valid_status}
        response = self.client.patch(self.endpoint, data)

        # Regular users update if authenticated
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])

    def test_update_invoice_missing_status(self):
        """Test update with missing status field"""
        self._authenticate_user(self.admin)
        data = {"other_field": "value"}
        response = self.client.patch(self.endpoint, data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data["success"])
        self.assertEqual(response.data["message"], "Failed to update invoice status")

    def test_update_invoice_invalid_status(self):
        """Test update with invalid status value"""
        self._authenticate_user(self.admin)
        data = {"status": "invalid_status"}
        response = self.client.patch(self.endpoint, data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data["success"])
        self.assertEqual(response.data["message"], "Failed to update invoice status")

    def test_update_invoice_not_found(self):
        """Test updating a non-existent invoice"""
        self._authenticate_user(self.admin)
        data = {"status": self.valid_status}
        response = self.client.patch(
            f"{self.admin_endpoints}/invoices/{self.non_existent_uuid}/", data
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertFalse(response.data["success"])
        self.assertEqual(response.data["message"], "Invoice not found")

    def test_update_invoice_unauthorized(self):
        """Test updating an invoice without authentication"""
        data = {"status": self.valid_status}
        response = self.client.patch(self.endpoint, data)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestGetInvoiceDetails(BaseSetup):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        super().setUp()
        self.company = CompanyFactory()
        self.subscription = SubscriptionFactory(sub_company=self.company)
        self.invoice = InvoiceFactory(inv_subscription=self.subscription)
        self.endpoint = f"{self.business_endpoint}/{self.company.slug}/billing/invoices/{self.invoice.id}/"

    def test_get_invoice_details(self):
        """Test getting invoice details"""
        self._authenticate_user(self.company.created_by)
        response = self.client.get(self.endpoint)

        if not response.status_code == 200:
            self.fail(f"Failed to get invoice details {response.data}")

        self.assertEqual(response.data["invoice_number"], self.invoice.invoice_number)


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestGetInvoicePdf(BaseSetup):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        super().setUp()
        self.company = CompanyFactory()
        self.subscription = SubscriptionFactory(sub_company=self.company)
        self.invoice = InvoiceFactory(inv_subscription=self.subscription)
        self.endpoint = f"{self.business_endpoint}/{self.company.slug}/billing/invoices/{self.invoice.id}/download/"

    def test_get_invoice_pdf(self):
        """Test getting invoice PDF"""
        self._authenticate_user(self.company.created_by)
        response = self.client.get(self.endpoint)

        if not response.status_code == 200:
            self.fail(f"Failed to get invoice PDF {response.data}")
        self.assertEqual(response["Content-Type"], "application/pdf")
