from django.test import TestCase, override_settings
from django.core.cache import cache

from base.factory import UserFactory
from businesses.factory import CompanyFactory
from candidates.factory import InterviewFactory
from jobs.factory import JobFactory
from rest_framework.test import APIClient
from datetime import datetime, timedelta
from rest_framework_simplejwt.tokens import RefreshToken
import jwt
from core.settings import SECRET_KEY


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class BusinessInterviewsEndpointsCase(TestCase):
    def setUp(self):
        cache.clear()  # Clear cache before each test

        # create a user
        self.user = UserFactory()

        # create a business
        self.business = CompanyFactory(
            created_by=self.user,
        )

        # crate a job
        self.job = JobFactory(
            company_name=self.business,
        )

        # create interviews
        for i in range(3):
            InterviewFactory(
                business=self.business,
                job_applied=self.job,
                from_time="10:00",
                to_time="11:00"
            )

        token_payload = {
            "user_id": self.user.id,
            "exp": datetime.now() + timedelta(days=1),
        }
        jwt_token = jwt.encode(token_payload, SECRET_KEY, algorithm="HS256")

        # initialize client
        self.client = APIClient()

        # authenticate client
        self.client.force_authenticate(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {jwt_token}")

        self.endpoint = f"/api/business/{self.business.slug}/interviews/"

    def tearDown(self):
        cache.clear()  # Clear cache after each test

    # test getting interviews for a business

    def test_get_business_interviews(self):
        response = self.client.get(self.endpoint)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 3)
    
    def test_get_business_interviews_unauthorized(self):
        self.client.logout()
        response = self.client.get(self.endpoint)
        self.assertEqual(response.status_code, 403)
    
    def test_get_business_interviews_no_token(self):
        self.client.credentials()
        response = self.client.get(self.endpoint)
        self.assertEqual(response.status_code, 403)
