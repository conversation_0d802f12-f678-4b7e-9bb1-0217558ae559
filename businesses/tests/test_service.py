from email.mime import application
import re
from django.test import TestCase, override_settings
from django.core.cache import cache
from base.models import Category, SocialMediaPlatforms, UserLanguage
from businesses.factory import CompanyFactory
from businesses.models import Company, Service
from businesses.services.business import BusinessService
from businesses.services.service import BusinessServiceService
from django.contrib.auth import get_user_model
from django.test import TestCase
from django.contrib.auth.models import User
from unittest.mock import Mock, patch
from candidates.factory import ApplicationFactory, CandidateFactory
from candidates.repositories.application_repository import RepositoryResponse
from candidates.services import candidate
from jobs.factory import JobFactory
from rest_framework import status

User = get_user_model()


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestBusinessServiceService(TestCase):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        self.service_service = BusinessServiceService()
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="password123",
        )
        self.category = Category.objects.create(name="testcategory")
        self.business = Company.objects.create(name="testbusiness")
        self.valid_data = {
            "name": "Test Service",
            "category_name": self.category,
            "company": self.business,
        }

    def tearDown(self):
        cache.clear()  # Clear cache after each test

    def test_create_service_success(self):
        service = self.service_service.create_service(data=self.valid_data)
        self.assertIsNotNone(service)
        self.assertEqual(service.data["name"], self.valid_data["name"])
        self.assertEqual(service.data["category_name"], self.category.id)

    def test_create_service_failure(self):
        service = self.service_service.create_service(data={})
        self.assertIsNone(service.data)

    def test_get_service_by_slug_success(self):
        service = self.service_service.create_service(data=self.valid_data)
        business = self.service_service.get_service(service.data["slug"])
        self.assertIsNotNone(business.data)
        self.assertEqual(business.data["slug"], service.data["slug"])

    def test_get_service_by_slug_failure(self):
        business = self.service_service.get_service(slug=9999)
        self.assertFalse(business.success)
        self.assertIsNone(business.data)

    def test_update_service_success(self):
        service = self.service_service.create_service(data=self.valid_data)
        updated_data = {"name": "Updated Service"}
        updated_business = self.service_service.update_service(
            slug=service.data["slug"], data=updated_data
        )
        self.assertTrue(updated_business.success)
        self.assertIsNotNone(updated_business.data)
        self.assertEqual(updated_business.data["name"], updated_data["name"])

    def test_update_service_failure(self):
        updated_business = self.service_service.update_service(
            slug=9999, data={"name": "Updated Service"}
        )
        self.assertIsNone(updated_business.data)
        self.assertFalse(updated_business.success)

    def test_delete_service_success(self):
        service = self.service_service.create_service(data=self.valid_data)
        deleted_business = self.service_service.delete_service(
            slug=service.data["slug"]
        )
        self.assertTrue(deleted_business.success)
        self.assertIsNone(deleted_business.data)

    def test_delete_service_failure(self):
        deleted_business = self.service_service.delete_service(slug=9999)
        self.assertFalse(deleted_business.success)
        self.assertIsNone(deleted_business.data)

    def test_get_all_services_success(self):
        self.service_service.create_service(data=self.valid_data)
        services = self.service_service.get_all_services(
            slug=self.business.slug
        )
        self.assertTrue(services.success)
        self.assertIsNotNone(services.data)

    def test_get_all_services_failure(self):
        services = self.service_service.get_all_services(slug=9999)
        self.assertFalse(services.success)
        self.assertIsNone(services.data)

    def test_get_or_create_service_success(self):
        service = self.service_service.get_or_create_service(
            name="Test Name", category_name="Test Category"
        )
        self.assertIsNotNone(service.data)
        self.assertEqual(service.data["name"], "Test Name")


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestBusinessService(TestCase):
    def setUp(self):
        cache.clear()  # Clear cache before each test

        # Create test user
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )

        # Initialize the service
        self.business_service = BusinessService()

        # Create test data
        self.valid_business_data = {
            "name": "Test Company",
            "description": "Test Description",
            "business_address": "Test Address",
            "industry": "Technology",
        }

        # Create test categories
        self.category = Category.objects.create(name="Technology")

        # Create test services
        self.service = Service.objects.create(
            name="Web Development", category_name=self.category
        )

        # Create test language
        self.language = UserLanguage.objects.create(
            name="English",
        )

        # Create test social media platform
        self.social_media = SocialMediaPlatforms.objects.create(
            name="LinkedIn", handle="LinkedIn", link="https://linkedin.com/in/testuser"
        )

    def tearDown(self):
        cache.clear()  # Clear cache after each test

    def test_create_business_success(self):
        response = self.business_service.create_business(
            self.user, self.valid_business_data
        )

        self.assertTrue(response.success)
        self.assertEqual(response.status, status.HTTP_201_CREATED)

        # Verify business was created in database
        created_business = Company.objects.filter(
            name=self.valid_business_data["name"]
        ).first()
        self.assertIsNotNone(created_business)
        self.assertEqual(
            created_business.description, self.valid_business_data["description"]
        )

    def test_create_business_missing_fields(self):
        invalid_data = {
            "name": "Test Company",
            "description": "",  # Empty description
            "industry_name": "Technology",
            # Missing address
        }

        response = self.business_service.create_business(
            self.user, invalid_data
        )

        self.assertFalse(response.success)
        self.assertEqual(response.status, status.HTTP_400_BAD_REQUEST)

    def test_create_business_duplicate_name(self):
        # First creation should succeed
        self.business_service.create_business(self.user, self.valid_business_data)

        # Second creation with same name should fail
        response = self.business_service.create_business(
            self.user, self.valid_business_data
        )

        self.assertFalse(response.success)
        self.assertIn("Company with the same name already exists", response.message)

    def test_update_business_success(self):
        try:
            company = Company.objects.create(
                name="Company",
                website_url="http://igihe.com/",
                description="Test description",
                created_by=self.user,
            )
            print("Test company update success: ", company.slug)
        except Exception as e:
            print(f"Error creating business: {e}")
            return
        update_data = {
            "name": "Test Company",
            "description": "Updated Description",
            "languages": [
                {
                    "name": "English",
                },
                {
                    "name": "French",
                },
            ],
            "business_socials": [
                {
                    "link": "https://linkedin.com/company/test",
                    "handle": "testhandle",
                }
            ],
            "services": [{"name": "Web Development", "category_name": "Technology"}],
        }

        # Update the business
        response = self.business_service.update_business(
            company.slug,
            self.user,
            update_data,
        )
        if not response.success:
            print(f"Update failed: {response.message}")
            print(f"Response data: {response.data}")
        self.assertTrue(response.success)

    def test_update_business_not_found(self):
        update_data = {
            "name": "Nonexistent Company",
            "description": "Updated Description",
        }

        response = self.business_service.update_business(
            user=self.user, business_data=update_data, slug=9999
        )

        self.assertFalse(response.success)
        self.assertEqual(response.status, status.HTTP_404_NOT_FOUND)

    def test_delete_business_success(self):
        # First create a business
        create_response = self.business_service.create_business(
            self.user, self.valid_business_data
        )
        slug = create_response.data["slug"]

        # Delete the business
        response = self.business_service.delete_business(self.user, slug)

        self.assertTrue(response.success)
        self.assertEqual(response.message, "Business deleted successfully.")

        # Verify business was deleted from database
        self.assertFalse(Company.objects.filter(slug=slug).exists())

    def test_delete_business_not_found(self):
        response = self.business_service.delete_business(self.user, 999)

        self.assertFalse(response.success)
        self.assertEqual(response.status, status.HTTP_404_NOT_FOUND)

    def test_get_businesses_by_name_success(self):
        self.business_service.create_business(self.user, self.valid_business_data)

        # Retrieve the business
        response = self.business_service.get_businesses_by_name(
            "Test Company"
        )

        self.assertTrue(response.success)
        self.assertEqual(response.status, status.HTTP_200_OK)
        self.assertEqual(response.data["name"], "Test Company")

    def test_get_all_businesses_success(self):
        # Create multiple businesses
        self.business_service.create_business(self.user, self.valid_business_data)

        self.business_service.create_business(
            self.user,
            {
                "name": "Second Company",
                "description": "Another Description",
                "address": "Another Address",
                "industry_name": "Technology",
            },
        )

        response = self.business_service.get_all_businesses()

        self.assertTrue(response.success)
        self.assertEqual(response.message, "Businesses retrieved successfully.")
        self.assertIsNotNone(response.data)

    def test_get_business_profile_status_complete(self):
        """Test when business profile is complete with all required fields"""
        # Create a company with complete profile
        company = Company.objects.create(
            name="Company",
            website_url="http://company.com/",
            description="Test description",
            established_at="2023-01-01",
            number_of_employees=50,
            business_address="Test Address",
            industry="Tech",
        )
        # Add related fields
        company.services.add(self.service)
        company.languages.add(self.language)
        company.business_socials.add(self.social_media)
        company.email = "<EMAIL>"
        company.phone_number = "+250780000000"
        company.logo = "company_logo.png"
        company.save()

        response = self.business_service.get_business_profile_status(company.slug)

        self.assertTrue(response.success)
        self.assertEqual(response.status, status.HTTP_200_OK)
        self.assertEqual(
            response.data,
            {"complete": True, "current_step": 2}
        )

    def test_get_business_profile_status_step1_incomplete(self):
        """Test when step 1 is incomplete"""
        # Create company with missing step 1 fields
        company = Company.objects.create(
            name="",  # Empty name
            website_url="http://company.com/",
            description="Test description",
            established_at="2023-01-01",
            number_of_employees=50,
            business_address="Test Address",
            industry="Tech"
        )
        # Add step 2 fields
        company.services.add(self.service)
        company.languages.add(self.language)
        company.business_socials.add(self.social_media)
        company.email = "<EMAIL>"
        company.phone_number = "+250780000000"
        company.logo = "company_logo.png"
        company.save()

        response = self.business_service.get_business_profile_status(company.slug)

        self.assertTrue(response.success)
        self.assertEqual(response.status, status.HTTP_200_OK)
        self.assertEqual(
            response.data,
            {"complete": False, "current_step": 1}
        )

    def test_get_business_profile_status_step2_incomplete(self):
        """Test when step 2 is incomplete"""
        # Create company with complete step 1 fields
        company = Company.objects.create(
            name="Company",
            website_url="http://company.com/",
            description="Test description",
            established_at="2023-01-01",
            number_of_employees=50,
            business_address="Test Address",
            industry="Tech"
        )
        # Missing step 2 fields (no services, languages, socials)
        company.email = ""  # Empty email
        company.phone_number = ""  # Empty phone
        company.save()

        response = self.business_service.get_business_profile_status(company.slug)

        self.assertTrue(response.success)
        self.assertEqual(response.status, status.HTTP_200_OK)
        self.assertEqual(
            response.data,
            {"complete": False, "current_step": 2}
        )

    def test_get_business_profile_status_not_found(self):
        """Test when business is not found"""
        response = self.business_service.get_business_profile_status("non-existent-slug")

        self.assertFalse(response.success)
        self.assertEqual(response.status, status.HTTP_404_NOT_FOUND)
        self.assertIsNone(response.data)
    
    def test_get_business_candidates(self):
        business = CompanyFactory()
        job = JobFactory(company_name=business)
        for i in range(5):
            candidate = CandidateFactory()
            application = ApplicationFactory(applicant=candidate, job_applied=job)
        response = self.business_service.get_business_candidates(business.slug)
        self.assertTrue(response.success)
        self.assertEqual(response.status, status.HTTP_200_OK)
    
    def test_get_business_candidates_not_found(self):
        response = self.business_service.get_business_candidates("non-existent-slug")
        self.assertFalse(response.success)
        self.assertEqual(response.status, status.HTTP_404_NOT_FOUND)
    