from businesses.views.data.dashboard import DashboardService
from businesses.models import Company as Business
from jobs.models import Job
from candidates.models import Interview, Application, Chat, Candidate
from django.test import TestCase, override_settings
from django.core.cache import cache
from django.contrib.auth import get_user_model


User = get_user_model()


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestDashboardService(TestCase):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        self.dashboard_service = DashboardService()
        self.user = User.objects.create_user(
            username="testuser", password="password123", email="<EMAIL>"
        )
        self.user2 = User.objects.create_user(
            username="testuser2", password="password123", email="<EMAIL>"
        )
        self.business = Business.objects.create(
            name="Test Business",
            email="<EMAIL>",
            created_by=self.user,
        )
        self.job = Job.objects.create(
            name="Test Job",
            company_name=self.business,
            min_salary=100,
            max_salary=300,
        )
        self.job2 = Job.objects.create(
            name="Test Job 2",
            company_name=self.business,
            min_salary=200,
            max_salary=400,
        )
        self.job3 = Job.objects.create(
            name="Test Job 3",
            company_name=self.business,
            min_salary=300,
            max_salary=500,
        )
        self.candidate = Candidate.objects.create(
            user=self.user2,
        )
        self.application = Application.objects.create(
            name="Test Application",
            applicant=self.candidate,
            job_applied=self.job,
            status="Pending",
        )
        self.application2 = Application.objects.create(
            name="Test Application 2",
            applicant=self.candidate,
            job_applied=self.job2,
            status="Rejected",
        )
        self.application3 = Application.objects.create(
            name="Test Application 3",
            applicant=self.candidate,
            job_applied=self.job3,
            status="Hired",
        )
        self.interview = Interview.objects.create(
            name="Test Interview",
            user=self.user2,
            business=self.business,
            job_applied=self.job,
            interview_date="2022-01-01",
            from_time="10:00",
            to_time="11:00",
        )
        self.chat = Chat.objects.create(
            sender=self.user2,
            receiver=self.user,
            message="Test chat message",
        )

    def tearDown(self):
        cache.clear()  # Clear cache after each test

    def test_get_application_statistics(self):
        jobs = [self.job, self.job2, self.job3]
        application_stats = self.dashboard_service.get_application_statistics(jobs)

        self.assertTrue(application_stats.success)
        data = application_stats.data
        self.assertEqual(data["total_application_rejected"], 1)
        self.assertEqual(data["total_application_pending"], 1)
        self.assertEqual(
            data["status_percentages"]["Hired"], 33.33
        )
        self.assertEqual(
            data["status_percentages"]["Pending"], 33.33
        )
        self.assertEqual(
            data["status_percentages"]["Rejected"], 33.33
        )
        

    def test_get_dashboard_data_success(self):
        response = self.dashboard_service.get_dashboard_data(slug=self.business.slug)

        self.assertTrue(response.success)
        data = response.data
        self.assertEqual(data["key_stats"]["total_job_postings"], 3)
        self.assertEqual(data["key_stats"]["unread_messages"], 1)
        self.assertEqual(data["key_stats"]["pending_applications"], 1)
        self.assertEqual(data["key_stats"]["rejected_applications"], 1)
        self.assertIn("upcoming_interviews", data)
        self.assertIn("application_stats", data)

    def test_get_dashboard_data_business_not_found(self):
        response = self.dashboard_service.get_dashboard_data(slug="invalid-slug")
        
        self.assertFalse(response.success)
