from rest_framework.test import APITestCase, APIClient
from django.test import override_settings
from django.core.cache import cache
from candidates.factory import ApplicationFactory, InterviewFactory
from core.settings import SECRET_KEY
import jwt
from rest_framework import status
from businesses.models import Company
from jobs.models import Job
from candidates.models import Candidate, Application, Chat, Interview
from django.contrib.auth import get_user_model
import datetime

User = get_user_model()
client = APIClient()

@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestBusinessDashboardDataEndpoint(APITestCase):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        self.user = User.objects.create_user(
            username="businessuser", email="<EMAIL>", password="password123"
        )
        self.user2 = User.objects.create_user(
            username="candidateuser", email="<EMAIL>", password="password123"
        )

        token = jwt.encode({"user_id": self.user.id}, SECRET_KEY, algorithm="HS256")
        client.force_authenticate(user=self.user)
        client.credentials(HTTP_AUTHORIZATION=f"Bearer {token}")

        self.company = Company.objects.create(
            name="Test Company", email="<EMAIL>", created_by=self.user
        )

        self.job1 = Job.objects.create(
            name="Test Job 1", company_name=self.company, min_salary=500, max_salary=1000
        )
        self.job2 = Job.objects.create(
            name="Test Job 2", company_name=self.company, min_salary=700, max_salary=1200
        )

        self.candidate = Candidate.objects.create(user=self.user2)

        self.application1 = ApplicationFactory(
            applicant=self.candidate, job_applied=self.job1, status="Pending"
        )
        self.application2 = ApplicationFactory(
            applicant=self.candidate, job_applied=self.job2, status="Rejected"
        )

        self.interview = InterviewFactory(
            user=self.user2,
            business=self.company,
            job_applied=self.job1,
            interview_date=datetime.datetime.today(),
            from_time="10:00",
            to_time="11:00",
            location="Online",
        )

        self.chat = Chat.objects.create(
            sender=self.user2,
            receiver=self.user,
            message="Test chat message",
            status="Unread",
        )

        self.url = f"/api/business/{self.company.slug}/dashboard/"

    def tearDown(self):
        cache.clear()  # Clear cache after each test

    def test_business_dashboard_data_success(self):
        response = client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertIn("key_stats", data)
        self.assertEqual(data["key_stats"]["total_job_postings"], 2)
        self.assertEqual(data["key_stats"]["unread_messages"], 1)
        self.assertEqual(data["key_stats"]["pending_applications"], 1)
        self.assertEqual(data["key_stats"]["rejected_applications"], 1)

    def test_business_dashboard_data_unauthorized(self):
        client.logout()
        response = client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_business_dashboard_data_no_token(self):
        client.credentials()
        response = client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
