from django.test import override_settings
from django.core.cache import cache
from accounts.factory import InvoiceFactory, PackageFactory, SubscriptionFactory
from accounts.models import Invoice
from base.tests.setup import BaseSetup
from businesses.services.invoices import InvoiceService
from businesses.services.subscriptions import SubscriptionService
from base.factory import UserFactory
from businesses.factory import CompanyFactory
from rest_framework import status
from accounts.models import Invoice

invoice_service = InvoiceService()


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestGetInvoices(BaseSetup):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        super().setUp()
        self.company = CompanyFactory()
        self.subscription = SubscriptionFactory()
        self.invoice = InvoiceFactory(inv_subscription=self.subscription)

    def test_get_invoices(self):
        subscriptions = [
            SubscriptionFactory(sub_company=self.company) for _ in range(3)
        ]
        SubscriptionFactory()

        for i in subscriptions:
            InvoiceFactory(inv_subscription=i)

        response = invoice_service.get_all_invoices(
            user=self.company.created_by,
            company_slug=self.company.slug,
        )

        if not response.success:
            self.fail(f"Failed to get invoices {response.message}")

        self.assertEqual(len(response.data), 3)

    def test_get_invoice_success(self):
        """Test getting a single invoice by id"""
        response = invoice_service.get_invoice(self.invoice.id)

        self.assertTrue(response.success)
        self.assertEqual(response.status, status.HTTP_200_OK)
        self.assertEqual(response.data["invoice_number"], self.invoice.invoice_number)

    def test_get_invoice_not_found(self):
        """Test getting a non-existent invoice"""
        response = invoice_service.get_invoice(9999999)

        self.assertFalse(response.success)
        self.assertEqual(response.status, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.message, "Invoice does not exist")


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestUpdateInvoice(BaseSetup):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        super().setUp()
        self.invoice = InvoiceFactory(created_by=self.user)
        self.valid_status = Invoice.INVOICE_STATUS_CHOICES[0][0]

    def test_update_invoice_status_success(self):
        """Test successful invoice status update"""
        data = {"status": self.valid_status}
        response = invoice_service.update_invoice(
            invoice_id=self.invoice.id,
            logged_in_user=self.user,
            data=data
        )

        self.assertTrue(response.success)
        self.assertEqual(response.status, status.HTTP_200_OK)
        self.assertEqual(response.message, "Invoice status updated successfully")
        self.assertEqual(response.data["status"], self.valid_status)

    def test_update_invoice_missing_status(self):
        """Test update with missing status field"""
        data = {"other_field": "value"}
        response = invoice_service.update_invoice(
            invoice_id=self.invoice.id,
            logged_in_user=self.user,
            data=data
        )

        self.assertFalse(response.success)
        self.assertEqual(response.status, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.message, "Status is required")

    def test_update_invoice_invalid_status(self):
        """Test update with invalid status value"""
        data = {"status": "invalid_status"}
        response = invoice_service.update_invoice(
            invoice_id=self.invoice.id,
            logged_in_user=self.user,
            data=data
        )

        self.assertFalse(response.success)
        self.assertEqual(response.status, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.message, "Invalid status")

    def test_update_invoice_not_found(self):
        """Test updating non-existent invoice"""
        data = {"status": self.valid_status}
        response = invoice_service.update_invoice(
            invoice_id=99999,
            logged_in_user=self.user,
            data=data
        )

        self.assertFalse(response.success)
        self.assertEqual(response.status, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.message, "Invoice does not exist")

    def test_update_by_non_owner_success(self):
        """Test updating invoice by non-owner"""
        data = {"status": self.valid_status}
        response = invoice_service.update_invoice(
            invoice_id=self.invoice.id,
            logged_in_user=self.admin,
            data=data
        )

        self.assertTrue(response.success)
        self.assertEqual(response.status, status.HTTP_200_OK)
        self.assertEqual(response.message, "Invoice status updated successfully")
        self.assertEqual(response.data["status"], self.valid_status)
        self.assertEqual(response.data["invoice_number"], self.invoice.invoice_number)
        

@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestDeleteInvoice(BaseSetup):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        super().setUp()
        self.company = CompanyFactory(created_by=self.user)
        self.subscription = SubscriptionFactory(
            sub_company=self.company,
            user=self.user  
        )
        self.invoice = InvoiceFactory(inv_subscription=self.subscription)
        self.non_owner = UserFactory()  

    def test_user_can_delete_invoice(self):
        """Test that invoice user can successfully delete"""
        response = invoice_service.delete_invoice(
            invoice_id=self.invoice.id,
            logged_in_user=self.user,
        )

        self.assertTrue(response.success)
        self.assertEqual(response.message, "Invoice deleted successfully")
       

    def test_non_owner_cannot_delete(self):
        """Test that non-owner cannot delete invoice"""
        response = invoice_service.delete_invoice(
            invoice_id=self.invoice.id,
            logged_in_user=self.non_owner,
        )

        self.assertFalse(response.success)
        self.assertEqual(
            response.message,
            "Only the invoice owner can delete this invoice"
        )
        # Verify invoice still exists
        self.invoice.refresh_from_db()  

    def test_delete_invoice_not_found(self):
        # Test non-existent invoice
        response = invoice_service.delete_invoice(
            invoice_id=9999,  
            logged_in_user=self.company.created_by,
        )

        self.assertFalse(response.success)
        self.assertEqual(response.message, "Invoice not found")

    def test_delete_invoice_permission_denied(self):
        # Test unauthorized user
        response = invoice_service.delete_invoice(
            invoice_id=self.invoice.id,
            logged_in_user=self.non_owner,
            
        )
        self.assertFalse(response.success)
        self.assertEqual(response.message, "Only the invoice owner can delete this invoice")


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestGenerateInvoicePDF(BaseSetup):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        super().setUp()
        self.company = CompanyFactory(
            name="Test Company",
            email="<EMAIL>"
        )
        self.package = PackageFactory(
            name="Premium Package",
            price=100.00
        )
        self.subscription = SubscriptionFactory(
            sub_company=self.company,
            sub_package=self.package,
            start_date="2023-01-01T00:00:00Z",
            end_date="2023-02-01T00:00:00Z",
            user=self.user
        )
        self.invoice = InvoiceFactory(
            inv_subscription=self.subscription,
            total_amount=100.00,
            status="pending",
            payment_method="Credit Card",
            invoice_number="INV-20230101-001"
        )

    def test_generate_invoice_pdf_success(self):
        """Test successful generation of invoice PDF"""
        response = invoice_service._generate_invoice_pdf(self.invoice)
        
        self.assertTrue(response.success)
        self.assertEqual(response.status, status.HTTP_200_OK)
        self.assertEqual(response.message, "Invoice PDF generated successfully")
        
        # Verify PDF content is returned
        self.assertIsNotNone(response.data)
        pdf_content = response.data.read()
        self.assertTrue(len(pdf_content) > 0)
        self.assertTrue(pdf_content.startswith(b'%PDF'))  # PDF magic number

    def test_generate_invoice_pdf_invalid_invoice(self):
        """Test PDF generation with non-existent invoice"""
        # Create an invoice instance that's not in the database
        invalid_invoice = Invoice(id=999)
        
        response = invoice_service._generate_invoice_pdf(invalid_invoice)
        
        self.assertFalse(response.success)
        self.assertEqual(response.status, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.message, "Invoice not found")
        self.assertIsNone(response.data)

    def test_generate_invoice_pdf_with_all_fields(self):
        """Test PDF generation with all invoice fields populated"""
        # Update invoice with additional fields
        self.invoice.status = "paid"
        self.invoice.payment_method = "PayPal"
        self.invoice.date_paid = "2023-01-15T00:00:00Z"
        self.invoice.save()

        response = invoice_service._generate_invoice_pdf(self.invoice)
        
        self.assertTrue(response.success)
        self.assertEqual(response.status, status.HTTP_200_OK)
        self.assertIsNotNone(response.data)

    def test_generate_invoice_pdf_without_payment_method(self):
        """Test PDF generation with missing optional fields"""
        self.invoice.payment_method = None
        self.invoice.save()

        response = invoice_service._generate_invoice_pdf(self.invoice)
        
        self.assertTrue(response.success)
        self.assertEqual(response.status, status.HTTP_200_OK)
        self.assertIsNotNone(response.data)

    def test_download_invoice_pdf_success(self):
        """Test successful download of invoice PDF"""
        response = invoice_service.download_invoice_pdf(self.invoice.id)

        self.assertTrue(response.success)
        self.assertEqual(response.status, status.HTTP_200_OK)
        self.assertEqual(response.message, "Invoice PDF generated successfully")

        # Verify PDF content is returned
        self.assertIsNotNone(response.data)
        pdf_content = response.data.read()
        self.assertTrue(len(pdf_content) > 0)
        self.assertTrue(pdf_content.startswith(b'%PDF'))  

    def test_download_invoice_pdf_invalid_invoice(self):
        """Test PDF download with non-existent invoice"""
        response = invoice_service.download_invoice_pdf(999) 

        self.assertFalse(response.success)
        self.assertEqual(response.status, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.message, "Invoice does not exist")
        self.assertIsNone(response.data)


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestInvoiceService(BaseSetup):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        super().setUp()
        self.client_1 = UserFactory()
        self.client_2 = UserFactory()
        self.subscription_1 = SubscriptionFactory(user=self.client_1)
        self.subscription_2 = SubscriptionFactory(user=self.client_2)
    
    def test_create_invoice(self):
        """Test creating an invoice"""
        response = invoice_service.create_invoice(self.client_1, self.subscription_1)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
    
    def test_create_invoice_missing_fields(self):
        """Test creating an invoice with missing required fields"""
        response = invoice_service.create_invoice(self.client_1, None)
        self.assertFalse(response.success)
        self.assertIsNone(response.data)
    
    def test_get_invoices(self):
        """Test getting all invoices"""
        invoice_1 = InvoiceFactory(inv_subscription=self.subscription_1)
        invoice_2 = InvoiceFactory(inv_subscription=self.subscription_2)
        response = invoice_service.get_invoices()

        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(len(response.data["results"]), 2)
    
    def test_get_invoices_with_params(self):
        """Test getting invoices with filters and search query"""
        invoice_1 = InvoiceFactory(inv_subscription=self.subscription_1, invoice_number="INV-20230101-001")
        invoice_2 = InvoiceFactory(inv_subscription=self.subscription_2)
        response = invoice_service.get_invoices(
            filters={"status": "pending"},
            search_query="INV-20230101-001",
        )

        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data["total_count"], 1)
