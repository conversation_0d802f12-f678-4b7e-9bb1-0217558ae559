from accounts.factory import PackageFactory, SubscriptionFactory
from accounts.models import Invoice
from base.tests.setup import BaseSetup
from businesses.models import BillingAddress
from businesses.services.subscriptions import SubscriptionService
from base.factory import UserFactory
from businesses.factory import CompanyFactory
from businesses.views import billing


sub_service = SubscriptionService()


class TestCreateSubscription(BaseSetup):
    def test_create_subscription_success(self):
        data = {
            "package_id": self.package.id,
            "end_date": "2022-12-31",
            "billing_address": "123 Main St",
            "billing_city": "Test City",
            "billing_country": "Test Country",
            "email": "<EMAIL>",
            "phone_number": "+**********",
            "postal_code": "12345",
        }

        response = sub_service.create_subscription(self.user, self.company.slug, data)

        if not response.success:
            self.fail(f"Failed to create a subscription {response.message}")

        self.assertIsNotNone(response.data)
        self.assertEqual(response.data["user"]["id"], self.company.created_by.id)
        self.assertEqual(response.data["company"]["id"], self.company.id)
        self.assertEqual(response.data["package"]["id"], self.package.id)
        self.assertEqual(response.data["end_date"], "2022-12-31")
        self.assertFalse(response.data["is_active"])

        # check if billing details are saved
        billing_address = BillingAddress.objects.filter(
            billing_company=self.company
        ).first()
        self.assertIsNotNone(billing_address)
        self.assertEqual(billing_address.billing_address, "123 Main St")
        self.assertEqual(billing_address.billing_city, "Test City")
        self.assertEqual(billing_address.billing_country, "Test Country")
        self.assertEqual(billing_address.email, "<EMAIL>")
        self.assertEqual(billing_address.phone_number, "+**********")
        self.assertEqual(billing_address.postal_code, "12345")

        # test if invoice is created
        invoice = Invoice.objects.filter(inv_subscription=response.data["id"]).first()
        self.assertIsNotNone(invoice)
        # check if invoice total is correct

    def test_create_free_subscription_success(self):
        data = {
            "package_id": self.free_package.id,
            "end_date": "2022-12-31",
            "billing_address": "123 Main St",
            "billing_city": "Test City",
            "billing_country": "Test Country",
            "email": "<EMAIL>",
            "phone_number": "+**********",
            "postal_code": "12345",
        }
        response = sub_service.create_subscription(self.user, self.company.slug, data)
        if not response.success:
            self.fail(f"Failed to create a free subscription {response.message}")

        # check if subscription is active
        self.assertTrue(response.data["is_active"])


class TestGetAllSubscriptions(BaseSetup):
    def test_get_all_subscriptions_success(self):
        subscriptions = [
            SubscriptionFactory(sub_company=self.company) for _ in range(4)
        ]
        SubscriptionFactory()
        response = sub_service.get_all_subscriptions(
            user=self.company.created_by,
            business_slug=self.company.slug,
        )
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 4)


class TestGetTotals(BaseSetup):
    def setUp(self):
        super().setUp()
        self.service = SubscriptionService()

    def test_get_totals_success(self):
        data = {
            "package_id": self.package.id,
            "end_date": "2025-12-31",
        }
        response = self.service.get_totals(data)
        if not response.success:
            self.fail(f"Failed to get totals {response.message}")
