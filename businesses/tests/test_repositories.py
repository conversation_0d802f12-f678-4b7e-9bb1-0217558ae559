from django.test import TestCase
from businesses.models import Company, Service
from businesses.repositories.business_repository import CompanyRepository
from businesses.repositories.service_repository import BusinessServiceRepository
from base.models import Category
from django.contrib.auth import get_user_model

User = get_user_model()

class TestCompanyRepository(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(username="testuser", password="password")
        self.repository = CompanyRepository()
        self.company = Company.objects.create(name="Original Name", created_by=self.user)
        self.valid_data = {
            "name": "Test Company",
            "description": "A test company",
        }
        self.update_data = {"name": "Updated Name"}

    def test_create_business_success(self):
        response = self.repository.create_business(self.user, self.valid_data)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.name, self.valid_data["name"])

    def test_update_business_success(self):
        response = self.repository.update_business(self.company.slug, self.update_data)
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, self.update_data["name"])

    def test_update_business_failure(self):
        response = self.repository.update_business(slug=9999, data=self.update_data)  # Non-existent slug
        self.assertFalse(response.success)
        self.assertIsNone(response.data)

    def test_delete_business_success(self):
        response = self.repository.delete_business(self.company.slug)
        self.assertTrue(response.success)
        self.assertIsNone(Company.objects.filter(slug=self.company.slug).first())

    def test_delete_business_failure(self):
        response = self.repository.delete_business(slug=9999)  # Non-existent Slug
        self.assertFalse(response.success)

    def test_get_business_success(self):
        response = self.repository.get_business(self.company.slug)
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, self.company.name)

    def test_get_business_failure(self):
        response = self.repository.get_business(slug=9999)  # Non-existent Slug
        self.assertFalse(response.success)
        self.assertIsNone(response.data)

    def test_get_businesses_by_name_success(self):
        Company.objects.create(name="Another Test Company", created_by=self.user)
        response = self.repository.get_businesses_by_name("Test")
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

    def test_get_businesses_by_name_failure(self):
        response = self.repository.get_businesses_by_name("NonExistent")
        self.assertFalse(response.success)
        self.assertIsNone(response.data)

    def test_get_all_businesses_success(self):
        Company.objects.create(name="Test Company 2", created_by=self.user)
        response = self.repository.get_all_businesses()
        self.assertTrue(response.success)
        self.assertEqual(len(response.data), 2)


class TestBusinessServiceRepository(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(username="testuser", password="password")
        self.category = Category.objects.create(name="testcategory")
        self.repository = BusinessServiceRepository()
        self.valid_data = {
            "name": "Test Service",
            "category_name": self.category,
        }
        self.update_data = {"name": "Updated Service"}
    
    def test_create_business_service_success(self):
        response = self.repository.create_service(data=self.valid_data)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.name, self.valid_data["name"])
    
    def test_update_business_service_success(self):
        service = Service.objects.create(name="Original Service", category_name=self.category, created_by=self.user)
        response = self.repository.update_service(slug=service.slug, data=self.update_data)
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, self.update_data["name"])
    
    def test_update_business_service_failure(self):
        response = self.repository.update_service(slug=9999, data=self.update_data)
        self.assertFalse(response.success)
        self.assertIsNone(response.data)
    
    def test_delete_business_service_success(self):
        service = Service.objects.create(name="Test Service", category_name=self.category, created_by=self.user)
        response = self.repository.delete_service(slug=service.slug)
        self.assertTrue(response.success)
        self.assertIsNone(Service.objects.filter(slug=service.slug).first())
    
    def test_delete_business_service_failure(self):
        response = self.repository.delete_service(slug=9999)
        self.assertFalse(response.success)
    
    def test_get_business_service_success(self):
        service = Service.objects.create(name="Test Service", category_name=self.category, created_by=self.user)
        response = self.repository.get_service(slug=service.slug)
        self.assertTrue(response.success)
        self.assertEqual(response.data.name, service.name)
    
    def test_get_business_service_failure(self):
        response = self.repository.get_service(slug=9999)
        self.assertFalse(response.success)
        self.assertIsNone(response.data)
    
    def test_get_all_services_success(self):
        Service.objects.create(name="Test Service 2", category_name=self.category, created_by=self.user)
        response = self.repository.get_all_services()
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
