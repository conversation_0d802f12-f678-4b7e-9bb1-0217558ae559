from django.contrib.auth.models import User
from datetime import datetime, timedelta
from django.test import TestCase, override_settings
from django.core.cache import cache
from django.urls import reverse
from rest_framework import status
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
import jwt
from core.settings import SECRET_KEY

from base.factory import UserFactory
from businesses.factory import CompanyFactory
from businesses.services.applications import BusinessApplicationService
from candidates.factory import ApplicationFactory, CandidateFactory
from jobs.factory import JobFactory


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class BusinessApplicationEndpointTestCase(TestCase):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        self.user = UserFactory()
        self.service = BusinessApplicationService()
        self.business = CompanyFactory(created_by=self.user)
        self.job = JobFactory(
            company_name=self.business,
        )

        # create 10 users
        self.users = [
            User.objects.create_user(
                username=f"testuser{i}",
                email=f"testuser{i}@example.com",
                password="password123",
            )
            for i in range(10)
        ]

        # create candidates
        self.candidates = [
            CandidateFactory(
                user=user,
            )
            for user in self.users
        ]

        # apply to the job
        for candidate in self.candidates:
            ApplicationFactory(
                applicant=candidate,
                job_applied=self.job,
                status="Pending",
            )

        token_payload = {
            "user_id": self.user.id,
            "exp": datetime.now() + timedelta(days=1),
        }
        jwt_token = jwt.encode(token_payload, SECRET_KEY, algorithm="HS256")

        # initialize client
        self.client = APIClient()

        # authenticate client
        self.client.force_authenticate(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {jwt_token}")

        self.endpoint = (
            f"/api/business/{self.business.slug}/jobs/{self.job.id}/applications/"
        )

    def tearDown(self):
        cache.clear()  # Clear cache after each test

    def test_get_job_applications(self):
        print("Business is: ", self.business.name)
        print("Business slug is: ", self.business.slug)
        print("Endpoint is: ", self.endpoint)
        response = self.client.get(self.endpoint)
        if not response.status_code == status.HTTP_200_OK:
            print(response.data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 10)

    def test_page_size_applications(self):
        response = self.client.get(f"{self.endpoint}?page_size=3")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data["results"]), 3)
