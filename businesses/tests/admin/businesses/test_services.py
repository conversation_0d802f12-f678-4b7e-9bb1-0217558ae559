from django.test import TransactionTestCase, override_settings
from django.core.cache import cache
from accounts.factory import CompanyFactory
from base.factory import UserFactory
from base.tests.setup import BaseSetup
from businesses.models import Company
from businesses.services.admin import AdminBusinessService
from django.contrib.auth.models import User

# services
service = AdminBusinessService()


# create business
@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestAdminCreateBusiness(TransactionTestCase):
    def setUp(self):
        self.admin = UserFactory()
        self.admin.is_admin = True
        self.admin.save()

    def test_create_business(self):
        data = {
            "contact_person": {
                "username": "test_user_admin",
                "email": "<EMAIL>",
                "password": "test_password",
                "first_name": "Test",
                "last_name": "User",
            },
            "business_data": {
                "name": "Testing Business Admin",
                "description": "Testing Business description",
                "industry": "Testing Business organization",
                "business_address": "123 Street",
            },
        }
        response = service.create_business(self.admin, data)
        if not response.success:
            self.fail(response.message)

        self.assertEqual(response.success, True)
        self.assertIsNotNone(User.objects.get(username="test_user_admin"))
        self.assertTrue(Company.objects.filter(name="Testing Business Admin").exists())


# get businesses
@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestAdminGetBusinesses(BaseSetup):
    def setUp(self):
        super().setUp()
        self.businesses = [CompanyFactory() for _ in range(3)]

    def test_get_businesses(self):
        response = service.list_businesses()
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)
        self.assertGreaterEqual(len(response.data), 3)


# update business
@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestUpdateBusinesses(BaseSetup):
    def setUp(self):
        super().setUp()
        self.business = CompanyFactory()

    def test_update_business(self):
        update_data = {
            "name": "Test Company",
            "description": "Updated Description",
            "languages": [
                {
                    "name": "English",
                },
                {
                    "name": "French",
                },
            ],
            "business_socials": [
                {
                    "link": "https://linkedin.com/company/test",
                    "handle": "testhandle",
                }
            ],
            "services": [{"name": "Web Development", "category_name": "Technology"}],
        }
        response = service.update_business(self.admin, self.business.slug, update_data)
        self.assertTrue(response.success)
        self.assertEqual(response.data["name"], "Test Company")


# delete business
@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestDeleteBusinesses(BaseSetup):
    def setUp(self):
        super().setUp()
        self.business = CompanyFactory()

    def test_delete_business(self):
        response = service.delete_business(self.admin, self.business.slug)
        print(response.data)
        self.assertTrue(response.success)
        self.assertEqual(response.data, None)


# activate a business
@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestActivateBusinesses(BaseSetup):
    def setUp(self):
        super().setUp()
        self.business = CompanyFactory()

    def test_activate_business(self):
        response = service.activate_business(self.business.slug)
        self.assertTrue(response.success)
        self.assertEqual(response.data["is_active"], True)


# deactivate a business
@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestDeactivateBusinesses(BaseSetup):
    def setUp(self):
        super().setUp()
        self.business = CompanyFactory(is_active=True)

    def test_deactivate_business(self):
        response = service.deactivate_business(self.business.slug)
        self.assertTrue(response.success)
        self.assertEqual(response.data["is_active"], False)
