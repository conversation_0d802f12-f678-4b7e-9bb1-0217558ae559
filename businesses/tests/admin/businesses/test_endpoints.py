from base.factory import UserFactory
from base.tests.setup import BaseSetup
from businesses.factory import CompanyFactory
from businesses.models import Company
from businesses.services.admin import AdminBusinessService
from django.contrib.auth.models import User
from django.test import override_settings
from django.core.cache import cache


service = AdminBusinessService()


# create business
@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestAdminCreateBusiness(BaseSetup):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        super().setUp()
    def test_create_business(self):
        data = {
            "contact_person": {
                "username": "test_user_admin",
                "email": "<EMAIL>",
                "password": "test_password",
                "first_name": "Test",
                "last_name": "User",
            },
            "business_data": {
                "name": "Testing Business Admin",
                "description": "Testing Business description",
                "industry": "Testing Business organization",
                "business_address": "123 Street",
            },
        }
        self._authenticate_user(self.admin)
        response = self.client.post(
            f"{self.admin_endpoints}/businesses/", data, format="json"
        )
        if not response.status_code == 201:
            self.fail(response.data)

        self.assertEqual(response.status_code, 201)
        self.assertIsNotNone(User.objects.get(username="test_user_admin"))
        self.assertTrue(Company.objects.filter(name="Testing Business Admin").exists())


# get businesses
@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestAdminGetBusinesses(BaseSetup):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        super().setUp()
        self.businesses = [CompanyFactory() for _ in range(3)]

    def test_get_businesses(self):

        self._authenticate_user(self.admin)
        response = self.client.get(f"{self.admin_endpoints}/businesses/")

        if not response.status_code == 200:
            self.fail(response.data)
        self.assertEqual(response.status_code, 200)
        self.assertIsNotNone(response.data)
        self.assertGreaterEqual(len(response.data), 3)


# update a business
@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestUpdateBusinesses(BaseSetup):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        super().setUp()
        self.business = CompanyFactory()

    def test_update_business(self):
        update_data = {
            "name": "Test Company",
            "description": "Updated Description",
            "languages": [
                {
                    "name": "English",
                },
                {
                    "name": "French",
                },
            ],
            "business_socials": [
                {
                    "link": "https://linkedin.com/company/test",
                    "handle": "testhandle",
                }
            ],
            "services": [{"name": "Web Development", "category_name": "Technology"}],
        }
        self._authenticate_user(self.admin)
        response = self.client.put(
            f"{self.admin_endpoints}/businesses/{self.business.slug}/",
            update_data,
            format="json",
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["name"], "Test Company")


# delete a business
@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestDeleteBusinesses(BaseSetup):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        super().setUp()
        self.business = CompanyFactory()

    def test_delete_business(self):
        self._authenticate_user(self.admin)

        response = self.client.delete(
            f"{self.admin_endpoints}/businesses/{self.business.slug}/"
        )

        self.assertEqual(response.status_code, 200)


# activate a business
@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestActivateBusinesses(BaseSetup):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        super().setUp()
        self.business = CompanyFactory()

    def test_activate_business(self):
        self._authenticate_user(self.admin)

        response = self.client.patch(
            f"{self.admin_endpoints}/businesses/{self.business.slug}/",
            {"action": "activate"},
        )
        self.assertEqual(response.status_code, 200)


# deactivate a business
@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class TestDeactivateBusinesses(BaseSetup):
    def setUp(self):
        cache.clear()  # Clear cache before each test
        super().setUp()
        self.business = CompanyFactory(is_active=True)

    def test_activate_business(self):
        self._authenticate_user(self.admin)

        response = self.client.patch(
            f"{self.admin_endpoints}/businesses/{self.business.slug}/",
            {"action": "deactivate"},
        )
        self.assertEqual(response.status_code, 200)
