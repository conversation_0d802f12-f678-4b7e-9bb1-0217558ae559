import copy
from django.db import transaction
from typing import Dict, Any, List, Optional
from django.core.exceptions import ValidationError
from accounts.services.profile_service import APIResponse
from base.models import Category
from base.services.logging import LoggingService
from businesses.models import Service
from businesses.repositories.service_repository import BusinessServiceRepository
from candidates.repositories.application_repository import RepositoryResponse
from rest_framework import status
from businesses.views.serializers import ServiceSerializer


logging_service = LoggingService()


class BusinessServiceService:
    def __init__(self):
        self.service_repository = BusinessServiceRepository()
        self.serializer = ServiceSerializer()
    
    def get_all_services(self, slug: str) -> APIResponse:
        """
        Retrieve all services for a business.
        """
        if not slug:
            return APIResponse(
                success=False,
                message="Slug is required.",
                data=None,
                status=status.HTTP_400_BAD_REQUEST
            )
        
        response = self.service_repository.get_services_by_slug(slug)
        
        if not response.success:
            return APIResponse(
                success=False,
                message=response.message,
                data=None,
                status=status.HTTP_404_NOT_FOUND,
            )
        
        serialized_data = ServiceSerializer.to_representation_list(response.data)
        
        return APIResponse(
            success=True,
            message="Services retrieved successfully.",
            data=serialized_data,
            status=status.HTTP_200_OK,
        )
    
    def get_service(self, slug: str) -> APIResponse:
        """
        Retrieve a single service by its Slug.
        """
        if not slug:
            return APIResponse(
                success=False,
                message="Slug is required.",
                data=None,
                status=status.HTTP_400_BAD_REQUEST
            )
        
        response = self.service_repository.get_service(slug)
        
        if not response.success:
            return APIResponse(
                success=False,
                message=response.message,
                data=None,
                status=status.HTTP_404_NOT_FOUND,
            )
        
        return APIResponse(
            success=True,
            message="Service retrieved successfully.",
            data=self.serializer.to_representation(response.data),
            status=status.HTTP_200_OK,
        )
    
    def create_service(self, data: Dict[str, Any]) -> APIResponse:
        """
        Create a new service for a business.
        """
        if "name" not in data:
            return APIResponse(
                success=False,
                message="Service name is required.",
                data=None,
                status=status.HTTP_400_BAD_REQUEST
            )
        service = self.service_repository.create_service(data=data)
        
        if not service.success:
            return APIResponse(
                success=False,
                message=service.message,
                data=None,
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        return APIResponse(
            success=True,
            message="Service created successfully.",
            data=self.serializer.to_representation(service.data),
            status=status.HTTP_201_CREATED,
        )
    
    def update_service(self, slug: str, data: Dict[str, Any]) -> APIResponse:
        """
        Update an existing service.
        """
        if not slug:
            return APIResponse(
                success=False,
                message="Slug is required.",
                data=None,
                status=status.HTTP_400_BAD_REQUEST,
            )
        update_service = self.service_repository.update_service(slug, data)
        
        if not update_service.success:
            return APIResponse(
                success=False,
                message=update_service.message,
                data=None,
                status=status.HTTP_404_NOT_FOUND,
            )
        
        return APIResponse(
            success=True,
            message="Service updated successfully.",
            data=self.serializer.to_representation(update_service.data),
            status=status.HTTP_200_OK,
        )
    
    def delete_service(self, slug: str) -> APIResponse:
        """
        Delete a service.
        """
        if not slug:
            return APIResponse(
                success=False,
                message="Slug is required.",
                data=None,
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        return self.service_repository.delete_service(slug)
    
    def get_or_create_service(self, name, category_name) -> APIResponse:
        try:
            category, _ = Category.objects.get_or_create(name=category_name)
            service, _ = Service.objects.get_or_create(name=name, category_name=category)
            return APIResponse(
                success=True,
                data=self.serializer.to_representation(service),
                message="Service retrieved or created successfully.",
                status=status.HTTP_200_OK,
            )
        except ValidationError as ve:
            logging_service.log_error(ve)
            return APIResponse(
                success=False,
                data=None,
                message="Failed to create or retrieve service",
                status_code=status.HTTP_400_BAD_REQUEST,
                )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Failed to create or retrieve service",
                status_code=status.HTTP_400_BAD_REQUEST,
                )
