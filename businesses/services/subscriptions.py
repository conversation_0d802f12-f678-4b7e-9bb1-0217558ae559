from functools import partial
from turtle import st
import uuid
from accounts.models import Package, Subscription
from accounts.serializers import GetSubscriptionSerializer
from base import serializers
from base.services.logging import LoggingService
from base.utils.status_checker import APIResponse
from businesses.models import BillingAddress, Company
from businesses.services.invoices import InvoiceService
from decimal import Decimal
from businesses.views.serializers import BillingAddressSerializer


logging_service = LoggingService()
invoices_service = InvoiceService()


class SubscriptionService:
    """CRUD operations for Subscription model"""

    def create_subscription(self, user, business_slug, data) -> APIResponse:
        """Create a new subscription"""

        # get company

        billing_details = {
            "billing_address": data.pop("billing_address", None),
            "billing_city": data.pop("billing_city", None),
            "billing_country": data.pop("billing_country", None),
            "email": data.pop("email", None),
            "phone_number": data.pop("phone_number", None),
            "postal_code": data.pop("postal_code", None),
        }

        try:
            company = Company.objects.get(slug=business_slug)

            # try saving billing details

            try:
                billing_details["name"] = f"{company.name} Billing Address"
                billing_details["billing_company"] = company.id
                serializer = BillingAddressSerializer(
                    data=billing_details, partial=True
                )
                if not serializer.is_valid():
                    logging_service.log_error(serializer.errors)
                    return APIResponse(
                        success=False,
                        data=None,
                        message="Invalid billing details.",
                    )
                serializer.save()
                # billing_address = BillingAddress.objects.get_or_create(
                #     **billing_details,
                #     billing_company=company,
                # )
            except Exception as e:
                logging_service.log_error(e)
                return APIResponse(
                    success=False,
                    data=None,
                    message="Failed to save billing details.",
                )

        except Company.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="Company not found.",
            )

        # get package
        try:
            package = Package.objects.get(id=data["package_id"])

        except Package.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="Package not found.",
            )

        # create subscription
        unique_suffix = str(uuid.uuid4())[:8]
        name = f"{user.first_name} {company.name} {unique_suffix}"
        subscription = Subscription.objects.create(
            name=name,
            user=company.created_by,
            sub_company=company,
            sub_package=package,
            end_date=data["end_date"],
        )

        # check if subscription price is zero, the activate it
        if package.price == 0:
            subscription.is_active = True
            subscription.save()

        serializer = GetSubscriptionSerializer(subscription)

        # create an invoice
        sub_data = serializer.data
        invoice_response = invoices_service.create_invoice(user, subscription)
        if not invoice_response.success:
            return APIResponse(
                success=False,
                message="Subscription created but failed to generate invoice",
                data=None,
                status=202,
            )
        sub_data["invoice_response"] = invoice_response.data
        return APIResponse(
            success=True,
            data=sub_data,
            message="Subscription created successfully.",
        )

    def get_subscription(self, subscription_id):
        """Get a subscription by id"""

    def update_subscription(self, subscription_id, logged_in_user, data):
        """Update a subscription by id"""

    def delete_subscription(self, subscription_id, logged_in_user):
        """Delete a subscription by id"""

    def get_all_subscriptions(
        self,
        user,
        business_slug,
    ) -> APIResponse:
        """Get all subscriptions"""
        try:
            company = Company.objects.get(slug=business_slug)
            subscriptions = Subscription.objects.filter(sub_company=company)
            serializer = GetSubscriptionSerializer(subscriptions, many=True)
            return APIResponse(
                success=True,
                data=serializer.data,
                message="Subscriptions retrieved successfully.",
            )

        except Company.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="Company not found.",
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message=f"Internal server error",
            )

    def get_totals(self, data) -> APIResponse:
        if not "package_id" in data or "end_date" not in data:
            return APIResponse(
                success=False,
                data=None,
                message="Package ID is required.",
            )
        try:
            package = Package.objects.get(id=data["package_id"])
            from datetime import datetime
            from dateutil.relativedelta import relativedelta
            from django.utils import timezone

            end_date = datetime.strptime(data["end_date"], "%Y-%m-%d").date()
            today = timezone.now().date()

            if end_date < today:
                return APIResponse(
                    success=False,
                    data=None,
                    message="End date must be in the future.",
                )

            delta = relativedelta(end_date, today)
            months_difference = delta.years * 12 + delta.months
            if delta.days > 0:
                months_difference += 1
            if months_difference < 1:
                months_difference = 1

            total_cost = months_difference * package.price
            tax = Decimal("0.18")
            tax_amount = total_cost * tax
            total_amount = total_cost + tax_amount

            return APIResponse(
                success=True,
                data={
                    "total_cost": float(round(total_cost, 2)),
                    "tax_amount": float(round(tax_amount, 2)),
                    "total_amount": float(round(total_amount, 2)),
                },
                message="Total cost calculated successfully.",
            )
        except Package.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="Package not found.",
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Internal server error",
            )

    # subscription specific methods

    def _activate_subscription(self, subscription):
        """Activate a subscription"""

    def _cancel_subscription(self, subscription):
        """Deactivate a subscription"""

    def _review_subscription(self, subscription):
        """Extend a subscription"""
