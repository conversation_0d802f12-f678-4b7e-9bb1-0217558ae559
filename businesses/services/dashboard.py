from base.services.logging import LoggingService
from businesses.views.serializers import BusinessSerializer, GetJobSerializer
from candidates.repositories.application_repository import RepositoryResponse
from candidates.repositories.chat_repository import ChatRepository
from candidates.repositories.application_repository import ApplicationRepository
from candidates.repositories.interview_repository import InterviewRepository
from candidates.views.serializers import GetInterviewSerializer
from jobs.repositories.job_repository import JobRepository
from businesses.repositories.business_repository import CompanyRepository as BusinessRepository


logging_service = LoggingService()


class DashboardService:
    """ Get data for business dashboard """
    
    def __init__(self):
        self.chat_repository = ChatRepository()
        self.application_repository = ApplicationRepository()
        self.interview_repository = InterviewRepository()
        self.job_repository = JobRepository()
        self.business_repository = BusinessRepository()
    
    def get_dashboard_data(self, slug):
        try:
            """ business data """
            business = self.business_repository.get_business(slug)
            if not business:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message="Business not found",
                )
            business_data = BusinessSerializer(business.data).data

            unread_messages = 0
            tot_jobs = 0
            interview_data = []
            ongoing_jobs_data = []
            application_stats = {}
            recommended_talents = []

            """ business messages """
            messages = self.chat_repository.get_chats_by_receiver_id(receiver_id=business_data["created_by"]["id"])
            if messages.success:
                unread_messages = messages.data.filter(status="Unread").count()
            
            """ business jobs """
            jobs = self.job_repository.get_all_jobs(business.data)
            if jobs.success:
                tot_jobs = jobs.data.count()
                ongoing_jobs = jobs.data.filter(status__iexact="Active")
                ongoing_jobs_data = GetJobSerializer(ongoing_jobs, many=True).data if ongoing_jobs else []
                
                """ Application Stats """
                application_statistics = self.get_application_statistics(jobs.data)
                if application_statistics.success:
                    application_stats = application_statistics.data
            
            """ business interviews """
            interviews = self.interview_repository.get_interviews_by_company(business.data)
            if interviews.success:
                interview_data = GetInterviewSerializer(
                    interviews.data.filter(status="Upcoming").order_by("-interview_date"), many=True).data

            stats = {
                "key_stats": {
                    "total_job_postings": tot_jobs,
                    "unread_messages": unread_messages,
                    "pending_applications": application_stats["total_application_pending"],
                    "rejected_applications": application_stats["total_application_rejected"]
                },
                "application_stats": application_stats["status_percentages"],
                "upcoming_interviews": interview_data,
                "ongoing_jobs": ongoing_jobs_data,
                "recommended_talents": recommended_talents,
                "business_data": business_data
            }
            return RepositoryResponse(
                success=True,
                data=stats,
                message="Dashboard data retrieved successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to fetch dashboard data",
            )
    
    def get_application_statistics(self, jobs):
        try:
            total_applications = 0
            status_counts = {
                "Pending": 0,
                "Shortlisted": 0,
                "Interview": 0,
                "Hired": 0,
                "Rejected": 0,
            }

            for job in jobs:
                applications = self.application_repository.get_applications_by_job(job)
                if applications.success:
                    total_applications += applications.data.count()
                    for app in applications.data:
                        status_counts[app.status] += 1

            status_percentages = {
                status: (round(count / total_applications * 100, 2) if total_applications > 0 else 0)
                for status, count in status_counts.items()
            }

            statistics_data =  {
                "total_application_rejected": status_counts["Rejected"],
                "total_application_pending": status_counts["Pending"],
                "status_percentages": status_percentages,
            }
            return RepositoryResponse(
                success=True,
                data=statistics_data,
                message="Application statistics retrieved successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to fetch application statistics",
            )