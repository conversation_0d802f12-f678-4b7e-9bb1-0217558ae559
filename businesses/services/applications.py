"""
This application service class manages business applications
it is responsible for CRUD operations on the application model
"""

from typing import Dict, Any, List, Optional
from django.core.exceptions import ValidationError
from base.email_template.interview_request import send_interview_request_email
from base.email_template.interview_request_confirmation import (
    send_interview_request_confirmation,
)
from base.email_template.send_hired_email import send_hired_email
from base.email_template.send_rejected_email import send_rejected_email
from base.email_template.send_shortlisted_email import send_shortlisted_email
from base.services.interviews import InterviewService
from base.services.logging import LoggingService
from base.utils.calculate_age import calculate_age
from base.utils.status_checker import APIResponse
from businesses.repositories.business_repository import (
    CompanyRepository,
    RepositoryResponse,
)
from candidates.models import Application
from candidates.repositories.application_repository import ApplicationRepository
from candidates.views.serializers import (
    ApplicationSerializer,
    GetApplicationSerializer,
    InterviewDataSerializer,
)
from jobs.repositories.job_repository import JobRepository
from django.core.paginator import Paginator, EmptyPage, InvalidPage
from django.db.models import Q

business_repo = CompanyRepository()
applications_repo = ApplicationRepository()
job_repository = JobRepository()
logging_service = LoggingService()
interview_service = InterviewService()


class BusinessApplicationService:

    # get applications by business slug
    def get_application_by_business_id(self, business_slug):
        business = business_repo.get_business(business_slug)
        if not business.success:
            return APIResponse(
                success=False,
                data=None,
                message=business.message,
                status=400,
            )

    # get applications by user id
    # get applications by candidate id
    # get applications by job id
    def get_applications_by_job_id(
        self,
        user,
        business_slug,
        job_id,
        page=1,
        page_size=10,
        search_query=None,
    ):
        # check if a user is allowed to view applications
        business = business_repo.get_business(business_slug)

        if not business.success:
            return APIResponse(
                success=False,
                data=None,
                message=business.message,
                status=400,
            )
        if not business.data.created_by.id == user.id:
            return APIResponse(
                success=False,
                data=None,
                message="User not authorized to view applications for this business",
                status=403,
            )
        job = job_repository.get_job_by_id(job_id)
        if not job.success:
            return APIResponse(
                success=False,
                data=None,
                message=job.message,
                status=400,
            )

        queryset = Application.objects.filter(job_applied_id=job.data.id)

        if search_query:
            queryset = queryset.filter(
                Q(applicant__user__first_name__icontains=search_query)
                | Q(applicant__user__last_name__icontains=search_query)
                | Q(applicant__user__email__icontains=search_query)
            )

        paginator = Paginator(queryset, page_size)
        try:
            paginated_applications = paginator.page(page)
        except (EmptyPage, InvalidPage):
            return APIResponse(
                success=False,
                data=None,
                message="Invalid page number",
                status=400,
            )
        applications = [
            {
                "id": app.id,
                "applicant": {
                    "id": app.applicant.id,
                    "first_name": app.applicant.user.first_name,
                    "last_name": app.applicant.user.last_name,
                    "email": app.applicant.user.email,
                    "gender": app.applicant.gender,
                    "age": calculate_age(app.applicant.date_of_birth),
                },
                "job_applied": {
                    "id": app.job_applied.id,
                    "title": app.job_applied.name,
                },
                "status": app.status,
                "created_at": app.date_created.isoformat(),
            }
            for app in paginated_applications.object_list
        ]
        # serializer = ApplicationSerializer(paginated_applications.object_list)
        return APIResponse(
            success=True,
            data={
                "results": applications,
                "total_pages": paginator.num_pages,
                "current_page": page,
                "total_count": paginator.count,
                "has_next": paginated_applications.has_next(),
                "has_previous": paginated_applications.has_previous(),
            },
            message="Applications retrieved successfully",
            status=200,
        )
    
    # get all business applications for all jobs
    def get_all_business_applications(
        self, user, business_slug, page=1, page_size=10, search_query=None
    ) -> APIResponse:
        business = business_repo.get_business(business_slug)
        if not business.success:
            return APIResponse(
                success=False,
                data=None,
                message=business.message,
                status=400,
            )
        if not business.data.created_by.id == user.id:
            return APIResponse(
                success=False,
                data=None,
                message="User not authorized to view applications for this business",
                status=403,
            )
        queryset = Application.objects.filter(job_applied__company_name=business.data)

        if search_query:
            queryset = queryset.filter(
                Q(applicant__user__first_name__icontains=search_query)
                | Q(applicant__user__last_name__icontains=search_query)
                | Q(applicant__user__email__icontains=search_query)
                | Q(job_applied__name__icontains=search_query)
                | Q(job_applied__company_name__icontains=search_query)
                | Q(job_applied__icontains=search_query)
            )
        paginator = Paginator(queryset, page_size)
        try:
            paginated_applications = paginator.page(page)
        except (EmptyPage, InvalidPage):
            return APIResponse(
                success=False,
                data=None,
                message="Invalid page number",
                status=400,
            )
        applications = GetApplicationSerializer(paginated_applications, many=True).data
        return APIResponse(
            success=True,
            data={
                "results": applications,
                "total_pages": paginator.num_pages,
                "current_page": page,
                "total_count": paginator.count,
                "has_next": paginated_applications.has_next(),
                "has_previous": paginated_applications.has_previous(),
            },
            message="Applications retrieved successfully",
            status=200,
        )


    # update applications
    def update_application(
        self, business_slug, application_id: str, data: Dict[str, Any]
    ) -> APIResponse:
        """
        Update an existing application.
        """
        if not application_id or not data:
            return APIResponse(
                success=False,
                message="Application ID is required.",
                data=None,
                status=400,
            )

        """
        Check if user has access to update the application
        1. User should be the one who created the business
        2. Application job should be the business's job
        """
        try:
            if not "status" in data:
                return APIResponse(
                    success=False,
                    message="Status is required.",
                    data=None,
                    status=400,
                )
            application_response = applications_repo.update_application_status(
                application_id, data["status"]
            )
            if not application_response.success:
                return APIResponse(
                    success=False,
                    message=application_response.message,
                    data=None,
                    status=400,
                )

            candidate_email = application_response.data.applicant.user.email
            candidate_first_name = application_response.data.applicant.user.first_name
            job_name = application_response.data.job_applied.name
            company_name = application_response.data.job_applied.company_name.name
            business_email = application_response.data.job_applied.company_name.email
            user_id = application_response.data.applicant.user.id
            job_id = application_response.data.job_applied.id

            if (
                "status" in data
                and data.get("status") == "Interview"
                and "interview" in data
            ):
                """
                Process interview

                1. Create an interview
                2. Notify the candidate
                3. Notify the business
                4. Update stats model
                5. Return the process interview response
                """
                """ create an interview using interview service"""
                user = application_response.data.applicant.user
                created_by = (
                    application_response.data.job_applied.company_name.created_by
                )
                job_applied = application_response.data.job_applied
                data = {"job_id": job_applied.id, **data.get("interview")}
                interview_response = interview_service.create_interview(
                    created_by,
                    user,
                    data,
                )
                if not interview_response.success:
                    return APIResponse(
                        success=False,
                        message=interview_response.message,
                        data=None,
                        status=400,
                    )

                """ Notify the candidate by email """
                send_interview_request_email(
                    candidate_email,
                    candidate_first_name,
                    job_name,
                    company_name,
                )

                """ Notify the business by email just as a confirmation that they have successfully requested the interview"""
                send_interview_request_confirmation(
                    business_email,
                    company_name,
                    job_name,
                    candidate_first_name,
                )

                """ Return the process interview response """
                application_serialized = GetApplicationSerializer(
                    application_response.data,
                ).data
                interview_serialized = InterviewDataSerializer(interview_response.data)
                return APIResponse(
                    success=True,
                    message="Application updated and Interview created successfully",
                    data={
                        "interview": application_serialized,
                        "application": interview_serialized.data,
                    },
                    status=200,
                )

            elif "status" in data and data.get("status") == "Shortlisted":
                """
                Process shortlisted application

                1. Notify candidate
                2. Update stats model
                3. Return application response
                """
                send_shortlisted_email(
                    candidate_email, candidate_first_name, job_name, company_name
                )
                application_serialized = GetApplicationSerializer(
                    application_response.data
                ).data
                return APIResponse(
                    success=True,
                    message="Application updated and Shortlisted email sent successfully",
                    data=application_serialized,
                    status=200,
                )

            elif "status" in data and data.get("status") == "Rejected":
                """
                Process rejected application

                1. Notify candidate
                2. Update stats model
                3. Return application response
                """
                send_rejected_email(
                    candidate_email, candidate_first_name, job_name, company_name
                )
                interview_response = interview_service.close_interview(user_id, job_id)
                if not interview_response.success:
                    if "not found" not in interview_response.message.lower():
                        return APIResponse(
                            success=False,
                            message=interview_response.message,
                            data=None,
                            status=400,
                        )
                application_serialized = GetApplicationSerializer(
                    application_response.data
                ).data
                return APIResponse(
                    success=True,
                    message="Application updated and Rejected email sent successfully",
                    data=application_serialized,
                    status=200,
                )
            elif "status" in data and data.get("status") == "Hired":
                """
                Process hired application

                1. Notify candidate
                2. Update stats model
                3. Return application response
                """
                send_hired_email(
                    candidate_email, candidate_first_name, job_name, company_name
                )
                interview_response = interview_service.close_interview(user_id, job_id)
                if not interview_response.success:
                    if "not found" not in interview_response.message.lower():
                        return APIResponse(
                            success=False,
                            message=interview_response.message,
                            data=None,
                            status=400,
                        )
                application_serialized = GetApplicationSerializer(
                    application_response.data
                ).data
                return APIResponse(
                    success=True,
                    message="Application updated and Hired email sent successfully",
                    data=application_serialized,
                    status=200,
                )
            else:
                return APIResponse(
                    success=False,
                    message="Invalid status update",
                    data=None,
                    status=400,
                )

        except ValidationError as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Validation error",
                data=None,
                status=400,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error updating application",
                data=None,
                status=500,
            )

    def delete_application(self, application_id: int) -> RepositoryResponse:
        """
        Delete an existing application.
        """
        if not application_id:
            return RepositoryResponse(
                success=False,
                message="Application ID is required.",
                data=None,
            )

        try:
            return self.repository.delete_application(application_id)
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Error deleting application",
                data=None,
            )
