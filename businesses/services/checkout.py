import stripe
import os

from accounts.models import Invoice
from base.services.logging import LoggingService
from base.utils.status_checker import APIResponse

login_service = LoggingService()


class StripeService:
    def __init__(self):
        self.stripe_secret_key = os.getenv("STRIPE_SECRET_KEY")
        if not self.stripe_secret_key:

            self.initialized = False
        else:
            self.initialized = True
            stripe.api_key = self.stripe_secret_key

    def initialize_payment(self, invoice_id) -> APIResponse:
        if not self.initialized:
            return APIResponse(
                success=False,
                data=None,
                message="Stripe service not initialized: Missing STRIPE_SECRET_KEY",
            )
        try:
            invoice = Invoice.objects.get(id=invoice_id)
            intent = stripe.PaymentIntent.create(
                amount=invoice.total_amount,
                currency="usd",
                description=f"Payment for invoice {invoice.invoice_number}",
                metadata={"invoice_id": invoice_id},
                payment_method_types=["card"],
            )

            return APIResponse(
                success=True,
                data={"client_secret": intent.client_secret},
                message="Payment intent created successfully",
            )
        except Exception as e:
            login_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Failed to create payment intent",
            )

    def update_invoice_status(self, invoice_id, payment_intent_id, status):
        if not self.initialized:
            return APIResponse(
                success=False,
                data=None,
                message="Stripe service not initialized: Missing STRIPE_SECRET_KEY",
            )
        try:
            payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)
            if payment_intent.status != "succeeded":
                return APIResponse(
                    success=False,
                    data=None,
                    message=f"Payment was not successful",
                )

            invoice = Invoice.objects.get(id=invoice_id)
            invoice.status = status
            invoice.save()
            return APIResponse(
                success=True,
                data=None,
                message=f"Invoice status updated to {status}",
            )
        except Invoice.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message=f"Invoice with id {invoice_id} not found",
            )

        except Exception as e:
            login_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Failed to update invoice status",
            )
