import json
from operator import inv
from turtle import pd
from typing import Any, Dict
import uuid
from django.core.paginator import Paginator, EmptyPage, InvalidPage
from django.db.models import Q, Prefetch
import logging
from accounts.models import Invoice, Subscription
from accounts.serializers import GetInvoicesSerializer
from base import serializers
from base.email_template.send_invoice import send_invoice_email
from base.models import Package
from base.services.logging import LoggingService
from base.utils.status_checker import APIResponse
from datetime import datetime
from dateutil.relativedelta import relativedelta
from fpdf import FPDF
import io
from rest_framework import status
from base.services.logging import LoggingService
from businesses.models import Company
import os
from django.conf import settings
from typing import Type


logging_service = LoggingService()


class InvoiceService:
    """CRUD operations for invoice"""

    def create_invoice(self, logged_in_user, subscription: Subscription) -> APIResponse:
        """
        Create a new invoice
        Subscription: A subscription instance that has start_date, end_date and package
        Amount: Is calculated by:
            1. Price of the package
            2. Number of months between the start and end dates
        """
        amount = 0
        if (
            not subscription
            or not subscription.sub_company
            or not subscription.start_date
            or not subscription.end_date
        ):
            return APIResponse(
                success=False,
                message="Missing required fields: subscription, start_date, end_date",
                data=None,
            )

        # handle dates
        start_date = (
            subscription.start_date
            if isinstance(subscription.start_date, datetime)
            else datetime.strptime(subscription.start_date, "%Y-%m-%dT%H:%M:%S.%f%z")
        )
        end_date_naive = (
            subscription.end_date
            if isinstance(subscription.end_date, datetime)
            else datetime.strptime(subscription.end_date, "%Y-%m-%d")
        )
        end_date = end_date_naive.replace(
            hour=0, minute=0, second=0, microsecond=0, tzinfo=start_date.tzinfo
        )

        # calculate months
        delta = relativedelta(end_date, start_date)
        total_months = delta.years * 12 + delta.months
        if delta.days > 0:
            total_months += 1

        amount = subscription.sub_package.price * total_months

        """ Create a unique name for the invoice to avoid slug conflicts """
        unique_suffix = str(uuid.uuid4())[:8]
        invoice_name = f"{subscription.name}-{unique_suffix}"

        invoice = Invoice.objects.create(
            inv_subscription=subscription,
            subtotal=amount,
            name=invoice_name,
        )

        # send invoice email
        invoice_response = self._send_invoice_email(invoice)
        serializer = GetInvoicesSerializer(invoice)
        return APIResponse(
            message="Invoices for subscription created successfully",
            success=True,
            data=serializer.data,
        )

    def _parse_filters(self, filters):
        """Helper function to parse filters string that might contain single or double quotes"""
        if not filters or isinstance(filters, dict):
            return filters

        try:
            return json.loads(filters)
        except json.JSONDecodeError:
            try:
                modified_filters = filters.replace("'", '"')
                return json.loads(modified_filters)
            except json.JSONDecodeError:
                return None

    def get_invoices(
        self,
        user=None,
        page: int = 1,
        page_size: int = 10,
        filters: Dict[str, Any] = {},
        search_query: str = None,
        order_by: str = "-date_created",
    ) -> APIResponse:
        """Get all invoices"""
        try:
            filter_conditions = Q()
            if filters:
                parsed_filters = self._parse_filters(filters)
                if parsed_filters is None:
                    return APIResponse(
                        success=False,
                        message="Invalid filters format",
                        data=None,
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                if invoice_number := parsed_filters.get("invoice_number"):
                    filter_conditions &= Q(invoice_number__icontains=invoice_number)
                if status_filter := parsed_filters.get("status"):
                    filter_conditions &= Q(status__icontains=status_filter)
                if payment_method := parsed_filters.get("payment_method"):
                    filter_conditions &= Q(payment_method__icontains=payment_method)
                if date_paid := parsed_filters.get("date_paid"):
                    filter_conditions &= Q(date_paid__icontains=date_paid)

            if search_query:
                filter_conditions &= (
                    Q(name__icontains=search_query)
                    | Q(invoice_number__icontains=search_query)
                    | Q(total_amount__icontains=search_query)
                    | Q(status__icontains=search_query)
                    | Q(payment_method__icontains=search_query)
                    | Q(date_paid__icontains=search_query)
                )
            queryset = (
                Invoice.objects.select_related("inv_subscription")
                .prefetch_related(
                    Prefetch(
                        "inv_subscription",
                        queryset=Company.objects.only("name"),
                    ),
                    Prefetch(
                        "inv_subscription",
                        queryset=Package.objects.only("name"),
                    ),
                )
                .filter(filter_conditions)
                .order_by(order_by)
                .distinct()
            )
            paginator = Paginator(queryset, page_size)
            try:
                paginated_invoices = paginator.page(page)
                serializer_data = GetInvoicesSerializer(
                    paginated_invoices, many=True
                ).data
            except (EmptyPage, InvalidPage):
                return APIResponse(
                    success=False,
                    data=None,
                    message="Invalid page number",
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return APIResponse(
                success=True,
                message="Invoices retrieved successfully",
                data={
                    "results": serializer_data,
                    "total_pages": paginator.num_pages,
                    "current_page": page,
                    "total_count": paginator.count,
                    "has_next": paginated_invoices.has_next(),
                    "has_previous": paginated_invoices.has_previous(),
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to retrieve invoices",
                data=None,
                status=status.HTTP_400_BAD_REQUEST,
            )

    def get_invoice(self, invoice_id) -> APIResponse:
        """Get an invoice by id"""
        try:
            invoice = Invoice.objects.get(id=invoice_id)
            serializer = GetInvoicesSerializer(invoice)
            return APIResponse(
                success=True,
                data=serializer.data,
                message="Invoice retrieved successfully",
                status=status.HTTP_200_OK,
            )
        except Invoice.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="Invoice does not exist",
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Failed to retrieve invoice",
                status=status.HTTP_400_BAD_REQUEST,
            )

    def update_invoice(self, invoice_id, data, logged_in_user=None) -> APIResponse:
        """Update an invoice by id"""
        try:
            invoice = Invoice.objects.get(id=invoice_id)

            if not "status" in data:
                return APIResponse(
                    success=False,
                    data=None,
                    message="Status is required",
                    status=status.HTTP_400_BAD_REQUEST,
                )

            valid_statuses = [choice[0] for choice in Invoice.INVOICE_STATUS_CHOICES]
            if data["status"] not in valid_statuses:
                return APIResponse(
                    success=False,
                    data=None,
                    message="Invalid status",
                    status=status.HTTP_400_BAD_REQUEST,
                )

            invoice.status = data["status"]
            invoice.save()

            serializer = GetInvoicesSerializer(invoice)
            return APIResponse(
                success=True,
                data=serializer.data,
                message="Invoice status updated successfully",
                status=status.HTTP_200_OK,
            )
        except Invoice.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="Invoice does not exist",
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Failed to update invoice status",
                status=status.HTTP_400_BAD_REQUEST,
            )

    def delete_invoice(self, invoice_id, logged_in_user):
        """Delete an invoice by id"""
        try:
            # get invoice
            invoice = Invoice.objects.get(id=invoice_id)

            if logged_in_user != invoice.inv_subscription.user:

                return APIResponse(
                    success=False,
                    message="Only the invoice owner can delete this invoice",
                    data=None,
                    status=403,
                )
            invoice.delete()
            return APIResponse(
                success=True,
                message="Invoice deleted successfully",
                data=None,
                status=200,
            )
        except Invoice.DoesNotExist:
            return APIResponse(
                success=False,
                message="Invoice not found",
                data=None,
                status=404,
            )

    def get_all_invoices(self, user, company_slug):
        """Get all invoices"""
        try:
            # get company
            company = Company.objects.get(slug=company_slug)

            # get subscriptions for company
            subscriptions = Subscription.objects.filter(sub_company=company)

            # get invoices for company
            invoices = Invoice.objects.filter(inv_subscription__in=subscriptions)

            serializer = GetInvoicesSerializer(invoices, many=True)
            return APIResponse(
                success=True,
                data=serializer.data,
                message="Invoices retrieved successfully",
            )
        except Company.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="Company not found",
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Failed to retrieve invoices",
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        # get subscriptions

        # get invoices for company

    # invoice specific methods
    def _calculate_invoice_total(self, invoice):
        """Calculate the total amount of an invoice"""

    def _generate_invoice_pdf(self, invoice) -> APIResponse:
        """Generate a PDF of an invoice"""
        try:
            invoice = Invoice.objects.get(id=invoice.id)
            subscription = invoice.inv_subscription
            company = subscription.sub_company
            package = subscription.sub_package

            # Initialize PDF
            pdf = FPDF()
            pdf.add_page()

            # Set font
            pdf.set_font("Courier", "B", 16)

            # Header
            pdf.cell(190, 10, "INVOICE", 0, 1, "C")
            pdf.line(10, 30, 200, 30)

            # Company and Invoice Details
            pdf.set_font("Courier", "", 12)
            pdf.cell(190, 10, f"Invoice Number: {invoice.invoice_number}", 0, 1, "R")
            pdf.cell(
                190, 10, f'Date: {invoice.date_created.strftime("%Y-%m-%d")}', 0, 1, "R"
            )
            pdf.cell(
                190, 10, f'Due Date: {invoice.due_date.strftime("%Y-%m-%d")}', 0, 1, "R"
            )

            # Company Information
            pdf.set_font("Courier", "B", 12)
            pdf.cell(190, 10, "Bill To:", 0, 1, "L")
            pdf.set_font("Courier", "", 12)
            pdf.cell(190, 10, f"Company: {company.name}", 0, 1, "L")
            pdf.cell(190, 10, f"Email: {company.email}", 0, 1, "L")

            # Client  Information
            pdf.set_font("Courier", "B", 12)
            pdf.cell(190, 10, "Bill To:", 0, 1, "L")
            pdf.set_font("Courier", "", 12)
            pdf.cell(190, 10, f"Company: {company.name}", 0, 1, "L")
            pdf.cell(190, 10, f"Email: {company.email}", 0, 1, "L")

            # Subscription Details
            pdf.ln(10)
            pdf.set_font("Courier", "B", 12)
            pdf.cell(190, 10, "Subscription Details", 0, 1, "L")
            pdf.set_font("Courier", "", 12)
            pdf.cell(190, 10, f"Package: {package.name}", 0, 1, "L")
            pdf.cell(
                190,
                10,
                f'Start Date: {subscription.start_date.strftime("%Y-%m-%d")}',
                0,
                1,
                "L",
            )
            pdf.cell(
                190,
                10,
                f'End Date: {subscription.end_date.strftime("%Y-%m-%d")}',
                0,
                1,
                "L",
            )

            # Payment Details
            pdf.ln(10)
            pdf.set_fill_color(200, 200, 200)
            pdf.cell(130, 10, "Description", 1, 0, "L", True)
            pdf.cell(60, 10, "Amount", 1, 1, "R", True)

            pdf.cell(130, 10, f"{package.name} Subscription", 1, 0, "L")
            pdf.cell(60, 10, f"${invoice.total_amount:.2f}", 1, 1, "R")

            pdf.cell(130, 10, "Total", 1, 0, "L", True)
            pdf.cell(60, 10, f"{invoice.total_amount:.2f}Rwf", 1, 1, "R", True)

            # Payment Status
            pdf.ln(10)
            pdf.set_font("Courier", "B", 12)
            pdf.cell(190, 10, f"Status: {invoice.status.upper()}", 0, 1, "L")
            if invoice.payment_method:
                pdf.cell(
                    190, 10, f"Payment Method: {invoice.payment_method}", 0, 1, "L"
                )

            # Footer
            pdf.set_y(-30)
            pdf.set_font("Courier", "I", 8)
            pdf.cell(190, 10, "Thank you for doing business with us!", 0, 1, "C")

            # Save PDF to BytesIO buffer
            buffer = io.BytesIO()
            pdf.output(buffer)
            buffer.seek(0)

            return APIResponse(
                success=True,
                message="Invoice PDF generated successfully",
                data=buffer,
                status=status.HTTP_200_OK,
            )

        except Invoice.DoesNotExist:
            return APIResponse(
                success=False,
                message=f"Invoice not found",
                data=None,
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to generate invoice PDF",
                data=None,
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def download_invoice_pdf(self, invoice_id) -> APIResponse:
        """Download a PDF of an invoice"""
        try:
            invoice_item = Invoice.objects.get(id=invoice_id)

            customer = {
                "name": invoice_item.inv_subscription.sub_company.name,
                "address": invoice_item.inv_subscription.sub_company.business_address,
            }
            invoice = {
                "invoice_number": invoice_item.invoice_number,
                "date": invoice_item.date_created.strftime("%Y-%m-%d"),
                "due_date": invoice_item.due_date.strftime("%Y-%m-%d"),
            }
            items = [
                {
                    "qty": invoice_item.inv_subscription.get_total_months(),
                    "description": f"{invoice_item.inv_subscription.sub_package.name} package for {invoice_item.inv_subscription.get_total_months()}",
                    "unit_price": invoice_item.inv_subscription.sub_package.price,
                    "amount": invoice_item.total_amount,
                },
            ]
            subtotal = float(invoice_item.total_amount)
            tax_amount = subtotal * 0.18
            # TODO: Check how this 18% VAT can be dynamic
            grand_total = float(subtotal) + float(tax_amount)
            pdf_response = self._generate_invoice_pdf_2(
                customer=customer,
                invoice=invoice,
                items=items,
                subtotal=round(subtotal, 2),
                tax_amount=round(tax_amount, 2),
                grand_total=round(grand_total, 2),
            )

            if pdf_response.success:
                return APIResponse(
                    success=True,
                    data=pdf_response.data,
                    message="Invoice PDF generated successfully",
                    status=status.HTTP_200_OK,
                )
            else:
                return APIResponse(
                    success=False,
                    data=None,
                    message=pdf_response.message or "Failed to generate invoice PDF",
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except Invoice.DoesNotExist:
            return APIResponse(
                success=False,
                data=None,
                message="Invoice does not exist",
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                data=None,
                message="Failed to download invoice PDF",
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _send_invoice_email(self, invoice: Type[Invoice]):
        """Send an email notification about an invoice"""
        invoice_pdf_response = self.download_invoice_pdf(invoice.id)

        if not invoice_pdf_response.success:
            return APIResponse(
                success=False,
                message="Failed to generate invoice PDF",
                data=None,
                status=status.HTTP_400_BAD_REQUEST,
            )
        invoice_pdf = invoice_pdf_response.data
        send_invoice_email(
            recipient_email=invoice.inv_subscription.sub_company.email,
            first_name=invoice.inv_subscription.sub_company.name,
            invoice_file=invoice_pdf,
            invoice_number=invoice.invoice_number,
        )

    def _cancel_invoice(self, invoice):
        """Cancel an invoice"""

    def _mark_invoice_as_paid(self, invoice):
        """Mark an invoice as paid"""

    def _mark_invoice_as_overdue(self, invoice):
        """Mark an invoice as overdue"""

    def _mark_invoice_as_refunded(self, invoice):
        """Mark an invoice as refunded"""

    def _mark_invoice_as_partial(self, invoice):
        """Mark an invoice as partial"""

    def _review_invoice(self, invoice):
        """Extend an invoice"""

    def _notify_invoice_due_date(self, invoice):
        """Notify the user about the due date of an invoice"""

    def _generate_invoice_pdf_2(
        self,
        customer,
        invoice,
        items,
        subtotal,
        tax_amount,
        grand_total,
    ) -> APIResponse:
        """Generate a PDF of an invoice based on the provided data"""
        try:
            # Sample invoice data (replace with your actual data source)
            company = {
                "name": "Job Match",
                "address": "Kigali, Rwanda, KG 137, Gate 39",
            }

            logo_path = os.path.join(settings.MEDIA_ROOT, "job-match-logo.png")
            if not logo_path:
                logging_service.log_error("Logo file not found")
                return APIResponse(
                    success=False,
                    message="Failed to generate PDF file, logo not found",
                    data=None,
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Initialize PDF
            pdf = FPDF()
            pdf.add_page()
            pdf.set_auto_page_break(auto=True, margin=15)

            # From Section
            pdf.set_font("Courier", "B", 10)
            pdf.cell(95, 5, "FROM", 0, 1)
            pdf.set_font("Courier", "", 10)
            pdf.cell(95, 5, company["name"], 0, 1)
            pdf.cell(95, 5, company["address"], 0, 1)

            # Logo
            pdf.set_xy(160, 10)
            pdf.image(logo_path, x=160, y=10, w=40)

            # Title
            pdf.set_xy(105, 30)
            pdf.set_font("Courier", "B", 16)
            pdf.cell(95, 10, "INVOICE", 0, 1, "R")

            # To Section
            pdf.set_xy(10, 40)
            pdf.set_font("Courier", "B", 10)
            pdf.cell(95, 5, "TO", 0, 1)
            pdf.set_font("Courier", "", 10)
            pdf.cell(95, 5, customer["name"], 0, 1)
            pdf.cell(95, 5, customer["address"], 0, 1)

            # Invoice Details
            pdf.set_font("Courier", "", 10)
            pdf.set_xy(105, 40)
            pdf.cell(95, 5, f"Invoice #: {invoice['invoice_number']}", 0, 1, "R")
            pdf.cell(190, 5, f"Invoice Date: {invoice['date']}", 0, 1, "R")
            pdf.cell(190, 5, f"Due Date: {invoice['due_date']}", 0, 1, "R")

            # Items Table Header
            pdf.ln(10)
            table_width = 20 + 110 + 30 + 30
            start_x = (pdf.w - table_width) / 2
            y_table = pdf.get_y()
            pdf.set_xy(start_x, y_table)
            pdf.set_fill_color(223, 201, 134)
            pdf.set_text_color(0, 0, 0)
            pdf.set_font("Courier", "B", 10)
            pdf.cell(20, 10, "QTY", 1, 0, "C", True)
            pdf.cell(110, 10, "Description", 1, 0, "L", True)
            pdf.cell(30, 10, "Unit Price", 1, 0, "C", True)
            pdf.cell(30, 10, "Amount", 1, 1, "C", True)
            pdf.set_text_color(0, 0, 0)

            # Items Table Content
            pdf.set_font("Courier", "", 10)
            for item in items:
                pdf.set_x(start_x)
                pdf.cell(20, 10, str(item["qty"]), 1, 0, "C")
                pdf.cell(110, 10, item["description"], 1, 0, "L")
                pdf.cell(30, 10, f"{item['unit_price']:.2f}", 1, 0, "C")
                pdf.cell(30, 10, f"{item['amount']:.2f}", 1, 1, "C")
            # After table, get current y for next section
            y_after_table = pdf.get_y() + 5

            # Totals
            pdf.set_y(y_after_table)
            pdf.set_x(start_x + 20 + 110)  # right align totals to table
            pdf.set_font("Courier", "B", 10)
            pdf.cell(30, 10, "Subtotal", 0, 0, "R")
            pdf.cell(30, 10, f"{subtotal}", 0, 1, "C")
            pdf.set_x(start_x + 20 + 110)
            pdf.cell(30, 10, f"VAT (18%)", 0, 0, "R")
            pdf.cell(30, 10, f"{tax_amount}", 0, 1, "C")
            pdf.set_x(start_x + 20 + 110)
            pdf.cell(30, 10, "TOTAL", 0, 0, "R")
            pdf.cell(30, 10, f"{grand_total}", 0, 1, "C")

            # Terms and Conditions (below totals, dynamic Y)
            y_terms = pdf.get_y() + 10
            pdf.set_y(y_terms)
            pdf.set_font("Courier", "B", 10)
            pdf.set_text_color(83, 83, 83)
            pdf.cell(0, 10, "TERMS AND CONDITIONS", 0, 1)
            pdf.set_font("Courier", "", 10)
            pdf.cell(0, 5, "Payment is due within 14 days of project completion", 0, 1)
            pdf.cell(0, 5, "QTY is how many months a subscription will last", 0, 1)
            pdf.cell(
                0,
                5,
                "All checks to be made to [ We will replace this with bank details ]",
                0,
                1,
            )
            pdf.cell(0, 5, "", 0, 1)  # Spacer
            pdf.cell(0, 5, "Thank you for your business!", 0, 1)
            # Save PDF to BytesIO buffer
            buffer = io.BytesIO()
            pdf.output(buffer)
            buffer.seek(0)

            return APIResponse(
                success=True,
                message="Invoice PDF generated successfully",
                data=buffer,
                status=200,
            )
        except Exception as e:
            logging_service.log_error(e)
            print(e)
            return APIResponse(
                success=False,
                data=None,
                message="Failed to generate pdf, internal server error",
            )
