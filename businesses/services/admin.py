from django.contrib.auth.models import User
from accounts.repositories.user_repository import UserRepository
from accounts.services.user_services import UserService
from base.services.logging import LoggingService
from base.utils.status_checker import APIResponse
from businesses.repositories.business_repository import CompanyRepository
from businesses.services.business import BusinessService
from businesses.views.serializers import BusinessSerializer

user_repository = UserRepository()
business_service = BusinessService()
business_repo = CompanyRepository()
logging_service = LoggingService()


class AdminBusinessService:
    # TODO: Check if a user has business management permissions
    # def __init__(self, user: User):
    #     if not user.is_superuser or not user.is_staff():
    #         raise PermissionError("User does not have admin permissions")
    #     self.user = user

    def create_business(self, user, data):
        """
        Create a business on behalf of the company
        Data should have:
            1. contact_person: First name, last name, email address
            2. business_data: Business data
        """
        # TOD: Check if user has access tp create a business

        if not "contact_person" in data:
            return APIResponse(
                success=False, data=None, message="Contact person data are required."
            )

        if not "business_data" in data:
            return APIResponse(
                success=False, data=None, message="Business data are required."
            )

        contact_person_data = data["contact_person"]
        business_data = data["business_data"]
        # create user

        try:
            # generate a strong password
            # TODO: Implement password generation
            strong_password = ""

            # create user with roles and permissions
            user_response = user_repository.create_user(contact_person_data)

            if not user_response.success:
                return user_response
            user_response.data.set_password(strong_password)

            # create a business
            business_response = business_service.create_business(
                user_response.data,
                business_data,
            )
            return business_response

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False, data=None, message="Failed to create contact person"
            )

    def update_business(self, user, business_slug, data):
        # TODO: Check if user has access to update a business
        return business_service.update_business(business_slug, user, data)

    def delete_business(self, user, business_slug):
        # TODO: Check if user has access to delete a business
        return business_service.delete_business(user, business_slug)

    def get_business_details(self, business_slug):
        # TODO: Check if user has access to view a business details
        return business_service.get_business_by_slug(business_slug)

    def list_businesses(self, page=1, page_size=10):
        return business_service.get_all_businesses(page=page, page_size=page_size)

    def get_business_overview(self, business_id):
        pass

    def activate_business(self, business_slug):
        try:
            business_response = business_repo.activate_business(business_slug)
            if not business_response.success:
                return APIResponse(success=False, message=business_response.message)

            serializer = BusinessSerializer(business_response.data)
            return APIResponse(
                success=True,
                message="Business activated successfully",
                data=serializer.data,
                status=200,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to activate business",
            )

    def deactivate_business(self, business_id):
        try:
            business_response = business_repo.deactivate_business(business_id)
            if not business_response.success:
                return APIResponse(success=False, message=business_response.message)

            serializer = BusinessSerializer(business_response.data)
            return APIResponse(
                success=True,
                message="Business deactivated successfully",
                data=serializer.data,
                status=200,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to deactivate business",
            )
