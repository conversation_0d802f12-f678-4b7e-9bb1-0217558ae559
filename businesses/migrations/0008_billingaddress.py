# Generated by Django 4.2.17 on 2025-05-28 10:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('base', '0009_packageservice_package'),
        ('businesses', '0007_company_is_active_company_is_verified'),
    ]

    operations = [
        migrations.CreateModel(
            name='BillingAddress',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('email', models.EmailField(blank=True, max_length=255, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=255, null=True)),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('billing_address', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='base.address')),
                ('billing_city', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='base.city')),
                ('billing_company', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='businesses.company')),
            ],
            options={
                'verbose_name_plural': 'Billing Addresses',
                'unique_together': {('billing_address', 'billing_city', 'billing_company')},
            },
            bases=('base.basemodel',),
        ),
    ]
