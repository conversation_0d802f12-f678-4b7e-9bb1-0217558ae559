# Generated by Django 4.2.11 on 2024-04-23 10:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('base', '0004_userlanguage'),
        ('businesses', '0002_remove_company_account'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='business_email',
            field=models.Char<PERSON>ield(max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='business_languages',
            field=models.ManyToManyField(to='base.userlanguage'),
        ),
        migrations.AddField(
            model_name='company',
            name='business_socials',
            field=models.ManyToManyField(to='base.socialmediaplatforms'),
        ),
        migrations.AddField(
            model_name='company',
            name='phone_number',
            field=models.Char<PERSON>ield(max_length=255, null=True),
        ),
    ]
