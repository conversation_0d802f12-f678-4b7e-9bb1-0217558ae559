# Generated by Django 4.2.11 on 2024-04-15 17:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('base', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Service',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('description', models.CharField(max_length=500, null=True)),
                ('category_name', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='base.category')),
            ],
            bases=('base.basemodel',),
        ),
        migrations.CreateModel(
            name='Company',
            fields=[
                ('basemodel_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='base.basemodel')),
                ('business_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('logo', models.ImageField(null=True, upload_to='logos')),
                ('description', models.TextField(blank=True)),
                ('website_url', models.URLField(blank=True)),
                ('established_at', models.DateTimeField(null=True)),
                ('number_of_employees', models.IntegerField(null=True)),
                ('account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('business_address', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='base.city')),
                ('industry', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='base.category')),
                ('services', models.ManyToManyField(to='businesses.service')),
            ],
            options={
                'verbose_name_plural': 'Companies ',
            },
            bases=('base.basemodel',),
        ),
    ]
