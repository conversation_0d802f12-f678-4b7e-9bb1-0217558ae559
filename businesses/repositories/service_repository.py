from base.services.logging import LoggingService
from businesses.models import Company, Service
from dataclasses import dataclass
from typing import Union, Optional, Dict, List
from django.db.models import Model
from django.db import transaction


logging_service = LoggingService()


@dataclass
class RepositoryResponse:
    success: bool
    message: str
    data: Optional[Union[Service, Dict, List, Model]]


class BusinessServiceRepository:
    """Create a new business service"""

    @transaction.atomic
    def create_service(self, data):
        # we need to implement updating the cache here
        try:
            service = Service.objects.create(**data)
            return RepositoryResponse(
                success=True,
                data=service,
                message="Service created successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to create service",
            )

    """Update an existing business service"""

    @transaction.atomic
    def update_service(self, slug, data):
        try:
            service = Service.objects.get(slug=slug)
            for key, value in data.items():
                setattr(service, key, value)
            service.save()
            return RepositoryResponse(
                success=True,
                data=service,
                message="Service updated successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to update service",
            )

    """Delete a business service"""

    @transaction.atomic
    def delete_service(self, slug):
        # we should update the cache here
        try:
            service = Service.objects.get(slug=slug)
            service.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Service deleted successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to delete service",
            )

    """Get a business service by id"""

    def get_service(self, slug):
        try:
            service = Service.objects.get(slug=slug)
            return RepositoryResponse(
                success=True,
                data=service,
                message="Service retrieved successfully",
            )
        except Service.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Service does not exist",
            )

    """Get all business services"""

    def get_all_services(self):
        # we need to implement getting users from the cache first
        try:
            services = Service.objects.all()
            return RepositoryResponse(
                success=True,
                data=services,
                message="All services retrieved successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to retrieve",
            )

    """Get services by business slug"""

    def get_services_by_slug(self, slug):
        try:
            business = Company.objects.filter(slug=slug).first()
            if not business:
                return RepositoryResponse(
                    success=False,
                    data=None,
                    message=f"No company found with slug {slug}",
                )
            print(f"get service by slug, company: {business}")

            services = Service.objects.filter(company=business)
            print(f"get service by slug, services: {services}")
            return RepositoryResponse(
                success=True,
                data=list(services),
                message=f"Services retrieved successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to retrieve service",
            )
