from base.services.logging import LoggingService
from businesses.models import Company
from dataclasses import dataclass
from typing import Union, Optional, Dict, List
from django.db.models import Model
from django.db import transaction


logging_service = LoggingService()


@dataclass
class RepositoryResponse:
    success: bool
    message: str
    data: Optional[Union[Company, Dict, List, Model]]


class CompanyRepository:
    """Create a new business"""

    @transaction.atomic
    def create_business(self, user, data):
        # we need to implement updating the cache here
        try:
            business = Company.objects.create(created_by=user, **data)
            return RepositoryResponse(
                success=True,
                data=business,
                message="Business created successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to create business",
            )

    """Update an existing business"""

    @transaction.atomic
    def update_business(self, slug, data):
        try:
            business = Company.objects.get(slug=slug)
            for key, value in data.items():
                setattr(business, key, value)
            business.save()
            return RepositoryResponse(
                success=True,
                data=business,
                message="Business updated successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to update the business",
            )

    """delete a business"""

    @transaction.atomic
    def delete_business(self, slug):
        try:
            business = Company.objects.get(slug=slug)
            business.delete()
            return RepositoryResponse(
                success=True,
                data=None,
                message="Business deleted successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to delete",
            )

    """Get a business by id"""

    def get_business(self, slug) -> RepositoryResponse:
        try:
            business = Company.objects.get(slug=slug)
            # print(business.name)
            return RepositoryResponse(
                success=True,
                data=business,
                message="Business retrieved successfully",
            )
        except Company.DoesNotExist:
            return RepositoryResponse(
                success=False,
                data=None,
                message="Business does not exist",
            )

    """Get businesses by name"""

    def get_businesses_by_name(self, name):
        try:
            businesses = Company.objects.get(name__icontains=name)
            return RepositoryResponse(
                success=True,
                data=businesses,
                message="Businesses retrieved successfully by name",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to retrieve",
            )

    """Get all businesses"""

    def get_all_businesses(self):
        # we need to implement getting users from the cache first
        try:
            businesses = Company.objects.all()
            return RepositoryResponse(
                success=True,
                data=businesses,
                message="All businesses retrieved successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to retrieve all business",
            )

    """Activate a business"""

    def activate_business(self, business_slug):
        try:
            business = Company.objects.get(slug=business_slug)
            business.is_active = True
            business.save()
            return RepositoryResponse(
                success=True,
                data=business,
                message="Business activated successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to activate business",
            )

    """Deactivate a business"""

    def deactivate_business(self, business_slug):
        try:
            business = Company.objects.get(slug=business_slug)
            business.is_active = False
            business.save()
            return RepositoryResponse(
                success=True,
                data=business,
                message="Business deactivated successfully",
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                data=None,
                message="Failed to deactivate business",
            )
