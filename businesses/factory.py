import factory
from factory.django import DjangoModelFactory
from datetime import timezone
from .models import Company, Service


class ServiceFactory(DjangoModelFactory):
    class Meta:
        model = Service

    name = factory.Sequence(lambda n: f"Service {n}")
    description = factory.Faker("paragraph")
    created_by = factory.SubFactory("base.factory.UserFactory")


class CompanyFactory(DjangoModelFactory):
    class Meta:
        model = Company

    name = factory.Sequence(lambda n: f"Company {n}")
    business_id = factory.Faker("uuid4")
    email = factory.Faker("company_email")
    phone_number = factory.Faker("phone_number")
    description = factory.Faker("catch_phrase")
    website_url = factory.Faker("url")
    established_at = factory.Faker("date_time", tzinfo=timezone.utc)
    number_of_employees = factory.Faker("random_int", min=1, max=10000)
    business_address = factory.Faker("address")
    industry = factory.Faker("job")
    created_by = factory.SubFactory("base.factory.UserFactory")

    @factory.post_generation
    def services(self, create, extracted, **kwargs):
        if not create:
            return

        if extracted:
            for service in extracted:
                self.services.add(service)
        else:
            self.services.add(ServiceFactory())
