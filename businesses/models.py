import email
import uuid
from django.db import models
from base.models import BaseModel, SocialMediaPlatforms, UserLanguage
from django.contrib.auth.models import User
from base.models import Address, City

from base.models import Category as SkillsCategory


class Service(BaseModel):
    description = models.CharField(max_length=500, null=True)
    category_name = models.ForeignKey(
        SkillsCategory, on_delete=models.CASCADE, blank=True, null=True
    )


# Create your models here.
class Company(BaseModel):
    business_id = models.UUIDField(default=uuid.uuid4, unique=True)
    email = models.CharField(max_length=255, null=True)
    business_socials = models.ManyToManyField(SocialMediaPlatforms)
    languages = models.ManyToManyField(UserLanguage)
    phone_number = models.CharField(max_length=255, null=True)
    logo = models.ImageField(upload_to="logos", null=True)
    description = models.TextField(blank=True)
    website_url = models.URLField(blank=True)
    established_at = models.DateTimeField(auto_now_add=False, null=True)
    number_of_employees = models.IntegerField(null=True)
    business_address = models.CharField(max_length=255, null=True, blank=True)
    services = models.ManyToManyField(Service)
    industry = models.CharField(max_length=255, null=True, blank=True)
    is_active = models.BooleanField(default=False)
    is_verified = models.BooleanField(default=False)

    class Meta:
        verbose_name_plural = "Companies "


class BillingAddress(BaseModel):
    billing_address = models.CharField(max_length=255, null=True, blank=True)
    billing_country = models.CharField(max_length=255, null=True, blank=True)
    billing_city = models.CharField(max_length=255, null=True, blank=True)
    billing_company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True)
    email = models.EmailField(max_length=255, null=True, blank=True)
    phone_number = models.CharField(max_length=255, null=True, blank=True)
    postal_code = models.CharField(max_length=20, null=True, blank=True)

    class Meta:
        verbose_name_plural = "Billing Addresses"
