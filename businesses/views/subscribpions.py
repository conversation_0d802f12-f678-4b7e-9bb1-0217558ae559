from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated

from base.services.logging import LoggingService
from businesses.services.subscriptions import SubscriptionService


sub_service = SubscriptionService()
logging_service = LoggingService()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def business_subscriptions(request, business_slug):
    try:
        if request.method == "POST":
            response = sub_service.create_subscription(
                request.user, business_slug, request.data
            )
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.status,
                )
            return Response(
                response.data,
                status=status.HTTP_201_CREATED,
            )
        elif request.method == "GET":
            parameters = request.query_params
            if "totals" in parameters and parameters.get("totals") == "true":
                data = {
                    "package_id": parameters.get("package_id"),
                    "end_date": parameters.get("end_date"),
                }
                totals = sub_service.get_totals(data)

                if not totals.success:
                    return Response(
                        {"error": totals.message},
                        status=totals.status,
                    )
                print("Totals:", totals.data)
                return Response(totals.data, status=status.HTTP_200_OK)
            else:
                response = sub_service.get_all_subscriptions(
                    user=request.user,
                    business_slug=business_slug,
                )
                if not response.success:
                    return Response({"error": response.message}, status=response.status)
                return Response(response.data, status=status.HTTP_200_OK)

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Failed to fetch subscriptions"},
            status=status.HTTP_400_BAD_REQUEST,
        )


@api_view(["GET", "PUT", "DELETE"])
@permission_classes([IsAuthenticated])
def business_subscription(request, subscription_id):
    pass
