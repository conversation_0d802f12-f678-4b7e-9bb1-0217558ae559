from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.http import HttpResponse

from base.services.logging import LoggingService
from businesses.services.invoices import InvoiceService


logging_service = LoggingService()
invoices_service = InvoiceService()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def business_invoices_api_view(request, business_slug):
    try:

        response = invoices_service.get_all_invoices(
            request.user,
            business_slug,
        )
        if not response.success:
            return Response(response.data, status=status.HTTP_400_BAD_REQUEST)
        return Response(response.data)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An error occurred while getting business invoices."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def business_invoice_details_api_view(request, business_slug, invoice_id):
    try:
        response = invoices_service.get_invoice(
            invoice_id,
        )
        if not response.success:
            return Response(response.data, status=status.HTTP_400_BAD_REQUEST)
        return Response(response.data)
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An error occurred while getting invoice details."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_invoice_pdf(request, business_slug, invoice_id):
    try:
        response = invoices_service.download_invoice_pdf(
            invoice_id,
        )
        if not response.success:
            return HttpResponse(response.data, status=status.HTTP_400_BAD_REQUEST)
        return HttpResponse(response.data, content_type="application/pdf")
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An error occurred while getting invoice PDF."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
