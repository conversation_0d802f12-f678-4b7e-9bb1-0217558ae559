from rest_framework import serializers
from accounts.serializers import UserSerializer
from base.models import City, Skill
from businesses.models import BillingAddress, Company, Service
from base.models import Category as BusinessCategory
from candidates.views.serializers import LanguageSerializer
from jobs.models import *
from base.serializers import HumanReadableDateField, SocialMediaPlatformsSerializer


class BusinessIndustrySerializer(serializers.ModelSerializer):
    class Meta:
        model = BusinessCategory
        fields = "__all__"


class CompanyCitySerializer(serializers.ModelSerializer):
    class Meta:
        model = City
        fields = "__all__"


class ServiceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Service
        fields = "__all__"

    @classmethod
    def to_representation_list(cls, instances):
        serializer = cls()
        return [serializer.to_representation(instance) for instance in instances]


class BusinessSerializer(serializers.ModelSerializer):
    created_by = UserSerializer()
    established_at = HumanReadableDateField()
    services = ServiceSerializer(many=True)
    languages = LanguageSerializer(many=True)
    business_socials = SocialMediaPlatformsSerializer(many=True)

    class Meta:
        model = Company
        fields = "__all__"


class JobTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = JobType
        fields = ["name"]


class WorkTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = WorkType
        fields = ["name"]


class ExperienceLevelSerializer(serializers.ModelSerializer):
    class Meta:
        model = ExperienceLevel
        fields = ["name"]


class BenefitsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Benefit
        fields = "__all__"


class JobRequiredSkillSerializer(serializers.ModelSerializer):
    class Meta:
        model = JobSkill
        fields = ["name"]


class JobJobSkillsCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = JobSkillsCategory
        fields = ["name"]


class SkillSerializer(serializers.ModelSerializer):
    class Meta:
        model = Skill
        fields = "__all__"


class JobSerializer(serializers.ModelSerializer):
    company_name = BusinessSerializer()
    location = serializers.StringRelatedField()
    job_type = serializers.StringRelatedField()
    work_type = serializers.StringRelatedField()
    experience_level = serializers.StringRelatedField()
    benefits = BenefitsSerializer(many=True)
    required_skills = JobRequiredSkillSerializer(many=True)
    date_created = HumanReadableDateField()
    date_updated = HumanReadableDateField()
    created_by = UserSerializer()

    class Meta:
        model = Job
        fields = "__all__"


class GetJobSerializer(serializers.ModelSerializer):
    benefits = BenefitsSerializer(many=True)
    required_skills = JobRequiredSkillSerializer(many=True)
    date_created = HumanReadableDateField()
    date_updated = HumanReadableDateField()

    class Meta:
        model = Job
        fields = "__all__"


class BillingAddressSerializer(serializers.ModelSerializer):
    class Meta:
        model = BillingAddress
        fields = "__all__"
