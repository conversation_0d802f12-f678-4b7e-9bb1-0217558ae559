from accounts.views.services import verify_user
from businesses.models import Company, Service
from base.models import Category as CompanyCategory
from uuid import UUID
from businesses.views.serializers import (
    BusinessSerializer,
    JobSerializer,
    ServiceSerializer,
    BusinessIndustrySerializer,
)
from rest_framework.response import Response
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated

from candidates.models import Application, Candidate
from candidates.views.serializers import CandidateSerializer
from jobs.models import Job
from businesses.services.business import BusinessService
from businesses.views.serializers import BusinessSerializer

business_service = BusinessService()


# service categories

@api_view(["POST"])
def business_services(request, slug):
    business_services = request.data.get("services")
    if business_services is None:
        return Response(
            {"message": "No service submitted found"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    try:
        business = Company.objects.get(slug=slug)
        for item in business_services:
            services_name = item
            service, created = Service.objects.get_or_create(name=services_name)
            if created:
                business.services.add(service)
            else:
                business.services.add(service)

    except Company.DoesNotExist:
        return Response(
            {"message": "No business found"}, status=status.HTTP_404_NOT_FOUND
        )
    return Response({"message": "We go the post"}, status=200)


@api_view(["GET"])
def business_industries(request):
    if request.method == "GET":
        industries = CompanyCategory.objects.all()
        serializer = BusinessIndustrySerializer(industries, many=True)
        return Response({"industries": serializer.data}, status=status.HTTP_200_OK)


@api_view(["GET"])
def business_services_list(request, business_slug):

    if request.method == "GET":
        id = request.query_params.get("id")
        try:
            business = Company.objects.get(slug=business_slug)

            services = Service.objects.filter(category_name=business.industry)
            serializer = ServiceSerializer(services, many=True)
            return Response({"services": serializer.data}, status=status.HTTP_200_OK)
        except Company.DoesNotExist:
            return Response(
                {"message": "Business with that ID does not found"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            print(e)
            return Response(
                {"message": f"There was an eeeee: {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def business_profile(request, business_slug):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_400_BAD_REQUEST,
        )

    try:
        business_data = Company.objects.get(slug=business_slug)
        business = BusinessSerializer(business_data)

        business_jobs = Job.objects.filter(company_name=business_data)
        jobs = JobSerializer(business_jobs, many=True)

        employees_data = []
        if business_jobs.exists():
            for job in business_jobs:
                job_data = Application.objects.filter(job_applied=job, status="Hired")
                for data in job_data:
                    employees_data.append(data.applicant)
        if employees_data:
            employees = CandidateSerializer(employees_data, many=True).data
        else:
            employees = []

        return Response(
            {"business": business.data, "jobs": jobs.data, "employees": employees},
            status=status.HTTP_200_OK,
        )
        # return Response({"business": business.data}, status=status.HTTP_200_OK)
    except Company.DoesNotExist:
        return Response(
            {"message": "Could not find the business"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    except Job.DoesNotExist:
        return Response(
            {"message": "No candidates found"}, status=status.HTTP_404_NOT_FOUND
        )

    except Candidate.DoesNotExist:
        return Response(
            {"message": "Could not find the account"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    except Exception as e:
        return Response({"message": f"Error: {e}"}, status=status.HTTP_400_BAD_REQUEST)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def company_profile_employees(request, business_slug):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_400_BAD_REQUEST,
        )

    try:
        business = Company.objects.get(slug=business_slug)
        business_jobs = Job.objects.filter(company_name=business)
        jobs = JobSerializer(business_jobs, many=True)
        employees_data = []
        if business_jobs.exists():
            for job in business_jobs:
                job_data = Application.objects.filter(job_applied=job, status="Hired")
                for data in job_data:
                    employees_data.append(data.applicant)
        if employees_data:
            employees = CandidateSerializer(employees_data, many=True).data
        else:
            employees = []
        return Response(
            {"jobs": jobs.data, "employees": employees}, status=status.HTTP_200_OK
        )

    except Company.DoesNotExist:
        return Response(
            {"message": "No business found"}, status=status.HTTP_404_NOT_FOUND
        )

    except Job.DoesNotExist:
        return Response(
            {"message": "No candidates found"}, status=status.HTTP_404_NOT_FOUND
        )

    except Candidate.DoesNotExist:
        return Response(
            {"message": "No candidates found"}, status=status.HTTP_404_NOT_FOUND
        )

    except Exception as e:
        return Response(
            {"an Error occurred": str(e)}, status=status.HTTP_400_BAD_REQUEST
        )
