from uuid import UUID
from django.http import Http404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from accounts.services.auth import PermissionDecorators
from accounts.views.services import verify_user
from businesses.models import *
from candidates.models import *
from jobs.models import *
from candidates.views.serializers import ApplicationSerializer
from jobs.repositories.job_repository import JobRepository
from jobs.services.job_service import JobService
from .serializers import BenefitsSerializer, JobSerializer
from rest_framework.pagination import PageNumberPagination
from .serializers import (
    JobSerializer,
    JobTypeSerializer,
    ExperienceLevelSerializer,
    WorkTypeSerializer,
    JobJobSkillsCategorySerializer,
)
from rest_framework import status
from datetime import datetime

job_service = JobService()


class JobPagination(PageNumberPagination):
    page_size = 10  # Adjust page size as needed


@api_view(["GET"])
# @permission_classes([IsAuthenticated])
def list_jobs(request):
    jobs = Job.objects.all()[:4]  # change this to pagination
    jobs_list = JobSerializer(jobs, many=True)
    return Response(jobs_list.data, status=200)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def business_jobs(request, business_slug):
    try:
        response = job_service.get_jobs_by_business(request.user, business_slug)
        if not response.success:
            return Response({"error": response.message}, status=response.status)
        serializer = JobSerializer(response.data, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    except Exception as e:
        return Response(
            {"message", "Unknown error getting jobs"},
            status=status.HTTP_400_BAD_REQUEST,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def business_single_job(request, business_id, job_slug):
    if request.method == "GET":
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:

            company = Company.objects.get(slug=business_id)
            job = Job.objects.get(slug=job_slug, company_name=company)
            serializer = JobSerializer(job)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            print(e)
            return Response(
                {"message": f"There was an error:  {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )


import time


@api_view(["GET"])
def list_job_types(request):
    job_types_list = JobType.objects.all()
    job_types = JobTypeSerializer(job_types_list, many=True)
    time.sleep(3)
    return Response(job_types.data, status=200)


@api_view(["GET"])
def list_work_types(request):
    work_types_list = WorkType.objects.all()
    work_types = WorkTypeSerializer(work_types_list, many=True)
    time.sleep(3)
    return Response(work_types.data, status=200)


@api_view(["GET"])
def list_experience_levels(request):
    experience_levels_list = ExperienceLevel.objects.all()
    experience_levels = ExperienceLevelSerializer(experience_levels_list, many=True)
    time.sleep(3)
    return Response(experience_levels.data, status=200)


@api_view(["GET"])
def job_skills_category(request):
    categories_list = SkillsCategory.objects.all()
    categories = JobJobSkillsCategorySerializer(categories_list, many=True)
    return Response(categories.data, status=status.HTTP_200_OK)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def single_job(request, slug):
    if request.method == "GET":
        # authorization
        access_token = request.headers.get("Authorization")
        user, message = verify_user(access_token)
        if user is None:
            return Response(
                {"status": "failed", "message": message},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            print("here")
            job = Job.objects.get(slug=slug)
            print("There, ", job)
            job_data = JobSerializer(job)
            candidate = Candidate.objects.get(user=user)
            user_has_applied = False
            if Application.objects.filter(
                applicant=candidate, job_applied=job
            ).exists():
                user_has_applied = True
            return Response(
                {
                    "status": "success",
                    "user_applied": user_has_applied,
                    "job": job_data.data,
                },
                status=status.HTTP_200_OK,
            )

        except Job.DoesNotExist:
            raise Http404("Job does not exist")
        except Exception as e:
            print(e)
            return Response(
                {"status": "error", "message": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


@api_view(["GET", "POST"])
def update_job_description(request, slug):
    # time.sleep(3)
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_400_BAD_REQUEST,
        )
    if request.method == "GET":
        try:
            job_data = Job.objects.get(slug=slug)
            job = JobSerializer(job_data)
            return Response(job.data, status=status.HTTP_200_OK)
        except Job.DoesNotExist:
            return Response(
                {"message": "No job  found"}, status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            print(e)
            return Response(
                {"message": "There was an error"}, status=status.HTTP_400_BAD_REQUEST
            )

    elif request.method == "POST":
        print(request.data)
        description = request.data.get("description")
        responsibilities = request.data.get("responsibilities")
        qualifications = request.data.get("qualifications")
        nice_to_have = request.data.get("nice_to_have")

        if not all([description, responsibilities, qualifications]):
            return Response(
                {"message": "Required fields are missing"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            job_data = Job.objects.get(slug=slug)
            job_data.job_description = description
            job_data.responsibilities = description
            job_data.qualifications = description
            job_data.nice_to_have = description
            job_data.save()
            job = JobSerializer(job_data)
            return Response(job.data, status=status.HTTP_200_OK)
        except Job.DoesNotExist:
            return Response(
                {"message": "No job  found"}, status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            print(e)
            return Response(
                {"message": "There was an error"}, status=status.HTTP_400_BAD_REQUEST
            )


@api_view(["GET", "POST"])
def update_job_benefits(request, slug):
    # time.sleep(3)
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_400_BAD_REQUEST,
        )
    if request.method == "GET":
        try:
            job_data = Job.objects.get(slug=slug)
            job = JobSerializer(job_data)
            benefits_data = Benefit.objects.all()
            benefits = BenefitsSerializer(benefits_data, many=True)
            return Response(
                {"job": job.data, "benefits": benefits.data}, status=status.HTTP_200_OK
            )
        except Job.DoesNotExist:
            return Response(
                {"message": "No job  found"}, status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            print(e)
            return Response(
                {"message": "There was an error"}, status=status.HTTP_400_BAD_REQUEST
            )

    elif request.method == "POST":
        benefits = request.data.get("benefits")

        if not benefits:
            return Response(
                {"message": "Benefits are missing"}, status=status.HTTP_400_BAD_REQUEST
            )
        job_benefits = []
        try:
            for benefit in benefits:
                if not Benefit.objects.filter(name=benefit["name"]).exists():
                    job_benefit = Benefit.objects.create(
                        name=benefit["name"], created_by=user
                    )
                    job_benefits.append(job_benefit.id)
                else:
                    job_benefits.append(benefit["id"])

            job_data = Job.objects.get(slug=slug)
            job_data.benefits.set(job_benefits)
            job_data.save()
            job = JobSerializer(job_data)
            return Response(job.data, status=status.HTTP_200_OK)
        except Job.DoesNotExist:
            return Response(
                {"message": "No job  found"}, status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            print(e)
            return Response(
                {"message": "There was an error"}, status=status.HTTP_400_BAD_REQUEST
            )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def job_applications(request, slug):
    if request.method == "GET":
        try:
            job = Job.objects.get(slug=slug)
            application = Application.objects.filter(job_applied=job).exclude(
                status__in=["Hired", "Rejected"]
            )
            applications = ApplicationSerializer(application, many=True)
            job_data = JobSerializer(job)
            return Response(
                {"job": job_data.data, "applications": applications.data},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response({"message": f"{e}"})
