import random
from django.http import HttpResponse
from . import dummy_data
from businesses.models import *
from jobs.models import *
from base.models import City


def businesses(request):
    business_names = dummy_data.business_names
    business_descriptions = dummy_data.descriptions
    cities = City.objects.all()
    print(business_names)
    try:
        for business_name in business_names:
            business = Company.objects.create(
                name=business_name,
                description=random.choice(business_descriptions),
                address=random.choice(cities),
                website_url="https://googel.com",
            )
            print(business.name, "is created")
        return HttpResponse("Businesses are saved")
    except Exception as e:
        print(e)
        return HttpResponse("An error happened")


def job_type(request):
    job_types = dummy_data.job_types

    try:
        for item in job_types:
            jobType = JobType.objects.create(job_type_name=item)
            print(jobType.job_type_name, "is created")

        return HttpResponse("Types are saved")
    except Exception as e:
        print(e)
        return HttpResponse("An error happened")


def work_type(request):
    work_types = dummy_data.work_types
    try:
        for item in work_types:
            jobType = WorkType.objects.create(work_type_name=item)
            print(jobType.work_type_name, "is created")

        return HttpResponse("Types are saved")
    except Exception as e:
        print(e)
        return HttpResponse("An error happened")


def experience_level(request):
    experience_levels = dummy_data.experience_levels
    try:
        for item in experience_levels:
            experience_level = ExperienceLevel.objects.create(
                experience_level_name=item
            )
            print(experience_level.experience_level_name, "is created")

        return HttpResponse("experience_levels are saved")
    except Exception as e:
        print(e)
        return HttpResponse("An error happened")


def dummy_job(request):
    """Creates dummy job entries with random data."""

    job_titles = dummy_data.job_titles
    companies = Company.objects.all()
    cities = City.objects.all()
    job_types = JobType.objects.all()
    work_types = WorkType.objects.all()
    experience_levels = ExperienceLevel.objects.all()
    job_details = dummy_data.descriptions
    min_salaries = [
        100000,
        120000,
        150000,
        170000,
        200000,
    ]  # List of salaries (300k to 100k with 70k step)
    max_salaries = [
        250000,
        270000,
        500000,
        800000,
        1000000,
    ]  # Ensure same number of elements as min_salaries

    try:
        for title in job_titles:
            job = Job.objects.create(
                job_title=title,
                company=random.choice(companies),
                location=random.choice(cities),
                job_type=random.choice(job_types),
                work_type=random.choice(work_types),
                experience_level=random.choice(experience_levels),
                min_salary=random.choice(min_salaries),
                max_salary=random.choice(max_salaries),
                job_details=random.choice(job_details),
            )
            print(f"{job.job_title} is created")  # Use f-string for cleaner output
        return HttpResponse("Dummy jobs are saved successfully")
    except Exception as e:
        print(f"An error occurred: {e}")
        return HttpResponse("An error happened while creating dummy jobs")
