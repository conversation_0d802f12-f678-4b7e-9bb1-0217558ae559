from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import api_view, permission_classes
from accounts.services.auth import PermissionDecorators
from accounts.views.services import verify_user
from businesses.models import Company
from businesses.services.dashboard import DashboardService


dashboard_service = DashboardService()


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@PermissionDecorators.is_creator(Company, lookup_field="slug", url_param_names=["business_slug"])
def business_dashboard_data(request, business_slug):
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)
    if user is None:
        return Response({"status": "failed", "message": message}, status=400)
    
    response = dashboard_service.get_dashboard_data(business_slug)
    if not response.success:
        return Response({"error": response.message}, status=status.HTTP_400_BAD_REQUEST)
    return Response(response.data, status=status.HTTP_200_OK)
